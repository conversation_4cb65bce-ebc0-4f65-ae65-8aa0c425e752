# 🤖 AI LeetCode Interviewer

A LeetCode-style coding interface with an intelligent AI interviewer powered by OpenAI GPT-3.5-turbo.

## ✨ Features

- **LeetCode-style Interface**: Authentic dark theme and layout
- **Resizable Panels**: Drag dividers to customize your workspace
- **AI Mock Interviewer**: Real-time conversation with GPT-3.5-turbo
- **Code Analysis**: AI analyzes your code and provides feedback
- **Multiple Languages**: JavaScript and Python support
- **Problem Categories**: 30+ LeetCode categories to practice
- **Custom AI Prompts**: Personalize your interviewer's behavior

## 🚀 Setup Instructions

### 1. Install Dependencies
```bash
npm install
```

### 2. Configure OpenAI API Key
1. Copy your OpenAI API key
2. Open the `.env` file in the root directory
3. Replace `your_openai_api_key_here` with your actual API key:

```env
VITE_OPENAI_API_KEY=sk-your-actual-api-key-here
```

### 3. Start the Development Server
```bash
npm run dev
```

### 4. Open in Browser
Navigate to `http://localhost:5173/`

## 🎯 How to Use

### Basic Workflow
1. **Read the Problem**: Check the Description tab for the current problem
2. **Code Your Solution**: Write JavaScript or Python in the code editor
3. **Chat with AI**: Ask questions in the Chat tab
4. **Submit & Get Feedback**: Use the bottom panel to submit your code + thoughts
5. **Iterate**: Improve based on AI feedback

### Customization
- **Resize Panels**: Drag the dividers to adjust panel sizes
- **Change AI Behavior**: Go to Settings → AI Interviewer Prompt
- **Select Categories**: Choose which LeetCode categories to practice
- **Generate New Problems**: Click "Generate New Problem" in Settings

## 🔧 Configuration Options

### Environment Variables (.env)
```env
# Required: Your OpenAI API Key
VITE_OPENAI_API_KEY=sk-your-key-here

# Optional: AI Model (default: gpt-3.5-turbo)
VITE_AI_MODEL=gpt-3.5-turbo

# Optional: Max tokens per response (default: 1000)
VITE_AI_MAX_TOKENS=1000

# Optional: AI creativity (0.0-1.0, default: 0.7)
VITE_AI_TEMPERATURE=0.7
```

## 🛡️ Security Note

This app uses `dangerouslyAllowBrowser: true` for OpenAI API calls. In production, you should:
1. Create a backend API proxy
2. Store API keys server-side
3. Implement rate limiting

## 🎨 Interface Layout

```
┌─────────────┬─────────────────────────┐
│   Sidebar   │     Code Editor         │
│   - Chat    │     (Monaco)            │
│   - Problem │                         │
│   - Settings│                         │
│             ├─────────────────────────┤
│             │  Chat & Submit Panel    │
│             │  [Code + Notes] → AI    │
└─────────────┴─────────────────────────┘
```

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

## 📝 License

MIT License - feel free to use this for your interview preparation!

## TODOS
- needs a loading indicating when generating
  - clicking generate problem creates a loading indicator over the left side panel
    - on success it populates the problem and switches to problem tab
    - on failure it shows an error toast
  - chatting with the AI also has a loading indicator in an ellipsis animation



Stretch
- update the interviewer prompt
- create draggable windows
- STYLE 
- Reduce the number of tokens used
