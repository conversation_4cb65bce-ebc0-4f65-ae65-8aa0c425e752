/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'leetcode-dark': '#1a1a1a',
        'leetcode-darker': '#0f0f0f',
        'leetcode-light': '#2d2d2d',
        'leetcode-lighter': '#3d3d3d',
        'leetcode-border': '#3d3d3d',
        'leetcode-accent': '#00b8a3',
        'leetcode-green': '#00b8a3',
        'leetcode-orange': '#ffa116',
        'leetcode-red': '#ff375f',
        'leetcode-text': '#eff1f6bf',
        'leetcode-text-secondary': '#a3a3a3',
        'leetcode-blue': '#007acc',
        'leetcode-purple': '#c678dd',
        'leetcode-yellow': '#e5c07b',
      },
      fontFamily: {
        'mono': ['Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
      },
    },
  },
  plugins: [],
}
