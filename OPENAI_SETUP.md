# OpenAI API Setup Guide

This guide will help you set up the OpenAI API to enable real AI-powered problem generation and chat features.

## 🔑 Getting Your OpenAI API Key

1. **Visit OpenAI Platform**: Go to [https://platform.openai.com](https://platform.openai.com)
2. **Sign Up/Login**: Create an account or log in to your existing account
3. **Navigate to API Keys**: Go to the API section and create a new API key
4. **Copy Your Key**: It will look like `sk-...` (starts with "sk-")

## 🛠️ Setting Up Your Environment

1. **Create/Edit .env file** in your project root:
   ```bash
   # Create the file if it doesn't exist
   touch .env
   ```

2. **Add your API key** to the `.env` file:
   ```env
   VITE_OPENAI_API_KEY=sk-your-actual-api-key-here
   VITE_AI_MODEL=gpt-4
   VITE_AI_TEMPERATURE=0.7
   VITE_AI_MAX_TOKENS=1500
   ```

3. **Restart your development server**:
   ```bash
   # Stop the current server (Ctrl+C)
   # Then restart it
   npm run dev
   ```

## 🧪 Testing Your Setup

1. **Open the app** in your browser
2. **Go to Settings tab** in the left sidebar
3. **Click "🔍 Debug API Config"** button
4. **Check the browser console** for:
   - ✅ `VITE_OPENAI_API_KEY present: true`
   - ✅ `VITE_OPENAI_API_KEY starts with sk-: true`
   - ✅ `VITE_OPENAI_API_KEY length: 51` (or similar)

## 🚀 Using AI Features

Once configured, you can:

### Generate AI Problems
1. **Go to Settings tab**
2. **Select categories** (Array, String, etc.)
3. **Choose language** (JavaScript/Python)
4. **Click "Generate New Problem"**
5. **AI will create** a custom JSON-formatted problem

### AI Chat Interviewer
1. **Submit code** in the chat panel
2. **AI will analyze** your solution
3. **Ask questions** about complexity, optimizations
4. **Get hints** and guidance

## 🔧 Troubleshooting

### Problem: Mock responses instead of AI
**Symptoms**: Console shows "Using mock problem generation (AI not configured)"
**Solutions**:
- Check your `.env` file exists and has the correct API key
- Restart the development server after adding the API key
- Verify the API key starts with `sk-`

### Problem: API errors
**Symptoms**: Console shows OpenAI API errors
**Solutions**:
- Check your OpenAI account has credits/billing set up
- Verify the API key is valid and not expired
- Check rate limits (the app has built-in rate limiting)

### Problem: Empty responses
**Symptoms**: AI generates empty or malformed responses
**Solutions**:
- Check your problem prompt in Settings
- Ensure the prompt asks for JSON format
- Try different temperature/token settings

## 💰 Cost Considerations

- **GPT-4**: More expensive but higher quality (~$0.03/1K tokens)
- **GPT-3.5-turbo**: Cheaper and faster (~$0.002/1K tokens)
- **Rate limiting**: Built-in 2-second delays between requests
- **Token optimization**: Only sends code when it changes

## 🔒 Security Notes

- **Never commit** your `.env` file to version control
- **Use environment variables** for production deployment
- **Consider using a backend proxy** for production (removes `dangerouslyAllowBrowser`)

## 📝 Example .env File

```env
# OpenAI Configuration
VITE_OPENAI_API_KEY=sk-proj-abcd1234...your-key-here
VITE_AI_MODEL=gpt-4
VITE_AI_TEMPERATURE=0.7
VITE_AI_MAX_TOKENS=1500

# Optional: Custom settings
VITE_AI_RATE_LIMIT=2000
```

## ✅ Success Indicators

When everything is working correctly, you'll see:

1. **Console logs**: 
   ```
   ✅ OpenAI service initialized successfully!
   🤖 AI responses will be powered by: gpt-4
   ```

2. **Problem generation**:
   ```
   🧩 Generating new problem for categories: ['Array'] language: javascript
   🔧 AI Service configured: true
   🚀 Sending request to OpenAI...
   ✅ Generated new problem!
   ```

3. **Real AI responses** instead of mock responses

Happy coding! 🎯
