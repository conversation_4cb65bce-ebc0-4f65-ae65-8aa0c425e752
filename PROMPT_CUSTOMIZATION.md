# AI Prompt Customization Guide

This guide explains how to customize the default AI prompts and configurations for your LeetCode-style interview practice app.

## 📁 Configuration File Location

All default prompts and AI configurations are stored in:
```
src/config/prompts.js
```

## 🎯 Available Configurations

### 1. Problem Generation Prompt (`DEFAULT_PROBLEM_PROMPT`)

This prompt is used when generating new algorithmic problems. It supports the following placeholder:
- `{CATEGORIES}` - Automatically replaced with selected categories

**Default behavior:**
- Generates LeetCode-style problems
- Includes examples, constraints, and test cases
- Focuses on interview-appropriate difficulty

**Customization example:**
```javascript
export const DEFAULT_PROBLEM_PROMPT = `Create a challenging algorithmic problem for categories: {CATEGORIES}.

Requirements:
- Make it harder than typical LeetCode medium
- Include multiple solution approaches
- Add tricky edge cases
- Provide detailed complexity analysis

Focus on problems that test deep algorithmic thinking.`
```

### 2. Interviewer Behavior Prompt (`DEFAULT_INTERVIEWER_PROMPT`)

This prompt defines how the AI interviewer behaves during chat interactions.

**Default behavior:**
- Encouraging but challenging
- Asks about complexity and optimizations
- Provides hints without giving away solutions

**Customization example:**
```javascript
export const DEFAULT_INTERVIEWER_PROMPT = `You are a senior software engineer conducting a technical interview. Be more direct and challenging.

Style:
- Ask tough follow-up questions immediately
- Push for optimal solutions from the start
- Focus heavily on system design implications
- Challenge assumptions and edge case handling

Be professional but demanding, as if this were a FAANG interview.`
```

### 3. AI Model Configurations

#### Problem Generation Config (`DEFAULT_PROBLEM_CONFIG`)
```javascript
export const DEFAULT_PROBLEM_CONFIG = {
  model: 'gpt-4',        // AI model for problem generation
  temperature: 0.7,      // Creativity level (0.0-2.0)
  maxTokens: 1500       // Response length limit
}
```

#### Interviewer Config (`DEFAULT_INTERVIEWER_CONFIG`)
```javascript
export const DEFAULT_INTERVIEWER_CONFIG = {
  model: 'gpt-4',        // AI model for conversations
  temperature: 0.8,      // Conversation style (0.0-2.0)
  maxTokens: 1000       // Response length limit
}
```

## 🔧 Temperature Guidelines

- **0.0-0.3**: Very focused, deterministic responses
- **0.4-0.7**: Balanced creativity and consistency
- **0.8-1.2**: More creative and varied responses
- **1.3-2.0**: Highly creative, potentially unpredictable

## 📝 Customization Examples

### For Beginner-Friendly Practice
```javascript
export const DEFAULT_INTERVIEWER_PROMPT = `You are a patient mentor helping someone learn algorithms. Be very encouraging and provide detailed explanations.

Guidelines:
- Break down complex concepts into simple steps
- Celebrate small wins and progress
- Provide multiple hints before giving solutions
- Focus on learning rather than evaluation
- Use analogies and simple examples`
```

### For Advanced/Competitive Programming
```javascript
export const DEFAULT_PROBLEM_PROMPT = `Generate an advanced competitive programming problem for categories: {CATEGORIES}.

Requirements:
- Difficulty level: Hard to Expert
- Multiple algorithmic concepts combined
- Tight time/space constraints
- Requires advanced data structures
- Include mathematical insights or proofs

Target audience: Competitive programmers preparing for contests.`
```

### For System Design Focus
```javascript
export const DEFAULT_INTERVIEWER_PROMPT = `You are a system design expert conducting a technical interview. Focus on scalability and architecture.

Guidelines:
- Always ask about system design implications
- Discuss trade-offs between different approaches
- Ask about handling large scale (millions of users)
- Focus on distributed systems concepts
- Challenge on performance and reliability`
```

## 🚀 How to Apply Changes

1. **Edit the prompts file**: Modify `src/config/prompts.js`
2. **Save the file**: Changes are automatically hot-reloaded
3. **Test in app**: Go to Settings tab to see your changes
4. **Fine-tune**: Adjust temperature and token settings as needed

## 💡 Pro Tips

1. **Use placeholders**: `{CATEGORIES}` in problem prompts gets replaced with selected categories
2. **Test different temperatures**: Higher values = more creative, lower = more consistent
3. **Adjust token limits**: More tokens = longer responses, fewer = more concise
4. **Model selection**: GPT-4 is more capable but slower, GPT-3.5 is faster but less sophisticated
5. **Separate configs**: Problem generation and interviewer can use different models/settings

## 🔄 Reverting to Defaults

If you want to revert to the original prompts, you can restore the default values from this guide or check the git history for the original `prompts.js` file.

## 📊 Configuration Presets

The file also includes helpful presets you can reference:

```javascript
// Temperature presets
TEMPERATURE_PRESETS = {
  creative: 1.2,
  balanced: 0.7,
  focused: 0.3,
  deterministic: 0.1
}

// Token limit presets
TOKEN_PRESETS = {
  short: 500,
  medium: 1000,
  long: 1500,
  extended: 2000
}
```

Happy customizing! 🎯
