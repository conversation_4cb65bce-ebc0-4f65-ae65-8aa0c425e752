@mixin absoluteFill {
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    position: absolute;
}

@mixin flexFix {
    display: flex;
    flex-basis: 0px;
    min-width: 0;
    min-height: 0;
}

@mixin baseMixin {
    /*
        base classes
    */
    .flexlayout {
        &__layout {
            @include absoluteFill;
            display: flex;
            background-color: var(--color-background);

            &_overlay {
                @include absoluteFill;
                z-index: 1000;
            }

            &_tab_stamps {
                position: absolute;
                top: -10000px; // offscreen
                z-index: 100;
                display: flex;
                flex-direction: column;
                align-items: start;
            }

            &_moveables {
                visibility: hidden;
                position: absolute;
                width: 100px; // size is needed to tabs have something to draw into
                height: 100px;
                top: -20000px; // offscreen
            }

            &_main {
                @include flexFix;
                flex-grow: 1;
                position: relative;
            }
        }

        &__layout_border_container {
            @include flexFix;
            flex-grow: 1;
        }

        &__layout_border_container_inner {
            @include flexFix;
            flex-grow: 1;
        }

        &__splitter {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--color-splitter);
            touch-action: none;
            z-index: 10;

            @media (hover: hover) {
                &:hover {
                    background-color: var(--color-splitter-hover);
                    transition: background-color ease-in .1s;
                    transition-delay: 0.05s;
                    border-radius: 5px;
                }
            }

            &_border {
            }

            &_drag {
                position: absolute;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                border-radius: 5px;
                background-color: var(--color-splitter-drag);
            }

            &_handle {
                background-color: #ccc;
                border-radius: 3px;

                &_horz {
                    width: 3px;
                    height: 30px;
                }

                &_vert {
                    width: 30px;
                    height: 3px;
                }
            }

            &_extra {
                touch-action: none;
                background-color: transparent;
            }
        }

        &__outline_rect {
            position: absolute;
            pointer-events: none;
            box-sizing: border-box;
            border: 2px solid var(--color-drag1);
            background: var(--color-drag1-background);
            border-radius: 5px;
            z-index: 1000;

            &_edge {
                pointer-events: none;
                border: 2px solid var(--color-drag2);
                background: var(--color-drag2-background);
                border-radius: 5px;
                z-index: 1000;
                box-sizing: border-box;
            }
        }

        &__edge_rect {
            position: absolute;
            z-index: 1000;
            background-color: var(--color-edge-marker);
            pointer-events: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        &__drag_rect {
            color: var(--color-drag-rect);
            background-color: var(--color-drag-rect-background);
            border: 2px solid var(--color-drag-rect-border);
            border-radius: 5px;
            box-sizing: border-box;
            // text-align: center;

            display: flex;
            justify-content: center;
            flex-direction: column;
            overflow: hidden;
            padding: 0.3em .8em;
            word-wrap: break-word;
            font-size: var(--font-size);
            font-family: var(--font-family);
        }

        &__row {
            @include flexFix;
            position: relative;
            box-sizing: border-box;
            overflow: hidden;
        }

        &__tabset {
            display: flex;
            flex-direction: column;
            position: relative;
            background-color: var(--color-tabset-background);
            box-sizing: border-box;
            font-family: var(--font-family);
            overflow: hidden;
            flex-grow: 1;

            &_container {
                @include flexFix;
                flex-direction: column;
                overflow: hidden;
                flex-grow: 1;
            }

            &_tab_divider {
                width: 4px;
            }

            &_content {
                @include flexFix;
                flex-grow: 1;
                box-sizing: border-box;
                position: relative;
            }

            &_leading {
                display:flex;
            }

            &_header {
                // tabset header bar
                display: flex;
                align-items: center;
                padding: 3px 3px 3px 5px;
                box-sizing: border-box;
                border-bottom: 1px solid var(--color-tabset-divider-line);
                color: var(--color-tabset-header);
                background-color: var(--color-tabset-header-background);
                font-size: var(--font-size);

                &_content {
                    flex-grow: 1;
                }
            }

            &_tabbar {
                &_outer {
                    // tabset tabbar outer
                    box-sizing: border-box;
                    background-color: var(--color-tabset-background);
                    overflow: hidden;
                    display: flex;
                    font-size: var(--font-size);
                }

                &_outer_top {
                    padding: 0px 2px 0px 2px;
                    border-bottom: 1px solid var(--color-tabset-divider-line);
                }

                &_outer_bottom {
                    padding: 0px 2px 0px 2px;
                    border-top: 1px solid var(--color-tabset-divider-line);
                }

                &_inner {
                    // tabset tabbar inner
                    position: relative;
                    box-sizing: border-box;
                    display: flex;
                    flex-grow: 1;
                    scrollbar-width: none; // firefox

                    &::-webkit-scrollbar {
                        display: none;
                    }

                    &_tab_container {
                        position: relative;
                        display: flex;
                        padding-left: 4px;
                        padding-right: 4px;
                        box-sizing: border-box;
                        white-space: nowrap;

                        &_top {
                            border-top: 2px solid transparent;
                        }

                        &_bottom {
                            border-bottom: 2px solid transparent;
                        }
                    }
                }
            }

            &-selected {
                background-color: var(--color-tabset-background-selected);
            }

            &-maximized {
                background-color: var(--color-tabset-background-maximized);
            }
        }

        &__tab_button_stamp {
            display: inline-flex;
            align-items: center;
            gap: 0.3em;
            white-space: nowrap;
            box-sizing: border-box;
        }

        &__tab {
            overflow: hidden;
            box-sizing: border-box;
            background-color: var(--color-tab-content);
            color: var(--color-text);
            position: relative;

            &_moveable {
                position: relative;
                height: 100%;
                min-width: 1px;
                min-height: 1px;
                overflow: auto;
                box-sizing: border-box;
            }

            &_overlay {
                z-index: 20;
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #0000003d;
            }

            &_button {
                display: flex;
                gap: 0.3em;
                align-items: center;
                box-sizing: border-box;
                padding: 3px 0.5em; // if you change top or bottom update the tabset_sizer above

                cursor: pointer;

                &_stretch {
                    background-color: transparent;
                    color: var(--color-tab-selected);
                    width: 100%;
                    padding: 3px 0em;
                    text-wrap: nowrap;
                    display: flex;
                    gap: 0.3em;
                    align-items: center;
                    box-sizing: border-box;
                    cursor: pointer;

                    @media (hover: hover) {
                        &:hover {
                            color: var(--color-tab-selected);
                        }
                    }
                }

                &--selected {
                    background-color: var(--color-tab-selected-background);
                    color: var(--color-tab-selected);
                }

                @media (hover: hover) {
                    &:hover {
                        color: var(--color-tab-selected);
                    }
                }

                &--unselected {
                    background-color: var(--color-tab-unselected-background);
                    color: var(--color-tab-unselected);
                }

                &_top {
                }

                &_bottom {
                }

                &_leading {
                    display: flex;
                }

                &_content {
                    display: flex;
                    text-wrap: nowrap;
                }

                &_textbox {
                    border: none;
                    font-family: var(--font-family);
                    font-size: var(--font-size);
                    color: var(--color-tab-textbox);
                    background-color: var(--color-tab-textbox-background);
                    border: 1px inset var(--color-1);
                    border-radius: 3px;
                    width: 10em;
                }

                &_textbox:focus {
                    outline: none;
                }

                &_trailing {
                    display: flex;
                    visibility: hidden;
                    border-radius: 4px;

                    &:hover {
                        background-color: var(--color-toolbar-button-hover);
                    }
                }

                @media (pointer: coarse) {
                    &_trailing {}
                }

                @media (hover: hover) {
                    &:hover &_trailing {
                        visibility: visible;
                    }
                }

                &--selected &_trailing {
                    visibility: visible;
                }

                &_overflow {
                    display: flex;
                    align-items: center;
                    border: none;
                    color: var(--color-overflow);
                    font-size: inherit;
                    background-color: transparent;
                    width: 2em;
                    overflow: hidden;
                }
            }

            &_toolbar {
                display: flex;
                align-items: center;
                gap: .3em;
                padding-left: .5em;
                padding-right: .3em;

                &_icon {
                    border: none;
                    outline: none;
                    font-size: inherit;
                    margin: 0px;
                    background-color: transparent;
                    padding: 1px;
                }

                &_button {
                    border: none;
                    outline: none;
                    font-size: inherit;
                    margin: 0px;
                    background-color: transparent;
                    border-radius: 4px;
                    padding: 1px;

                    @media (hover: hover) {
                        &:hover {
                            background-color: var(--color-toolbar-button-hover);
                        }
                    }

                    &-min {}

                    &-max {}

                    &-float {}

                    &-close {}
                }

                &_sticky_buttons_container {
                    display: flex;
                    gap: 0.3em;
                    padding-left: 5px;
                    align-items: center;
                }
            }
        }

        &__border {
            box-sizing: border-box;
            overflow: hidden;
            display: flex;
            font-size: var(--font-size);
            font-family: var(--font-family);
            color: var(--color-border);
            background-color: var(--color-border-background);

            &_tab_contents {
                box-sizing: border-box;
                overflow: hidden;
                background-color: var(--color-border-tab-content);
            }
            
            &_leading {
                display:flex;
            }

            &_top {
                border-bottom: 1px solid var(--color-border-divider-line);
                align-items: center;
            }

            &_bottom {
                border-top: 1px solid var(--color-border-divider-line);
                align-items: center;
            }

            &_left {
                border-right: 1px solid var(--color-border-divider-line);
                align-content: center;
                flex-direction: column;
            }

            &_right {
                border-left: 1px solid var(--color-border-divider-line);
                align-content: center;
                flex-direction: column;
            }

            &_inner {
                position: relative;
                box-sizing: border-box;
                align-items: center;
                display: flex;
                flex-grow: 1;
                scrollbar-width: none; // firefox

                &::-webkit-scrollbar {
                    display: none;
                }

                &_tab_container {
                    white-space: nowrap;
                    display: flex;
                    padding-left: 2px;
                    padding-right: 2px;
                    box-sizing: border-box;
                    position: absolute;

                    &_right {
                        transform-origin: top left;
                        transform: rotate(90deg);
                    }

                    &_left {
                        flex-direction: row-reverse;
                        transform-origin: top right;
                        transform: rotate(-90deg);
                    }
                }
            }

            &_tab_divider {
                width: 4px;
            }

            &_button {
                display: flex;
                gap: 0.3em;
                align-items: center;
                cursor: pointer;
                padding: 3px 0.5em;
                margin: 2px 0px;
                box-sizing: border-box;
                white-space: nowrap;

                &--selected {
                    background-color: var(--color-border-tab-selected-background);
                    color: var(--color-border-tab-selected);
                }

                @media (hover: hover) {
                    &:hover {
                        // background-color: var(--color-border-tab-selected-background);
                        color: var(--color-border-tab-selected);
                    }
                }

                &--unselected {
                    background-color: var(--color-border-tab-unselected-background);
                    color: var(--color-border-tab-unselected);
                }

                &_leading {
                    display: flex;
                }

                &_content {
                    display: flex;
                }

                &_trailing {
                    display: flex;
                    border-radius: 4px;
                    visibility: hidden;

                    &:hover {
                    }
                }

                @media (pointer: coarse) {
                    &_trailing {}
                }

                @media (hover: hover) {
                    &:hover &_trailing {
                        visibility: visible;
                    }
                }

                &--selected &_trailing {
                    visibility: visible;
                }
            }

            &_toolbar {
                display: flex;
                gap: .3em;
                align-items: center;

                &_left,
                &_right {
                    flex-direction: column;
                    padding-top: .5em;
                    padding-bottom: .3em;
                }

                &_top,
                &_bottom {
                    padding-left: .5em;
                    padding-right: .3em;
                }

                &_button {
                    border: none;
                    outline: none;
                    font-size: inherit;
                    background-color: transparent;
                    border-radius: 4px;
                    padding: 1px;

                    @media (hover: hover) {
                        &:hover {
                            background-color: var(--color-toolbar-button-hover);
                        }
                    }

                    &-float {}

                    &_overflow {
                        display: flex;
                        align-items: center;
                        border: none;
                        color: var(--color-overflow);
                        font-size: inherit;
                        background-color: transparent;
                        width: 1.5em;
                    }

                    &_overflow_top,
                    &_overflow_bottom {}

                    &_overflow_right,
                    &_overflow_left {}
                }
            }
        }

        &__popup_menu {
            font-size: var(--font-size);
            font-family: var(--font-family);

            &_item {
                padding: 2px 0.5em;
                white-space: nowrap;
                cursor: pointer;
                border-radius: 2px;

                &--selected {
                    font-weight: 500;
                    background-color: var(--color-tab-selected-background);
                    color: var(--color-tab-selected);
                }
            }

            @media (hover: hover) {
                &_item:hover {
                    background-color: var(--color-6);
                }
            }

            &_container {
                box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.15);
                border: 1px solid var(--color-popup-border);
                color: var(--color-popup-unselected);
                background: var(--color-popup-unselected-background);
                border-radius: 3px;
                position: absolute;
                z-index: 1000;
                max-height: 50%;
                min-width: 100px;
                overflow: auto;
                padding: 2px;
            }
        }

        &__floating_window {
            _body {
                height: 100%;
            }

            &_content {
                @include absoluteFill;
            }
        }

        &__error_boundary_container {
            @include absoluteFill;
            display: flex;
            justify-content: center;
        }

        &__error_boundary_content {
            display: flex;
            align-items: center;
        }

        // This class is used to measure the size for the border bars,
        // only the height is used, the padding should match the vertical spacing (padding & margins)
        // that have been used in the above classes

        &__border_sizer {
            position: absolute;
            top: -30000px; // offscreen
            padding-top: 6px;
            padding-bottom: 5px;
            font-size: var(--font-size);
            font-family: var(--font-family);
        }

        &__mini_scrollbar {
            position: absolute;
            background-color: var(--color-mini-scroll-indicator);
            border-radius: 5px;
            width: var(--size-mini-scroll-indicator);
            height: var(--size-mini-scroll-indicator);
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
        }

        &__mini_scrollbar:hover {
            background-color: var(--color-mini-scroll-indicator-hovered);
            transition: background-color 0.3s ease-in-out;
        }

        &__mini_scrollbar_container {
            position: relative;
            display: flex;
            flex-grow: 1;
            overflow: hidden;

            @media (hover: hover) {
                &:hover .flexlayout__mini_scrollbar {
                    opacity: 1;
                    visibility: visible;
                }
            }
        }
    }

/* ======================== End of Base Classes =========================== */

}