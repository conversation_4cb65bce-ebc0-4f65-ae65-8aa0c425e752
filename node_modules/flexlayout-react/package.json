{"name": "flexlayout-react", "version": "0.8.17", "description": "A multi-tab docking layout manager", "author": "Caplin Systems Ltd", "repository": {"type": "git", "url": "git+https://github.com/caplin/FlexLayout.git"}, "license": "ISC", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./types/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./types/index.d.ts"}, "./style/*": "./style/*"}, "files": ["dist/", "types/", "style/"], "keywords": ["react", "layout", "dock", "popout", "tabs", "tabset", "splitter", "drag", "drop", "reactjs", "flexlayout", "flex layout", "layout manager", "drag and drop", "split view", "docking library", "docking layout"], "scripts": {"dev": "vite", "preview": "vite preview", "build": "npm run build:clean && npm run build:demo && npm run css && npm run build:lib && npm run build:types && npm run doc", "build:clean": "rimraf demo/dist dist/ types/ typedoc/", "build:demo": "vite build", "build:types": "tsc -p tsconfig-types.json", "build:lib": "vite build --config vite.config.lib.ts", "test": "vitest", "playwright": "playwright test --ui", "lint": "eslint src/*", "doc": "typedoc --out typedoc --exclude \"**/demo/**/*.tsx\" --excludeInternal --disableSources --excludePrivate --excludeProtected --readme none ./src", "css": "sass style:style"}, "eslintConfig": {"extends": "react-app"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "devDependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@eslint/js": "^9.24.0", "@mui/material": "^7.0.2", "@mui/x-data-grid": "^7.28.3", "@playwright/test": "^1.51.1", "@types/node": "^22.14.1", "@types/prismjs": "^1.26.5", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.0", "ag-grid-community": "^33.2.3", "ag-grid-react": "^33.2.3", "chart.js": "^4.4.9", "eslint": "^9.24.0", "eslint-plugin-react": "^7.37.5", "globals": "^16.0.0", "ol": "^10.5.0", "prettier": "^3.5.3", "prismjs": "^1.30.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "rimraf": "^6.0.1", "sass": "^1.86.3", "styled-components": "^6.1.17", "typedoc": "^0.28.2", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.0", "vitest": "^3.1.1"}}