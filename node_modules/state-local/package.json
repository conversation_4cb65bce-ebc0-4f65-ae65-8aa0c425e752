{"name": "state-local", "version": "1.0.7", "description": "Tiny, simple, and robust technique for defining and acting with local states", "main": "lib/cjs/state-local.js", "module": "lib/es/state-local.js", "unpkg": "lib/umd/state-local.min.js", "jsdelivr": "lib/umd/state-local.min.js", "types": "lib/types.d.ts", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/suren-atoyan/state-local"}, "bugs": {"url": "https://github.com/suren-atoyan/state-local/issues"}, "homepage": "https://github.com/suren-atoyan/state-local#readme", "keywords": ["state", "state management", "local state"], "scripts": {"test": "jest", "test-watch": "npm run build && jest --watch", "coverage": "jest --collect-coverage", "lint": "npx eslint src", "prepublish": "npm test && npm run lint && npm run build", "build": "rollup -c && cp ./src/types.d.ts ./lib/"}, "babel": {"presets": ["@babel/preset-env"]}, "husky": {"hooks": {"pre-commit": "npm test && npm run lint"}}, "devDependencies": {"@babel/core": "^7.11.0", "@babel/preset-env": "^7.11.0", "@rollup/plugin-babel": "^5.2.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.1", "@rollup/plugin-replace": "^2.3.4", "babel-jest": "^26.2.2", "babel-loader": "^8.1.0", "eslint": "^7.6.0", "husky": "^4.2.5", "jest": "^26.2.2", "rollup": "^2.35.1", "rollup-plugin-terser": "^7.0.2"}, "dependencies": {}}