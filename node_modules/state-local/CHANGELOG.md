### Versions

## 1.0.7
###### *Jan 7, 2021*

- package: add jsdelivr source path

## 1.0.6
###### *Jan 3, 2021*

- use default export in the entry file
- fix library import in `spec.js`

## 1.0.5
###### *Jan 3, 2021*

- replace `webpack` with `rollup`
- add cjs/es bundles

## 1.0.4
###### *Nov 15, 2020*

- revert "corrected moudles"

## 1.0.3
###### *Nov 15, 2020*

- remove `src` from `.npmignore` and refer it to `package.module`

## 1.0.2
###### *Nov 14, 2020*

- fix `module` path in package.json

## 1.0.1
###### *Aug 6, 2020*

- add default export into the index point

## 1.0.0
###### *Aug 6, 2020*

🎉 First stable version of the library
