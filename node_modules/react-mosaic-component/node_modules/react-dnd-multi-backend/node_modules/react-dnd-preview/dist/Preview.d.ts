import type { ReactNode } from 'react';
import { type PreviewState } from './Context';
import type { Point, PreviewPlacement } from './offsets';
export type PreviewGenerator<T = unknown, El extends Element = Element> = (state: PreviewState<T, El>) => JSX.Element;
export type PreviewProps<T = unknown, El extends Element = Element> = ({
    children: PreviewGenerator<T, El> | ReactNode;
} | {
    generator: PreviewGenerator<T, El>;
}) & {
    placement?: PreviewPlacement;
    padding?: Point;
};
export declare const Preview: <T = unknown, El extends Element = Element>(props: PreviewProps<T, El>) => JSX.Element | null;
