import{createContext as u}from"react";var i=u(void 0);import{useRef as y}from"react";import{useDragLayer as w}from"react-dnd";var l=(e,t)=>({x:e.x-t.x,y:e.y-t.y}),m=(e,t)=>({x:e.x+t.x,y:e.y+t.y}),P=e=>{let t=e.getInitialClientOffset(),n=e.getInitialSourceClientOffset();return t===null||n===null?{x:0,y:0}:l(t,n)},f=(e,t)=>{switch(e){case"left":case"top-start":case"bottom-start":return 0;case"right":case"top-end":case"bottom-end":return t.width;default:return t.width/2}},d=(e,t)=>{switch(e){case"top":case"top-start":case"top-end":return 0;case"bottom":case"bottom-start":case"bottom-end":return t.height;default:return t.height/2}},c=(e,t,n="center",r={x:0,y:0})=>{let o=e.getClientOffset();if(o===null)return null;if(!t.current||!t.current.getBoundingClientRect)return l(o,P(e));let s=t.current.getBoundingClientRect(),p={x:f(n,s),y:d(n,s)};return m(l(o,p),r)};var x=e=>{let t=`translate(${e.x.toFixed(1)}px, ${e.y.toFixed(1)}px)`;return{pointerEvents:"none",position:"fixed",top:0,left:0,transform:t,WebkitTransform:t}},a=e=>{let t=y(null),n=w(r=>({currentOffset:c(r,t,e?.placement,e?.padding),isDragging:r.isDragging(),itemType:r.getItemType(),item:r.getItem(),monitor:r}));return!n.isDragging||n.currentOffset===null?{display:!1}:{display:!0,itemType:n.itemType,item:n.item,style:x(n.currentOffset),monitor:n.monitor,ref:t}};import{jsx as E}from"react/jsx-runtime";var v=e=>{let t=a({placement:e.placement,padding:e.padding});if(!t.display)return null;let{display:n,...r}=t,o;return"children"in e?typeof e.children=="function"?o=e.children(r):o=e.children:o=e.generator(r),E(i.Provider,{value:r,children:o})};export{i as Context,v as Preview,a as usePreview};
