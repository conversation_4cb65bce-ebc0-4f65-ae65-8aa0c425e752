import type { RefObject } from 'react';
import type { DragLayerMonitor } from 'react-dnd';
export type Point = {
    x: number;
    y: number;
};
export type PreviewPlacement = 'center' | 'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'right';
export declare const calculatePointerPosition: (monitor: DragLayerMonitor, childRef: RefObject<Element>, placement?: PreviewPlacement, padding?: Point) => Point | null;
