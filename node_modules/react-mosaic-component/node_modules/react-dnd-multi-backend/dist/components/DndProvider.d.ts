import { type MultiBackendOptions } from 'dnd-multi-backend';
import { type ReactNode } from 'react';
export declare const PreviewPortalContext: import("react").Context<Element | null>;
export type DndProviderProps = {
    context?: any;
    options: MultiBackendOptions;
    children?: ReactNode;
    debugMode?: boolean;
    portal?: Element;
};
export declare const DndProvider: ({ portal, ...props }: DndProviderProps) => JSX.Element;
