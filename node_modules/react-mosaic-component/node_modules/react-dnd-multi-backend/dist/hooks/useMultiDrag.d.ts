import { type ConnectDragPreview, type ConnectDragSource, type DragSourceHookSpec } from 'react-dnd';
export type useMultiDragOneState<Props> = [Props, ConnectDragSource, ConnectDragPreview];
export type useMultiDragState<Props> = [useMultiDragOneState<Props>, Record<string, useMultiDragOneState<Props>>];
export declare const useMultiDrag: <Drag, Drop, Props>(spec: DragSourceHookSpec<Drag, Drop, Props>) => useMultiDragState<Props>;
