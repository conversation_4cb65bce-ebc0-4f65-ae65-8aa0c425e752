@import (reference) './mixins';

@split-size: 6px;

.mosaic {
  height: 100%;
  width: 100%;

  &,
  > * {
    box-sizing: border-box;
  }

  .mosaic-zero-state {
    @padding: @split-size;
    .absolute-fill(@padding, @padding, @padding, @padding);
    width: auto;
    height: auto;
    z-index: 1;
  }
}

.mosaic-root {
  @size: @split-size / 2;
  .absolute-fill(@size, @size, @size, @size);
}

.mosaic-split {
  position: absolute;
  z-index: 1;
  touch-action: none;

  &:hover {
    background: black;
  }

  .mosaic-split-line {
    position: absolute;
  }

  &.-row {
    margin-left: -@split-size / 2;
    width: @split-size;
    cursor: ew-resize;

    .mosaic-split-line {
      top: 0;
      bottom: 0;
      left: @split-size / 2;
      right: @split-size / 2;
    }
  }

  &.-column {
    margin-top: -@split-size / 2;
    height: @split-size;
    cursor: ns-resize;

    .mosaic-split-line {
      top: @split-size / 2;
      bottom: @split-size / 2;
      left: 0;
      right: 0;
    }
  }
}

.mosaic-tile {
  position: absolute;
  margin: @split-size / 2;

  > * {
    height: 100%;
    width: 100%;
  }
}

.split-percentages(@split-amount) {
  @amount: ~'calc(100% - ' @split-amount ~')';
  &.left {
    right: @amount;
  }
  &.right {
    left: @amount;
  }
  &.bottom {
    top: @amount;
  }
  &.top {
    bottom: @amount;
  }
}

.mosaic-drop-target {
  position: relative;

  &.drop-target-hover .drop-target-container {
    display: block;
  }

  &.mosaic > .drop-target-container .drop-target {
    .split-percentages(10px);
  }

  .drop-target-container {
    .absolute-fill();
    display: none;

    &.-dragging {
      display: block;
    }

    .drop-target {
      .absolute-fill();
      .split-percentages(30%);
      background: fade(black, 20%);
      border: 2px solid black;
      opacity: 0;
      z-index: 5;

      &.drop-target-hover {
        opacity: 1;
        .split-percentages(50%);
      }
    }
  }
}
