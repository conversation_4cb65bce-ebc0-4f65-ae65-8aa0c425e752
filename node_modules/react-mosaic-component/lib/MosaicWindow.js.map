{"version": 3, "file": "MosaicWindow.js", "sourceRoot": "", "sources": ["../src/MosaicWindow.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,0DAAoC;AACpC,uDAAiC;AACjC,+DAAyC;AACzC,2DAAqC;AACrC,2DAAqC;AACrC,yDAAmC;AACnC,6CAA0C;AAC1C,uCAOmB;AAEnB,2EAAqH;AACrH,iDAAgD;AAChD,+CAAoE;AACpE,iDAA2F;AAC3F,uDAAsD;AACtD,iCAA+F;AAC/F,sDAA2D;AAC3D,0DAAsE;AACtE,8DAA6D;AAuC7D;IAA+D,wCAG9D;IAHD;QAAA,qEAiMC;QA1KC,WAAK,GAA8B;YACjC,sBAAsB,EAAE,KAAK;SAC9B,CAAC;QAEM,iBAAW,GAAuB,IAAI,CAAC;QAyGvC,sBAAgB,GAAG,UAAC,QAAkC;YACpD,IAAA,IAAI,GAAK,KAAI,CAAC,KAAK,KAAf,CAAgB;YAE5B,OAAO,8BAAC,mCAAgB,IAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,GAAI,CAAC;QAC7E,CAAC,CAAC;QAQM,WAAK,GAAG;YAAC,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAC7B,KAAI,CAAC,eAAe,EAAE,CAAC;YACjB,IAAA,KAAuB,KAAI,CAAC,KAAK,EAA/B,UAAU,gBAAA,EAAE,IAAI,UAAe,CAAC;YAChC,IAAA,aAAa,GAAK,KAAI,CAAC,OAAO,cAAjB,CAAkB;YACvC,IAAM,IAAI,GAAG,aAAa,CAAC,OAAO,EAAE,CAAC;YAErC,IAAM,SAAS,GACb,KAAI,CAAC,WAAY,CAAC,WAAW,GAAG,KAAI,CAAC,WAAY,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC;YAEpF,OAAO,OAAO,CAAC,OAAO,CAAC,UAAW,eAAI,IAAI,EAAE,CAAC,IAAI,CAAC,UAAC,MAAM;gBACvD,OAAA,aAAa,CAAC,WAAW,CAAC,IAAI,EAAE;oBAC9B,SAAS,WAAA;oBACT,MAAM,QAAA;oBACN,KAAK,EAAE,IAAA,8CAA4B,EAAC,IAAI,EAAE,IAAI,CAAC;iBAChD,CAAC;YAJF,CAIE,CACH,CAAC;QACJ,CAAC,CAAC;QAEM,UAAI,GAAG;YAAC,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAC5B,KAAI,CAAC,eAAe,EAAE,CAAC;YACf,IAAA,aAAa,GAAK,KAAI,CAAC,OAAO,cAAjB,CAAkB;YACjC,IAAA,KAAuB,KAAI,CAAC,KAAK,EAA/B,UAAU,gBAAA,EAAE,IAAI,UAAe,CAAC;YACxC,OAAO,OAAO,CAAC,OAAO,CAAC,UAAW,eAAI,IAAI,EAAE,CAAC,IAAI,CAAC,UAAC,IAAI,IAAK,OAAA,aAAa,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,EAArC,CAAqC,CAAC,CAAC;QACrG,CAAC,CAAC;QAEM,+BAAyB,GAAG,UAAC,4BAAgD;;YACnF,IAAM,sBAAsB,GAC1B,4BAA4B,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,4BAA4B,CAAC;YAChH,KAAI,CAAC,QAAQ,CAAC,EAAE,sBAAsB,wBAAA,EAAE,CAAC,CAAC;YAC1C,MAAA,MAAA,KAAI,CAAC,KAAK,EAAC,0BAA0B,mDAAG,sBAAsB,CAAC,CAAC;QAClE,CAAC,CAAC;QAEM,aAAO,GAAG,cAAM,OAAA,KAAI,CAAC,KAAK,CAAC,IAAI,EAAf,CAAe,CAAC;QAEhC,uBAAiB,GAAG,UAAC,iBAA0C;YAC7D,IAAA,iBAAiB,GAAK,KAAI,CAAC,KAAK,kBAAf,CAAgB;YACzC,OAAO,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;QAC9C,CAAC,CAAC;QAEe,kBAAY,GAAwB;YACnD,kBAAkB,EAAE,KAAI,CAAC,OAAO,CAAC,kBAAkB;YACnD,mBAAmB,EAAE;gBACnB,KAAK,EAAE,KAAI,CAAC,KAAK;gBACjB,cAAc,EAAE,KAAI,CAAC,IAAI;gBACzB,yBAAyB,EAAE,KAAI,CAAC,yBAAyB;gBACzD,OAAO,EAAE,KAAI,CAAC,OAAO;gBACrB,iBAAiB,EAAE,KAAI,CAAC,iBAAiB;aAC1C;SACF,CAAC;;IACJ,CAAC;IApKC,qCAAM,GAAN;QAAA,iBAyCC;QAxCO,IAAA,KASF,IAAI,CAAC,KAAK,EARZ,SAAS,eAAA,EACT,MAAM,YAAA,EACN,aAAa,mBAAA,EACb,kBAAkB,wBAAA,EAClB,iBAAiB,uBAAA,EACjB,kBAAkB,wBAAA,EAClB,eAAe,qBAAA,EACf,gCAAgC,sCACpB,CAAC;QAEf,OAAO,CACL,8BAAC,kCAAmB,CAAC,QAAQ,IAAC,KAAK,EAAE,IAAI,CAAC,YAAY,IACnD,iBAAiB,CAChB,uCACE,SAAS,EAAE,IAAA,oBAAU,EAAC,kCAAkC,EAAE,SAAS,EAAE;gBACnE,mBAAmB,EAAE,MAAM,IAAI,eAAe,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ;gBACxE,0BAA0B,EAAE,IAAI,CAAC,KAAK,CAAC,sBAAsB;aAC9D,CAAC,EACF,GAAG,EAAE,UAAC,OAAO,IAAK,OAAA,CAAC,KAAI,CAAC,WAAW,GAAG,OAAO,CAAC,EAA5B,CAA4B;YAE7C,IAAI,CAAC,aAAa,EAAE;YACrB,uCAAK,SAAS,EAAC,oBAAoB,IAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAO;YAC9D,CAAC,gCAAgC,IAAI,CACpC,uCACE,SAAS,EAAC,4BAA4B,EACtC,OAAO,EAAE;oBACP,KAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;gBACxC,CAAC,GACD,CACH;YACD,uCAAK,SAAS,EAAC,sCAAsC,IAAE,kBAAkB,CAAO;YAC/E,kBAAkB,CAAC,aAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,uCAAK,SAAS,EAAC,uBAAuB,IACnC,IAAA,gBAAM,EAA2B,wCAAwB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAClF,CACF,CACP,CAC4B,CAChC,CAAC;IACJ,CAAC;IAEO,iDAAkB,GAA1B;QACQ,IAAA,KAAkC,IAAI,CAAC,KAAK,EAA1C,eAAe,qBAAA,EAAE,UAAU,gBAAe,CAAC;QACnD,IAAI,eAAe,EAAE;YACnB,OAAO,eAAe,CAAC;SACxB;aAAM,IAAI,UAAU,EAAE;YACrB,OAAO,uDAA8B,CAAC;SACvC;aAAM;YACL,OAAO,0DAAiC,CAAC;SAC1C;IACH,CAAC;IAEO,4CAAa,GAArB;;QAAA,iBA+CC;QA9CO,IAAA,KAA6F,IAAI,CAAC,KAAK,EAArG,KAAK,WAAA,EAAE,SAAS,eAAA,EAAE,kBAAkB,wBAAA,EAAE,2BAA2B,iCAAA,EAAE,IAAI,UAAA,EAAE,aAAa,mBAAe,CAAC;QACtG,IAAA,sBAAsB,GAAK,IAAI,CAAC,KAAK,uBAAf,CAAgB;QAC9C,IAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAClD,IAAM,mBAAmB,GAAG,SAAS,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QACzD,IAAM,kBAAkB,GAAG,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,UAAC,EAAsB,IAAK,OAAA,EAAE,EAAF,CAAE,CAAC;QAE/G,IAAI,aAAa,EAAE;YACjB,IAAM,gBAAgB,GAAG,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAA4B,CAAC;YAC7G,OAAO,CACL,uCAAK,SAAS,EAAE,IAAA,oBAAU,EAAC,uBAAuB,EAAE,EAAE,SAAS,EAAE,mBAAmB,EAAE,CAAC,IACpF,gBAAgB,CACb,CACP,CAAC;SACH;QAED,IAAM,QAAQ,GAAG,kBAAkB,CACjC,uCAAK,KAAK,EAAE,KAAK,EAAE,SAAS,EAAC,qBAAqB,IAC/C,KAAK,CACF,CACN,CAAC;QAEH,IAAM,qBAAqB,GAAG,CAAC,IAAA,iBAAO,EAAC,kBAAkB,CAAC,CAAC;QAE3D,OAAO,CACL,uCAAK,SAAS,EAAE,IAAA,oBAAU,EAAC,uBAAuB,EAAE,EAAE,SAAS,EAAE,mBAAmB,EAAE,CAAC;YACpF,QAAQ;YACT,uCAAK,SAAS,EAAE,IAAA,oBAAU,EAAC,wBAAwB,EAAE,qCAAiB,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;gBAC/F,qBAAqB,IAAI,CACxB,0CACE,OAAO,EAAE,cAAM,OAAA,KAAI,CAAC,yBAAyB,CAAC,CAAC,sBAAsB,CAAC,EAAvD,CAAuD,EACtE,SAAS,EAAE,IAAA,oBAAU,EACnB,qCAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,QAAQ,EAAE,SAAS,CAAC,EAClF,qCAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,MAAM,CAAC;wBAErE,GAAC,qCAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC,IAAG,sBAAsB;4BAEpG;oBAED,wCAAM,SAAS,EAAC,cAAc,IAAE,2BAA4B,CAAQ,CAC7D,CACV;gBACA,qBAAqB,IAAI,8BAAC,qBAAS,OAAG;gBACtC,eAAe,CACZ,CACF,CACP,CAAC;IACJ,CAAC;IAQO,8CAAe,GAAvB;QACE,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,IAAI,EAAE;YACjC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;SACrE;IACH,CAAC;IA1IM,iCAAY,GAA4C;QAC7D,2BAA2B,EAAE,MAAM;QACnC,SAAS,EAAE,IAAI;QACf,aAAa,EAAE,UAAC,EAAS;gBAAP,KAAK,WAAA;YAAO,OAAA,CAC5B,uCAAK,SAAS,EAAC,gBAAgB;gBAC7B,uCAAK,SAAS,EAAC,uBAAuB;oBACpC,uCAAK,SAAS,EAAC,qBAAqB,IAAE,KAAK,CAAO,CAC9C;gBACN,uCAAK,SAAS,EAAC,oBAAoB;oBACjC,0CAAK,KAAK,CAAM;oBAChB,8BAAC,qCAAiB,CAAC,IAAI,IAAC,SAAS,EAAC,sBAAsB,EAAC,IAAI,EAAC,OAAO,EAAC,IAAI,EAAC,aAAa,GAAG,CACvF,CACF,CACP;QAV6B,CAU7B;QACD,aAAa,EAAE,IAAI;KACpB,CAAC;IACK,gCAAW,GAAG,4BAAa,CAAC;IA6KrC,2BAAC;CAAA,AAjMD,CAA+D,eAAK,CAAC,SAAS,GAiM7E;AAjMY,oDAAoB;AAmMjC,SAAS,6BAA6B,CAA+B,KAAmC;IAChG,IAAA,KAA8B,IAAA,kBAAU,EAAC,4BAAa,CAAC,EAArD,aAAa,mBAAA,EAAE,QAAQ,cAA8B,CAAC;IAExD,IAAA,KAA4C,IAAA,mBAAO,EAAiB;QACxE,IAAI,EAAE,sBAAc,CAAC,MAAM;QAC3B,IAAI,EAAE,UAAC,QAAQ;YACb,IAAI,KAAK,CAAC,WAAW,EAAE;gBACrB,KAAK,CAAC,WAAW,EAAE,CAAC;aACrB;YACD,+CAA+C;YAC/C,yFAAyF;YACzF,IAAM,SAAS,GAAG,IAAA,eAAK,EAAC,cAAM,OAAA,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAA9B,CAA8B,CAAC,CAAC;YAC9D,OAAO;gBACL,QAAQ,UAAA;gBACR,SAAS,WAAA;aACV,CAAC;QACJ,CAAC;QACD,GAAG,EAAE,UAAC,IAAI,EAAE,OAAO;YACT,IAAA,SAAS,GAAK,IAAI,UAAT,CAAU;YAC3B,kDAAkD;YAClD,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAE/B,IAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;YAC3B,IAAM,UAAU,GAAmB,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE,CAAmB,CAAC;YAC7E,IAAA,QAAQ,GAA4B,UAAU,SAAtC,EAAQ,eAAe,GAAK,UAAU,KAAf,CAAgB;YACvD,IAAI,QAAQ,IAAI,IAAI,IAAI,eAAe,IAAI,IAAI,IAAI,CAAC,IAAA,iBAAO,EAAC,eAAe,EAAE,OAAO,CAAC,EAAE;gBACrF,aAAa,CAAC,UAAU,CAAC,IAAA,mCAAmB,EAAC,aAAa,CAAC,OAAO,EAAG,EAAE,OAAO,EAAE,eAAe,EAAE,QAAQ,CAAC,CAAC,CAAC;gBAC5G,IAAI,KAAK,CAAC,SAAS,EAAE;oBACnB,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;iBACzB;aACF;iBAAM;gBACL,yCAAyC;gBACzC,aAAa,CAAC,UAAU,CAAC;oBACvB;wBACE,IAAI,EAAE,IAAA,mBAAS,EAAC,OAAO,CAAC;wBACxB,IAAI,EAAE;4BACJ,eAAe,EAAE;gCACf,IAAI,EAAE,SAAS;6BAChB;yBACF;qBACF;iBACF,CAAC,CAAC;gBACH,IAAI,KAAK,CAAC,SAAS,EAAE;oBACnB,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;iBAC1B;aACF;QACH,CAAC;KACF,CAAC,EA5CO,iBAAiB,QAAA,EAAE,kBAAkB,QA4C5C,CAAC;IAEG,IAAA,KAAmD,IAAA,mBAAO,EAAC;QAC/D,MAAM,EAAE,sBAAc,CAAC,MAAM;QAC7B,OAAO,EAAE,UAAC,OAA0C;;YAAK,OAAA,CAAC;gBACxD,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;gBACxB,eAAe,EAAE,MAAA,OAAO,CAAC,OAAO,EAAE,0CAAE,QAAQ;aAC7C,CAAC,CAAA;SAAA;KACH,CAAC,EANK,UAA2B,EAAzB,MAAM,YAAA,EAAE,eAAe,qBAAA,EAAI,iBAAiB,QAMnD,CAAC;IACH,OAAO,CACL,8BAAC,oBAAoB,eACf,KAAK,IACT,kBAAkB,EAAE,kBAAkB,EACtC,iBAAiB,EAAE,iBAAiB,EACpC,iBAAiB,EAAE,iBAAiB,EACpC,MAAM,EAAE,MAAM,EACd,eAAe,EAAE,eAAe,IAChC,CACH,CAAC;AACJ,CAAC;AAED;IAAgE,gCAAyC;IAAzG;;IAIA,CAAC;IAHC,6BAAM,GAAN;QACE,OAAO,8BAAC,6BAA6B,eAAS,IAAI,CAAC,KAAsC,EAAI,CAAC;IAChG,CAAC;IACH,mBAAC;AAAD,CAAC,AAJD,CAAgE,eAAK,CAAC,aAAa,GAIlF;AAJY,oCAAY"}