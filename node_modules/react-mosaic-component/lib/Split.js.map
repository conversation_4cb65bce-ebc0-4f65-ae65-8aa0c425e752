{"version": 3, "file": "Split.js", "sourceRoot": "", "sources": ["../src/Split.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,0DAAoC;AACpC,uDAAiC;AACjC,6DAAuC;AACvC,gDAA0B;AAG1B,kDAAiD;AAEjD,IAAM,kBAAkB,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,SAAS;AAE/C,IAAM,mBAAmB,GAAG;IAC1B,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,KAAK;CACf,CAAC;AAUF;IAA2B,yBAA+B;IAA1D;QAAA,qEAmHC;QAlHS,iBAAW,GAAG,eAAK,CAAC,SAAS,EAAkB,CAAC;QAChD,oBAAc,GAAG,KAAK,CAAC;QAkEvB,iBAAW,GAAG,UAAC,KAAoD;YACzE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;gBACxB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;oBACtB,OAAO;iBACR;aACF;YAED,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,KAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC,CAAC;QAEM,eAAS,GAAG,UAAC,KAA8B;YACjD,KAAI,CAAC,eAAe,EAAE,CAAC;YAEvB,IAAM,UAAU,GAAG,KAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;YAC3D,KAAI,CAAC,KAAK,CAAC,SAAU,CAAC,UAAU,CAAC,CAAC;QACpC,CAAC,CAAC;QAEM,iBAAW,GAAG,UAAC,KAA8B;YACnD,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,KAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC,CAAC;QAEM,+BAAyB,GAAG,IAAA,kBAAQ,EAAC,UAAC,KAA8B;YAC1E,IAAM,UAAU,GAAG,KAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;YAC3D,IAAI,UAAU,KAAK,KAAI,CAAC,KAAK,CAAC,eAAe,EAAE;gBAC7C,KAAI,CAAC,KAAK,CAAC,QAAS,CAAC,UAAU,CAAC,CAAC;aAClC;QACH,CAAC,EAAE,kBAAkB,CAAC,CAAC;;IAkBzB,CAAC;IAzGC,sBAAM,GAAN;QACU,IAAA,SAAS,GAAK,IAAI,CAAC,KAAK,UAAf,CAAgB;QACjC,OAAO,CACL,uCACE,SAAS,EAAE,IAAA,oBAAU,EAAC,cAAc,EAAE;gBACpC,MAAM,EAAE,SAAS,KAAK,KAAK;gBAC3B,SAAS,EAAE,SAAS,KAAK,QAAQ;aAClC,CAAC,EACF,GAAG,EAAE,IAAI,CAAC,WAAW,EACrB,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE;YAE1B,uCAAK,SAAS,EAAC,mBAAmB,GAAG,CACjC,CACP,CAAC;IACJ,CAAC;IAED,iCAAiB,GAAjB;QACE,IAAI,CAAC,WAAW,CAAC,OAAQ,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;IAClG,CAAC;IAED,oCAAoB,GAApB;QACE,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;YAC5B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,aAAc,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;SAClH;IACH,CAAC;IAEO,6BAAa,GAArB;QACE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,WAAW,CAAC,OAAQ,CAAC,aAAc,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAC/F,IAAI,CAAC,WAAW,CAAC,OAAQ,CAAC,aAAc,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;YAC9G,IAAI,CAAC,WAAW,CAAC,OAAQ,CAAC,aAAc,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAC3F,IAAI,CAAC,WAAW,CAAC,OAAQ,CAAC,aAAc,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAC5F,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC5B;IACH,CAAC;IAEO,+BAAe,GAAvB;QACE,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;YAC5B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,aAAc,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YACjG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,aAAc,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;YAChH,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,aAAc,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAC7F,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,aAAc,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAC9F,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;SAC7B;IACH,CAAC;IAEO,4BAAY,GAApB;;QACQ,IAAA,KAA8C,IAAI,CAAC,KAAK,EAAtD,WAAW,iBAAA,EAAE,SAAS,eAAA,EAAE,eAAe,qBAAe,CAAC;QAC/D,IAAM,aAAa,GAAG,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;QAC9D,IAAM,kBAAkB,GAAG,yBAAW,CAAC,0BAA0B,CAAC,WAAW,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC;QAC3G,6BACK,yBAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,gBACnC,aAAa,IAAG,UAAG,kBAAkB,MAAG,OACzC;IACJ,CAAC;IAiCO,2CAA2B,GAAnC,UAAoC,KAA8B;QAC1D,IAAA,KAAwD,IAAI,CAAC,KAAK,EAAhE,yBAAyB,+BAAA,EAAE,SAAS,eAAA,EAAE,WAAW,iBAAe,CAAC;QACzE,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,OAAQ,CAAC,aAAc,CAAC,qBAAqB,EAAE,CAAC;QACpF,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAEvE,IAAI,kBAA0B,CAAC;QAC/B,IAAI,SAAS,KAAK,QAAQ,EAAE;YAC1B,kBAAkB,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;SACxF;aAAM;YACL,kBAAkB,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;SACxF;QAED,IAAM,kBAAkB,GAAG,yBAAW,CAAC,0BAA0B,CAAC,WAAW,EAAE,kBAAkB,EAAE,SAAS,CAAC,CAAC;QAE9G,OAAO,IAAA,eAAK,EAAC,kBAAkB,EAAE,yBAA0B,EAAE,GAAG,GAAG,yBAA0B,CAAC,CAAC;IACjG,CAAC;IA9GM,kBAAY,GAAG;QACpB,QAAQ,EAAE,cAAM,OAAA,KAAK,CAAC,EAAN,CAAM;QACtB,SAAS,EAAE,cAAM,OAAA,KAAK,CAAC,EAAN,CAAM;QACvB,yBAAyB,EAAE,EAAE;KAC9B,CAAC;IA2GJ,YAAC;CAAA,AAnHD,CAA2B,eAAK,CAAC,aAAa,GAmH7C;AAnHY,sBAAK;AAqHlB,SAAS,YAAY,CAAC,KAAsD;IAC1E,OAAQ,KAAoB,CAAC,cAAc,IAAI,IAAI,CAAC;AACtD,CAAC"}