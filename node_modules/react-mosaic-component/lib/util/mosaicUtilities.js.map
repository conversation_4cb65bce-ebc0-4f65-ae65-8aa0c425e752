{"version": 3, "file": "mosaicUtilities.js", "sourceRoot": "", "sources": ["../../src/util/mosaicUtilities.ts"], "names": [], "mappings": ";;;;;;AAAA,uDAAiC;AACjC,mDAA6B;AAG7B,SAAS,kBAAkB,CACzB,IAAmB,EACnB,SAAkC;IAAlC,0BAAA,EAAA,iBAAkC;IAElC,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;QAClB,IAAM,aAAa,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC;QACnD,OAAO;YACL,SAAS,WAAA;YACT,KAAK,EAAE,kBAAkB,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;YACpD,MAAM,EAAE,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC;SACvD,CAAC;KACH;SAAM;QACL,OAAO,IAAI,CAAC;KACb;AACH,CAAC;AAED,IAAY,MAKX;AALD,WAAY,MAAM;IAChB,2CAAY,CAAA;IACZ,6CAAS,CAAA;IACT,iDAAW,CAAA;IACX,mDAAY,CAAA;AACd,CAAC,EALW,MAAM,GAAN,cAAM,KAAN,cAAM,QAKjB;AAED;;;;GAIG;AACH,SAAgB,QAAQ,CAAsB,IAAmB;IAC/D,OAAQ,IAAwB,CAAC,SAAS,IAAI,IAAI,CAAC;AACrD,CAAC;AAFD,4BAEC;AAED;;;;;GAKG;AACH,SAAgB,4BAA4B,CAC1C,MAAuB,EACvB,cAAuC;IAAvC,+BAAA,EAAA,sBAAuC;IAEvC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,IAAI,CAAC;KACb;IAED,IAAI,OAAO,GAAoB,IAAA,eAAK,EAAC,MAAM,CAAC,CAAC;IAC7C,IAAI,IAAI,GAAoB,EAAE,CAAC;IAE/B,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;QACzB,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtB,IAAI,CAAC,IAAI,CAAC;oBACR,SAAS,EAAE,KAAK;oBAChB,KAAK,EAAE,OAAO,CAAC,KAAK,EAAG;oBACvB,MAAM,EAAE,OAAO,CAAC,KAAK,EAAG;iBACzB,CAAC,CAAC;aACJ;iBAAM;gBACL,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAG,CAAC,CAAC;aAChC;SACF;QACD,OAAO,GAAG,IAAI,CAAC;QACf,IAAI,GAAG,EAAE,CAAC;KACX;IACD,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;AACxD,CAAC;AA3BD,oEA2BC;AAED;;;;GAIG;AACH,SAAgB,cAAc,CAAC,MAAoB;IACjD,IAAI,MAAM,KAAK,OAAO,EAAE;QACtB,OAAO,QAAQ,CAAC;KACjB;SAAM,IAAI,MAAM,KAAK,QAAQ,EAAE;QAC9B,OAAO,OAAO,CAAC;KAChB;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,kBAAW,MAAM,yBAAsB,CAAC,CAAC;KAC1D;AACH,CAAC;AARD,wCAQC;AAED;;;;GAIG;AACH,SAAgB,iBAAiB,CAAC,SAA0B;IAC1D,IAAI,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO,QAAQ,CAAC;KACjB;SAAM;QACL,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAND,8CAMC;AAED;;;;;GAKG;AACH,SAAgB,eAAe,CAAC,IAAqB,EAAE,MAAc;IACnE,IAAI,WAAW,GAAoB,IAAI,CAAC;IACxC,IAAM,WAAW,GAAe,EAAE,CAAC;IACnC,OAAO,QAAQ,CAAC,WAAW,CAAC,EAAE;QAC5B,IAAI,WAAW,CAAC,SAAS,KAAK,KAAK,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,IAAI,MAAM,KAAK,MAAM,CAAC,WAAW,CAAC,EAAE;YACpG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1B,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC;SACjC;aAAM,IAAI,WAAW,CAAC,SAAS,KAAK,QAAQ,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,IAAI,MAAM,KAAK,MAAM,CAAC,SAAS,CAAC,EAAE;YAC5G,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1B,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC;SACjC;aAAM;YACL,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3B,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;SAClC;KACF;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAjBD,0CAiBC;AAED;;;;GAIG;AACH,SAAgB,SAAS,CAAsB,IAA0B;IACvE,IAAI,IAAI,IAAI,IAAI,EAAE;QAChB,OAAO,EAAE,CAAC;KACX;SAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;QACzB,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;KAC7D;SAAM;QACL,OAAO,CAAC,IAAI,CAAC,CAAC;KACf;AACH,CAAC;AARD,8BAQC;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAAsB,IAA0B,EAAE,IAAgB;IAC7F,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QACnB,OAAO,IAAA,aAAG,EAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;KAC9B;SAAM;QACL,OAAO,IAAI,CAAC;KACb;AACH,CAAC;AAND,sCAMC;AAED;;;;;GAKG;AACH,SAAgB,4BAA4B,CAC1C,IAA0B,EAC1B,IAAgB;IAEhB,IAAI,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;KACrD;IACD,IAAM,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,IAAI,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,gBAAS,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gCAA6B,CAAC,CAAC;KACxE;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAZD,oEAYC"}