{"version": 3, "file": "Mosaic.js", "sourceRoot": "", "sources": ["../src/Mosaic.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,0DAAoC;AAEpC,2DAAqC;AACrC,qDAA+B;AAC/B,yDAAmC;AACnC,+DAAqD;AACrD,gDAA0B;AAC1B,uCAAwC;AACxC,mEAAuD;AACvD,6BAAkC;AAElC,+CAAkE;AAClE,2CAA0C;AAC1C,qDAAoD;AACpD,qDAAoD;AAEpD,sDAA4G;AAC5G,0DAAmD;AAEnD,IAAM,yBAAyB,GAAG,EAAE,CAAC;AAiErC,SAAS,cAAc,CAAsB,KAAqB;IAChE,OAAQ,KAAoC,CAAC,YAAY,IAAI,IAAI,CAAC;AACpE,CAAC;AAQD;IAAgF,gDAG/E;IAHD;QAAA,iBA6HC;;;QAhGC,WAAK,GAAmB;YACtB,WAAW,EAAE,IAAI;YACjB,gBAAgB,EAAE,IAAI;YACtB,QAAQ,EAAE,MAAA,KAAI,CAAC,KAAK,CAAC,QAAQ,mCAAI,IAAA,SAAI,GAAE;SACxC,CAAC;QAuBM,gBAAU,GAAG,UAAC,OAA0B,EAAE,iBAAkC;YAAlC,kCAAA,EAAA,yBAAkC;YAClF,IAAM,WAAW,GAAG,KAAI,CAAC,OAAO,EAAE,IAAK,EAAoB,CAAC;YAE5D,KAAI,CAAC,WAAW,CAAC,IAAA,0BAAU,EAAC,WAAW,EAAE,OAAO,CAAC,EAAE,iBAAiB,CAAC,CAAC;QACxE,CAAC,CAAC;QAEM,iBAAW,GAAG,UAAC,WAAiC,EAAE,iBAAkC;YAAlC,kCAAA,EAAA,yBAAkC;YAC1F,KAAI,CAAC,KAAK,CAAC,QAAS,CAAC,WAAW,CAAC,CAAC;YAClC,IAAI,CAAC,iBAAiB,IAAI,KAAI,CAAC,KAAK,CAAC,SAAS,EAAE;gBAC9C,KAAI,CAAC,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;aACnC;YAED,IAAI,cAAc,CAAC,KAAI,CAAC,KAAK,CAAC,EAAE;gBAC9B,KAAI,CAAC,QAAQ,CAAC,EAAE,WAAW,aAAA,EAAE,CAAC,CAAC;aAChC;QACH,CAAC,CAAC;QAEM,aAAO,GAAyB;YACtC,UAAU,EAAE,KAAI,CAAC,UAAU;YAC3B,MAAM,EAAE,UAAC,IAAgB;gBACvB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrB,KAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;iBACxB;qBAAM;oBACL,KAAI,CAAC,UAAU,CAAC,CAAC,IAAA,kCAAkB,EAAC,KAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;iBAC7D;YACH,CAAC;YACD,MAAM,EAAE,UAAC,IAAgB,EAAE,UAA8C;gBAA9C,2BAAA,EAAA,sCAA8C;gBACvE,OAAA,KAAI,CAAC,UAAU,CAAC,CAAC,IAAA,kCAAkB,EAAI,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;YAA1D,CAA0D;YAC5D,OAAO,EAAE,cAAM,OAAA,KAAI,CAAC,OAAO,EAAG,EAAf,CAAe;YAC9B,IAAI,EAAE,UAAC,IAAgB,IAAK,OAAA,KAAI,CAAC,UAAU,CAAC,CAAC,IAAA,gCAAgB,EAAI,IAAI,CAAC,CAAC,CAAC,EAA5C,CAA4C;YACxE,WAAW,EAAE,UAAC,IAAgB,EAAE,OAAsB;gBACpD,OAAA,KAAI,CAAC,UAAU,CAAC;oBACd;wBACE,IAAI,MAAA;wBACJ,IAAI,EAAE;4BACJ,IAAI,EAAE,OAAO;yBACd;qBACF;iBACF,CAAC;YAPF,CAOE;SACL,CAAC;QAEe,kBAAY,GAAqB;YAChD,aAAa,EAAE,KAAI,CAAC,OAAO;YAC3B,QAAQ,EAAE,KAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,kBAAkB,EAAE,KAAI,CAAC,KAAK,CAAC,kBAAmB;SACnD,CAAC;;IAwBJ,CAAC;IAlHQ,qDAAwB,GAA/B,UACE,SAA2C,EAC3C,SAAiC;QAEjC,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;YAC5G,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;SACtF;QAED,IAAI,cAAc,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,YAAY,KAAK,SAAS,CAAC,gBAAgB,EAAE;YACtF,OAAO;gBACL,gBAAgB,EAAE,SAAS,CAAC,YAAY;gBACxC,WAAW,EAAE,SAAS,CAAC,YAAY;aACpC,CAAC;SACH;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAQD,6CAAM,GAAN;QACU,IAAA,SAAS,GAAK,IAAI,CAAC,KAAK,UAAf,CAAgB;QAEjC,OAAO,CACL,8BAAC,4BAAa,CAAC,QAAQ,IAAC,KAAK,EAAE,IAAI,CAAC,YAAkC;YACpE,uCAAK,SAAS,EAAE,IAAA,oBAAU,EAAC,SAAS,EAAE,2BAA2B,CAAC;gBAC/D,IAAI,CAAC,UAAU,EAAE;gBAClB,8BAAC,iCAAe,OAAG,CACf,CACiB,CAC1B,CAAC;IACJ,CAAC;IAEO,8CAAO,GAAf;QACE,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;SAC/B;aAAM;YACL,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;SACzB;IACH,CAAC;IAiDO,iDAAU,GAAlB;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC5B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE;YACvC,OAAO,IAAI,CAAC,KAAK,CAAC,aAAc,CAAC;SAClC;aAAM;YACC,IAAA,KAAyB,IAAI,CAAC,KAAK,EAAjC,UAAU,gBAAA,EAAE,MAAM,YAAe,CAAC;YAC1C,OAAO,8BAAC,uBAAU,IAAC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,GAAI,CAAC;SAC3E;IACH,CAAC;IAEO,mDAAY,GAApB,UAAqB,IAA0B;QAC7C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;YACzC,IAAM,UAAU,GAAG,IAAA,cAAI,EAAC,IAAA,gBAAM,EAAC,IAAA,iBAAO,EAAC,IAAA,2BAAS,EAAC,IAAI,CAAC,CAAC,EAAE,UAAC,CAAC,IAAK,OAAA,CAAC,GAAG,CAAC,EAAL,CAAK,CAAC,CAAC,CAAC;YAExE,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzB,MAAM,IAAI,KAAK,CACb,yBAAkB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,gEAA6D,CACrG,CAAC;aACH;SACF;IACH,CAAC;IAxHM,yCAAY,GAAG;QACpB,QAAQ,EAAE,cAAM,OAAA,KAAK,CAAC,EAAN,CAAM;QACtB,aAAa,EAAE,8BAAC,iCAAe,OAAG;QAClC,SAAS,EAAE,wBAAwB;QACnC,kBAAkB,EAAE,KAAK;KAC1B,CAAC;IAoHJ,mCAAC;CAAA,AA7HD,CAAgF,eAAK,CAAC,aAAa,GA6HlG;AA7HY,oEAA4B;AA+HzC;IAA0D,0BAAmC;IAA7F;;IAYA,CAAC;IAXC,uBAAM,GAAN;QACE,OAAO,CACL,8BAAC,uBAAW,aACV,OAAO,EAAE,sCAAY,EACrB,OAAO,EAAE,oCAAY,IACjB,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAEjF,8BAAC,4BAA4B,eAAQ,IAAI,CAAC,KAAK,EAAI,CACvC,CACf,CAAC;IACJ,CAAC;IACH,aAAC;AAAD,CAAC,AAZD,CAA0D,eAAK,CAAC,aAAa,GAY5E;AAZY,wBAAM"}