{"hash": "acbfac69", "configHash": "e9ab834c", "lockfileHash": "1a0bdcbb", "browserHash": "300f0f8b", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "0b9cd759", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "f18cd6ca", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "050d2960", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "2796ce42", "needsInterop": true}, "@monaco-editor/react": {"src": "../../@monaco-editor/react/dist/index.mjs", "file": "@monaco-editor_react.js", "fileHash": "2488712d", "needsInterop": false}, "flexlayout-react": {"src": "../../flexlayout-react/dist/index.js", "file": "flexlayout-react.js", "fileHash": "fa703f08", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "77fb41a5", "needsInterop": false}, "openai": {"src": "../../openai/index.mjs", "file": "openai.js", "fileHash": "057bad76", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "8b0c5bb8", "needsInterop": true}}, "chunks": {"chunk-ABJ2RQAK": {"file": "chunk-ABJ2RQAK.js"}, "chunk-HE4GKDYE": {"file": "chunk-HE4GKDYE.js"}, "chunk-MJNCUEZK": {"file": "chunk-MJNCUEZK.js"}, "chunk-UGC3UZ7L": {"file": "chunk-UGC3UZ7L.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}