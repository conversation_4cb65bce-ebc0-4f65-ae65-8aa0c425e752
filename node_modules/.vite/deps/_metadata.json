{"hash": "28c0e491", "configHash": "e9ab834c", "lockfileHash": "18e98526", "browserHash": "acb4eb88", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "9f3fb313", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "c4a6dcd6", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "ce305457", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "e50c8e6b", "needsInterop": true}, "@monaco-editor/react": {"src": "../../@monaco-editor/react/dist/index.mjs", "file": "@monaco-editor_react.js", "fileHash": "305852e1", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "a76329ce", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "8eff1a90", "needsInterop": true}, "react-resizable-panels": {"src": "../../react-resizable-panels/dist/react-resizable-panels.browser.development.js", "file": "react-resizable-panels.js", "fileHash": "ec68e09e", "needsInterop": false}, "openai": {"src": "../../openai/index.mjs", "file": "openai.js", "fileHash": "093d0e9a", "needsInterop": false}, "react-mosaic-component": {"src": "../../react-mosaic-component/lib/index.js", "file": "react-mosaic-component.js", "fileHash": "04e24ffd", "needsInterop": true}, "rc-dock": {"src": "../../rc-dock/es/index.js", "file": "rc-dock.js", "fileHash": "33735c66", "needsInterop": false}}, "chunks": {"chunk-VTIQK5XW": {"file": "chunk-VTIQK5XW.js"}, "chunk-GEG26V2R": {"file": "chunk-GEG26V2R.js"}, "chunk-Y5BGZF4O": {"file": "chunk-Y5BGZF4O.js"}, "chunk-H5FQS3OF": {"file": "chunk-H5FQS3OF.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}