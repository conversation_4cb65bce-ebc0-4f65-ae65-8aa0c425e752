{"hash": "6acefe70", "configHash": "e9ab834c", "lockfileHash": "e35ac6bd", "browserHash": "32732f5d", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "29b2d887", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "023441e2", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "ab7040ea", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "1bc5dd30", "needsInterop": true}, "@monaco-editor/react": {"src": "../../@monaco-editor/react/dist/index.mjs", "file": "@monaco-editor_react.js", "fileHash": "d581e645", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "ee559879", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "727731fa", "needsInterop": true}, "react-resizable-panels": {"src": "../../react-resizable-panels/dist/react-resizable-panels.browser.development.js", "file": "react-resizable-panels.js", "fileHash": "5e8588d8", "needsInterop": false}, "openai": {"src": "../../openai/index.mjs", "file": "openai.js", "fileHash": "df1322cd", "needsInterop": false}, "react-mosaic-component": {"src": "../../react-mosaic-component/lib/index.js", "file": "react-mosaic-component.js", "fileHash": "6ed04bec", "needsInterop": true}}, "chunks": {"chunk-Y5BGZF4O": {"file": "chunk-Y5BGZF4O.js"}, "chunk-VTIQK5XW": {"file": "chunk-VTIQK5XW.js"}, "chunk-H5FQS3OF": {"file": "chunk-H5FQS3OF.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}