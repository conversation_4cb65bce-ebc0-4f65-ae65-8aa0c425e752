{"hash": "66322ea3", "configHash": "e9ab834c", "lockfileHash": "eb0554a4", "browserHash": "352a4557", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "35fc59d4", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "de6145ab", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "593354d6", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "13f7e650", "needsInterop": true}, "@monaco-editor/react": {"src": "../../@monaco-editor/react/dist/index.mjs", "file": "@monaco-editor_react.js", "fileHash": "6fbfbae4", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "9181c0a2", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "f05fb2ac", "needsInterop": true}, "react-resizable-panels": {"src": "../../react-resizable-panels/dist/react-resizable-panels.browser.development.js", "file": "react-resizable-panels.js", "fileHash": "f521fa79", "needsInterop": false}, "openai": {"src": "../../openai/index.mjs", "file": "openai.js", "fileHash": "027d8400", "needsInterop": false}}, "chunks": {"chunk-HE4GKDYE": {"file": "chunk-HE4GKDYE.js"}, "chunk-UGC3UZ7L": {"file": "chunk-UGC3UZ7L.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}