{"version": 3, "sources": ["../../flexlayout-react/dist/index.js"], "sourcesContent": ["/**\n * flexlayout-react\n * @version 0.8.17\n */\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\nimport { jsx, jsxs, Fragment } from \"react/jsx-runtime\";\nimport * as React from \"react\";\nimport { useRef, useEffect } from \"react\";\nimport { createPortal } from \"react-dom\";\nimport { createRoot } from \"react-dom/client\";\nconst _Orientation = class _Orientation {\n  /** @internal */\n  constructor(name) {\n    /** @internal */\n    __publicField(this, \"_name\");\n    this._name = name;\n  }\n  static flip(from) {\n    if (from === _Orientation.HORZ) {\n      return _Orientation.VERT;\n    } else {\n      return _Orientation.HORZ;\n    }\n  }\n  getName() {\n    return this._name;\n  }\n  toString() {\n    return this._name;\n  }\n};\n__publicField(_Orientation, \"HORZ\", new _Orientation(\"horz\"));\n__publicField(_Orientation, \"VERT\", new _Orientation(\"vert\"));\nlet Orientation = _Orientation;\nclass Rect {\n  constructor(x, y, width, height) {\n    __publicField(this, \"x\");\n    __publicField(this, \"y\");\n    __publicField(this, \"width\");\n    __publicField(this, \"height\");\n    this.x = x;\n    this.y = y;\n    this.width = width;\n    this.height = height;\n  }\n  static empty() {\n    return new Rect(0, 0, 0, 0);\n  }\n  static fromJson(json) {\n    return new Rect(json.x, json.y, json.width, json.height);\n  }\n  toJson() {\n    return { x: this.x, y: this.y, width: this.width, height: this.height };\n  }\n  snap(round) {\n    this.x = Math.round(this.x / round) * round;\n    this.y = Math.round(this.y / round) * round;\n    this.width = Math.round(this.width / round) * round;\n    this.height = Math.round(this.height / round) * round;\n  }\n  static getBoundingClientRect(element) {\n    const { x, y, width, height } = element.getBoundingClientRect();\n    return new Rect(x, y, width, height);\n  }\n  static getContentRect(element) {\n    const rect = element.getBoundingClientRect();\n    const style2 = window.getComputedStyle(element);\n    const paddingLeft = parseFloat(style2.paddingLeft);\n    const paddingRight = parseFloat(style2.paddingRight);\n    const paddingTop = parseFloat(style2.paddingTop);\n    const paddingBottom = parseFloat(style2.paddingBottom);\n    const borderLeftWidth = parseFloat(style2.borderLeftWidth);\n    const borderRightWidth = parseFloat(style2.borderRightWidth);\n    const borderTopWidth = parseFloat(style2.borderTopWidth);\n    const borderBottomWidth = parseFloat(style2.borderBottomWidth);\n    const contentWidth = rect.width - borderLeftWidth - paddingLeft - paddingRight - borderRightWidth;\n    const contentHeight = rect.height - borderTopWidth - paddingTop - paddingBottom - borderBottomWidth;\n    return new Rect(\n      rect.left + borderLeftWidth + paddingLeft,\n      rect.top + borderTopWidth + paddingTop,\n      contentWidth,\n      contentHeight\n    );\n  }\n  static fromDomRect(domRect) {\n    return new Rect(domRect.x, domRect.y, domRect.width, domRect.height);\n  }\n  relativeTo(r) {\n    return new Rect(this.x - r.x, this.y - r.y, this.width, this.height);\n  }\n  clone() {\n    return new Rect(this.x, this.y, this.width, this.height);\n  }\n  equals(rect) {\n    return this.x === (rect == null ? void 0 : rect.x) && this.y === (rect == null ? void 0 : rect.y) && this.width === (rect == null ? void 0 : rect.width) && this.height === (rect == null ? void 0 : rect.height);\n  }\n  equalSize(rect) {\n    return this.width === (rect == null ? void 0 : rect.width) && this.height === (rect == null ? void 0 : rect.height);\n  }\n  getBottom() {\n    return this.y + this.height;\n  }\n  getRight() {\n    return this.x + this.width;\n  }\n  get bottom() {\n    return this.y + this.height;\n  }\n  get right() {\n    return this.x + this.width;\n  }\n  getCenter() {\n    return { x: this.x + this.width / 2, y: this.y + this.height / 2 };\n  }\n  positionElement(element, position) {\n    this.styleWithPosition(element.style, position);\n  }\n  styleWithPosition(style2, position = \"absolute\") {\n    style2.left = this.x + \"px\";\n    style2.top = this.y + \"px\";\n    style2.width = Math.max(0, this.width) + \"px\";\n    style2.height = Math.max(0, this.height) + \"px\";\n    style2.position = position;\n    return style2;\n  }\n  contains(x, y) {\n    if (this.x <= x && x <= this.getRight() && this.y <= y && y <= this.getBottom()) {\n      return true;\n    } else {\n      return false;\n    }\n  }\n  removeInsets(insets) {\n    return new Rect(this.x + insets.left, this.y + insets.top, Math.max(0, this.width - insets.left - insets.right), Math.max(0, this.height - insets.top - insets.bottom));\n  }\n  centerInRect(outerRect) {\n    this.x = (outerRect.width - this.width) / 2;\n    this.y = (outerRect.height - this.height) / 2;\n  }\n  /** @internal */\n  _getSize(orientation) {\n    let prefSize = this.width;\n    if (orientation === Orientation.VERT) {\n      prefSize = this.height;\n    }\n    return prefSize;\n  }\n  toString() {\n    return \"(Rect: x=\" + this.x + \", y=\" + this.y + \", width=\" + this.width + \", height=\" + this.height + \")\";\n  }\n}\nconst _DockLocation = class _DockLocation {\n  /** @internal */\n  constructor(_name, _orientation, _indexPlus) {\n    /** @internal */\n    __publicField(this, \"name\");\n    /** @internal */\n    __publicField(this, \"orientation\");\n    /** @internal */\n    __publicField(this, \"indexPlus\");\n    this.name = _name;\n    this.orientation = _orientation;\n    this.indexPlus = _indexPlus;\n    _DockLocation.values.set(this.name, this);\n  }\n  /** @internal */\n  static getByName(name) {\n    return _DockLocation.values.get(name);\n  }\n  /** @internal */\n  static getLocation(rect, x, y) {\n    x = (x - rect.x) / rect.width;\n    y = (y - rect.y) / rect.height;\n    if (x >= 0.25 && x < 0.75 && y >= 0.25 && y < 0.75) {\n      return _DockLocation.CENTER;\n    }\n    const bl = y >= x;\n    const br = y >= 1 - x;\n    if (bl) {\n      return br ? _DockLocation.BOTTOM : _DockLocation.LEFT;\n    } else {\n      return br ? _DockLocation.RIGHT : _DockLocation.TOP;\n    }\n  }\n  getName() {\n    return this.name;\n  }\n  getOrientation() {\n    return this.orientation;\n  }\n  /** @internal */\n  getDockRect(r) {\n    if (this === _DockLocation.TOP) {\n      return new Rect(r.x, r.y, r.width, r.height / 2);\n    } else if (this === _DockLocation.BOTTOM) {\n      return new Rect(r.x, r.getBottom() - r.height / 2, r.width, r.height / 2);\n    }\n    if (this === _DockLocation.LEFT) {\n      return new Rect(r.x, r.y, r.width / 2, r.height);\n    } else if (this === _DockLocation.RIGHT) {\n      return new Rect(r.getRight() - r.width / 2, r.y, r.width / 2, r.height);\n    } else {\n      return r.clone();\n    }\n  }\n  /** @internal */\n  split(rect, size) {\n    if (this === _DockLocation.TOP) {\n      const r1 = new Rect(rect.x, rect.y, rect.width, size);\n      const r2 = new Rect(rect.x, rect.y + size, rect.width, rect.height - size);\n      return { start: r1, end: r2 };\n    } else if (this === _DockLocation.LEFT) {\n      const r1 = new Rect(rect.x, rect.y, size, rect.height);\n      const r2 = new Rect(rect.x + size, rect.y, rect.width - size, rect.height);\n      return { start: r1, end: r2 };\n    }\n    if (this === _DockLocation.RIGHT) {\n      const r1 = new Rect(rect.getRight() - size, rect.y, size, rect.height);\n      const r2 = new Rect(rect.x, rect.y, rect.width - size, rect.height);\n      return { start: r1, end: r2 };\n    } else {\n      const r1 = new Rect(rect.x, rect.getBottom() - size, rect.width, size);\n      const r2 = new Rect(rect.x, rect.y, rect.width, rect.height - size);\n      return { start: r1, end: r2 };\n    }\n  }\n  /** @internal */\n  reflect() {\n    if (this === _DockLocation.TOP) {\n      return _DockLocation.BOTTOM;\n    } else if (this === _DockLocation.LEFT) {\n      return _DockLocation.RIGHT;\n    }\n    if (this === _DockLocation.RIGHT) {\n      return _DockLocation.LEFT;\n    } else {\n      return _DockLocation.TOP;\n    }\n  }\n  toString() {\n    return \"(DockLocation: name=\" + this.name + \", orientation=\" + this.orientation + \")\";\n  }\n};\n__publicField(_DockLocation, \"values\", /* @__PURE__ */ new Map());\n__publicField(_DockLocation, \"TOP\", new _DockLocation(\"top\", Orientation.VERT, 0));\n__publicField(_DockLocation, \"BOTTOM\", new _DockLocation(\"bottom\", Orientation.VERT, 1));\n__publicField(_DockLocation, \"LEFT\", new _DockLocation(\"left\", Orientation.HORZ, 0));\n__publicField(_DockLocation, \"RIGHT\", new _DockLocation(\"right\", Orientation.HORZ, 1));\n__publicField(_DockLocation, \"CENTER\", new _DockLocation(\"center\", Orientation.VERT, 0));\nlet DockLocation = _DockLocation;\nvar I18nLabel = /* @__PURE__ */ ((I18nLabel2) => {\n  I18nLabel2[\"Close_Tab\"] = \"Close\";\n  I18nLabel2[\"Close_Tabset\"] = \"Close tab set\";\n  I18nLabel2[\"Active_Tabset\"] = \"Active tab set\";\n  I18nLabel2[\"Move_Tabset\"] = \"Move tab set\";\n  I18nLabel2[\"Move_Tabs\"] = \"Move tabs(?)\";\n  I18nLabel2[\"Maximize\"] = \"Maximize tab set\";\n  I18nLabel2[\"Restore\"] = \"Restore tab set\";\n  I18nLabel2[\"Popout_Tab\"] = \"Popout selected tab\";\n  I18nLabel2[\"Overflow_Menu_Tooltip\"] = \"Hidden tabs\";\n  I18nLabel2[\"Error_rendering_component\"] = \"Error rendering component\";\n  I18nLabel2[\"Error_rendering_component_retry\"] = \"Retry\";\n  return I18nLabel2;\n})(I18nLabel || {});\nvar CLASSES = /* @__PURE__ */ ((CLASSES2) => {\n  CLASSES2[\"FLEXLAYOUT__BORDER\"] = \"flexlayout__border\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_\"] = \"flexlayout__border_\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_TAB_CONTENTS\"] = \"flexlayout__border_tab_contents\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_BUTTON\"] = \"flexlayout__border_button\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_BUTTON_\"] = \"flexlayout__border_button_\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_BUTTON_CONTENT\"] = \"flexlayout__border_button_content\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_BUTTON_LEADING\"] = \"flexlayout__border_button_leading\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_BUTTON_TRAILING\"] = \"flexlayout__border_button_trailing\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_BUTTON__SELECTED\"] = \"flexlayout__border_button--selected\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_BUTTON__UNSELECTED\"] = \"flexlayout__border_button--unselected\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_TOOLBAR_BUTTON_OVERFLOW\"] = \"flexlayout__border_toolbar_button_overflow\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_TOOLBAR_BUTTON_OVERFLOW_\"] = \"flexlayout__border_toolbar_button_overflow_\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_INNER\"] = \"flexlayout__border_inner\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_INNER_\"] = \"flexlayout__border_inner_\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_INNER_TAB_CONTAINER\"] = \"flexlayout__border_inner_tab_container\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_INNER_TAB_CONTAINER_\"] = \"flexlayout__border_inner_tab_container_\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_TAB_DIVIDER\"] = \"flexlayout__border_tab_divider\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_LEADING\"] = \"flexlayout__border_leading\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_SIZER\"] = \"flexlayout__border_sizer\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_TOOLBAR\"] = \"flexlayout__border_toolbar\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_TOOLBAR_\"] = \"flexlayout__border_toolbar_\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_TOOLBAR_BUTTON\"] = \"flexlayout__border_toolbar_button\";\n  CLASSES2[\"FLEXLAYOUT__BORDER_TOOLBAR_BUTTON_FLOAT\"] = \"flexlayout__border_toolbar_button-float\";\n  CLASSES2[\"FLEXLAYOUT__DRAG_RECT\"] = \"flexlayout__drag_rect\";\n  CLASSES2[\"FLEXLAYOUT__EDGE_RECT\"] = \"flexlayout__edge_rect\";\n  CLASSES2[\"FLEXLAYOUT__EDGE_RECT_TOP\"] = \"flexlayout__edge_rect_top\";\n  CLASSES2[\"FLEXLAYOUT__EDGE_RECT_LEFT\"] = \"flexlayout__edge_rect_left\";\n  CLASSES2[\"FLEXLAYOUT__EDGE_RECT_BOTTOM\"] = \"flexlayout__edge_rect_bottom\";\n  CLASSES2[\"FLEXLAYOUT__EDGE_RECT_RIGHT\"] = \"flexlayout__edge_rect_right\";\n  CLASSES2[\"FLEXLAYOUT__ERROR_BOUNDARY_CONTAINER\"] = \"flexlayout__error_boundary_container\";\n  CLASSES2[\"FLEXLAYOUT__ERROR_BOUNDARY_CONTENT\"] = \"flexlayout__error_boundary_content\";\n  CLASSES2[\"FLEXLAYOUT__FLOATING_WINDOW_CONTENT\"] = \"flexlayout__floating_window_content\";\n  CLASSES2[\"FLEXLAYOUT__LAYOUT\"] = \"flexlayout__layout\";\n  CLASSES2[\"FLEXLAYOUT__LAYOUT_MOVEABLES\"] = \"flexlayout__layout_moveables\";\n  CLASSES2[\"FLEXLAYOUT__LAYOUT_OVERLAY\"] = \"flexlayout__layout_overlay\";\n  CLASSES2[\"FLEXLAYOUT__LAYOUT_TAB_STAMPS\"] = \"flexlayout__layout_tab_stamps\";\n  CLASSES2[\"FLEXLAYOUT__LAYOUT_MAIN\"] = \"flexlayout__layout_main\";\n  CLASSES2[\"FLEXLAYOUT__LAYOUT_BORDER_CONTAINER\"] = \"flexlayout__layout_border_container\";\n  CLASSES2[\"FLEXLAYOUT__LAYOUT_BORDER_CONTAINER_INNER\"] = \"flexlayout__layout_border_container_inner\";\n  CLASSES2[\"FLEXLAYOUT__OUTLINE_RECT\"] = \"flexlayout__outline_rect\";\n  CLASSES2[\"FLEXLAYOUT__OUTLINE_RECT_EDGE\"] = \"flexlayout__outline_rect_edge\";\n  CLASSES2[\"FLEXLAYOUT__SPLITTER\"] = \"flexlayout__splitter\";\n  CLASSES2[\"FLEXLAYOUT__SPLITTER_EXTRA\"] = \"flexlayout__splitter_extra\";\n  CLASSES2[\"FLEXLAYOUT__SPLITTER_\"] = \"flexlayout__splitter_\";\n  CLASSES2[\"FLEXLAYOUT__SPLITTER_BORDER\"] = \"flexlayout__splitter_border\";\n  CLASSES2[\"FLEXLAYOUT__SPLITTER_DRAG\"] = \"flexlayout__splitter_drag\";\n  CLASSES2[\"FLEXLAYOUT__SPLITTER_HANDLE\"] = \"flexlayout__splitter_handle\";\n  CLASSES2[\"FLEXLAYOUT__SPLITTER_HANDLE_HORZ\"] = \"flexlayout__splitter_handle_horz\";\n  CLASSES2[\"FLEXLAYOUT__SPLITTER_HANDLE_VERT\"] = \"flexlayout__splitter_handle_vert\";\n  CLASSES2[\"FLEXLAYOUT__ROW\"] = \"flexlayout__row\";\n  CLASSES2[\"FLEXLAYOUT__TAB\"] = \"flexlayout__tab\";\n  CLASSES2[\"FLEXLAYOUT__TAB_POSITION\"] = \"flexlayout__tab_position\";\n  CLASSES2[\"FLEXLAYOUT__TAB_MOVEABLE\"] = \"flexlayout__tab_moveable\";\n  CLASSES2[\"FLEXLAYOUT__TAB_OVERLAY\"] = \"flexlayout__tab_overlay\";\n  CLASSES2[\"FLEXLAYOUT__TABSET\"] = \"flexlayout__tabset\";\n  CLASSES2[\"FLEXLAYOUT__TABSET_CONTAINER\"] = \"flexlayout__tabset_container\";\n  CLASSES2[\"FLEXLAYOUT__TABSET_HEADER\"] = \"flexlayout__tabset_header\";\n  CLASSES2[\"FLEXLAYOUT__TABSET_HEADER_CONTENT\"] = \"flexlayout__tabset_header_content\";\n  CLASSES2[\"FLEXLAYOUT__TABSET_MAXIMIZED\"] = \"flexlayout__tabset-maximized\";\n  CLASSES2[\"FLEXLAYOUT__TABSET_SELECTED\"] = \"flexlayout__tabset-selected\";\n  CLASSES2[\"FLEXLAYOUT__TABSET_TAB_DIVIDER\"] = \"flexlayout__tabset_tab_divider\";\n  CLASSES2[\"FLEXLAYOUT__TABSET_CONTENT\"] = \"flexlayout__tabset_content\";\n  CLASSES2[\"FLEXLAYOUT__TABSET_TABBAR_INNER\"] = \"flexlayout__tabset_tabbar_inner\";\n  CLASSES2[\"FLEXLAYOUT__TABSET_TABBAR_INNER_\"] = \"flexlayout__tabset_tabbar_inner_\";\n  CLASSES2[\"FLEXLAYOUT__TABSET_LEADING\"] = \"flexlayout__tabset_leading\";\n  CLASSES2[\"FLEXLAYOUT__TABSET_TABBAR_INNER_TAB_CONTAINER\"] = \"flexlayout__tabset_tabbar_inner_tab_container\";\n  CLASSES2[\"FLEXLAYOUT__TABSET_TABBAR_INNER_TAB_CONTAINER_\"] = \"flexlayout__tabset_tabbar_inner_tab_container_\";\n  CLASSES2[\"FLEXLAYOUT__TABSET_TABBAR_OUTER\"] = \"flexlayout__tabset_tabbar_outer\";\n  CLASSES2[\"FLEXLAYOUT__TABSET_TABBAR_OUTER_\"] = \"flexlayout__tabset_tabbar_outer_\";\n  CLASSES2[\"FLEXLAYOUT__TAB_BORDER\"] = \"flexlayout__tab_border\";\n  CLASSES2[\"FLEXLAYOUT__TAB_BORDER_\"] = \"flexlayout__tab_border_\";\n  CLASSES2[\"FLEXLAYOUT__TAB_BUTTON\"] = \"flexlayout__tab_button\";\n  CLASSES2[\"FLEXLAYOUT__TAB_BUTTON_STRETCH\"] = \"flexlayout__tab_button_stretch\";\n  CLASSES2[\"FLEXLAYOUT__TAB_BUTTON_CONTENT\"] = \"flexlayout__tab_button_content\";\n  CLASSES2[\"FLEXLAYOUT__TAB_BUTTON_LEADING\"] = \"flexlayout__tab_button_leading\";\n  CLASSES2[\"FLEXLAYOUT__TAB_BUTTON_OVERFLOW\"] = \"flexlayout__tab_button_overflow\";\n  CLASSES2[\"FLEXLAYOUT__TAB_BUTTON_OVERFLOW_COUNT\"] = \"flexlayout__tab_button_overflow_count\";\n  CLASSES2[\"FLEXLAYOUT__TAB_BUTTON_TEXTBOX\"] = \"flexlayout__tab_button_textbox\";\n  CLASSES2[\"FLEXLAYOUT__TAB_BUTTON_TRAILING\"] = \"flexlayout__tab_button_trailing\";\n  CLASSES2[\"FLEXLAYOUT__TAB_BUTTON_STAMP\"] = \"flexlayout__tab_button_stamp\";\n  CLASSES2[\"FLEXLAYOUT__TAB_TOOLBAR\"] = \"flexlayout__tab_toolbar\";\n  CLASSES2[\"FLEXLAYOUT__TAB_TOOLBAR_BUTTON\"] = \"flexlayout__tab_toolbar_button\";\n  CLASSES2[\"FLEXLAYOUT__TAB_TOOLBAR_ICON\"] = \"flexlayout__tab_toolbar_icon\";\n  CLASSES2[\"FLEXLAYOUT__TAB_TOOLBAR_BUTTON_\"] = \"flexlayout__tab_toolbar_button-\";\n  CLASSES2[\"FLEXLAYOUT__TAB_TOOLBAR_BUTTON_FLOAT\"] = \"flexlayout__tab_toolbar_button-float\";\n  CLASSES2[\"FLEXLAYOUT__TAB_TOOLBAR_STICKY_BUTTONS_CONTAINER\"] = \"flexlayout__tab_toolbar_sticky_buttons_container\";\n  CLASSES2[\"FLEXLAYOUT__TAB_TOOLBAR_BUTTON_CLOSE\"] = \"flexlayout__tab_toolbar_button-close\";\n  CLASSES2[\"FLEXLAYOUT__POPUP_MENU_CONTAINER\"] = \"flexlayout__popup_menu_container\";\n  CLASSES2[\"FLEXLAYOUT__POPUP_MENU_ITEM\"] = \"flexlayout__popup_menu_item\";\n  CLASSES2[\"FLEXLAYOUT__POPUP_MENU_ITEM__SELECTED\"] = \"flexlayout__popup_menu_item--selected\";\n  CLASSES2[\"FLEXLAYOUT__POPUP_MENU\"] = \"flexlayout__popup_menu\";\n  CLASSES2[\"FLEXLAYOUT__MINI_SCROLLBAR\"] = \"flexlayout__mini_scrollbar\";\n  CLASSES2[\"FLEXLAYOUT__MINI_SCROLLBAR_CONTAINER\"] = \"flexlayout__mini_scrollbar_container\";\n  return CLASSES2;\n})(CLASSES || {});\nclass Action {\n  constructor(type, data) {\n    __publicField(this, \"type\");\n    __publicField(this, \"data\");\n    this.type = type;\n    this.data = data;\n  }\n}\nconst _Actions = class _Actions {\n  /**\n   * Adds a tab node to the given tabset node\n   * @param json the json for the new tab node e.g {type:\"tab\", component:\"table\"}\n   * @param toNodeId the new tab node will be added to the tabset with this node id\n   * @param location the location where the new tab will be added, one of the DockLocation enum values.\n   * @param index for docking to the center this value is the index of the tab, use -1 to add to the end.\n   * @param select (optional) whether to select the new tab, overriding autoSelectTab\n   * @returns {Action} the action\n   */\n  static addNode(json, toNodeId, location, index, select) {\n    return new Action(_Actions.ADD_NODE, {\n      json,\n      toNode: toNodeId,\n      location: location.getName(),\n      index,\n      select\n    });\n  }\n  /**\n   * Moves a node (tab or tabset) from one location to another\n   * @param fromNodeId the id of the node to move\n   * @param toNodeId the id of the node to receive the moved node\n   * @param location the location where the moved node will be added, one of the DockLocation enum values.\n   * @param index for docking to the center this value is the index of the tab, use -1 to add to the end.\n   * @param select (optional) whether to select the moved tab(s) in new tabset, overriding autoSelectTab\n   * @returns {Action} the action\n   */\n  static moveNode(fromNodeId, toNodeId, location, index, select) {\n    return new Action(_Actions.MOVE_NODE, {\n      fromNode: fromNodeId,\n      toNode: toNodeId,\n      location: location.getName(),\n      index,\n      select\n    });\n  }\n  /**\n   * Deletes a tab node from the layout\n   * @param tabNodeId the id of the tab node to delete\n   * @returns {Action} the action\n   */\n  static deleteTab(tabNodeId) {\n    return new Action(_Actions.DELETE_TAB, { node: tabNodeId });\n  }\n  /**\n   * Deletes a tabset node and all it's child tab nodes from the layout\n   * @param tabsetNodeId the id of the tabset node to delete\n   * @returns {Action} the action\n   */\n  static deleteTabset(tabsetNodeId) {\n    return new Action(_Actions.DELETE_TABSET, { node: tabsetNodeId });\n  }\n  /**\n   * Change the given nodes tab text\n   * @param tabNodeId the id of the node to rename\n   * @param text the test of the tab\n   * @returns {Action} the action\n   */\n  static renameTab(tabNodeId, text) {\n    return new Action(_Actions.RENAME_TAB, { node: tabNodeId, text });\n  }\n  /**\n   * Selects the given tab in its parent tabset\n   * @param tabNodeId the id of the node to set selected\n   * @returns {Action} the action\n   */\n  static selectTab(tabNodeId) {\n    return new Action(_Actions.SELECT_TAB, { tabNode: tabNodeId });\n  }\n  /**\n   * Set the given tabset node as the active tabset\n   * @param tabsetNodeId the id of the tabset node to set as active\n   * @returns {Action} the action\n   */\n  static setActiveTabset(tabsetNodeId, windowId) {\n    return new Action(_Actions.SET_ACTIVE_TABSET, { tabsetNode: tabsetNodeId, windowId });\n  }\n  /**\n   * Adjust the weights of a row, used when the splitter is moved\n   * @param nodeId the row node whose childrens weights are being adjusted\n   * @param weights an array of weights to be applied to the children \n   * @returns {Action} the action\n   */\n  static adjustWeights(nodeId, weights) {\n    return new Action(_Actions.ADJUST_WEIGHTS, { nodeId, weights });\n  }\n  static adjustBorderSplit(nodeId, pos) {\n    return new Action(_Actions.ADJUST_BORDER_SPLIT, { node: nodeId, pos });\n  }\n  /**\n   * Maximizes the given tabset\n   * @param tabsetNodeId the id of the tabset to maximize\n   * @returns {Action} the action\n   */\n  static maximizeToggle(tabsetNodeId, windowId) {\n    return new Action(_Actions.MAXIMIZE_TOGGLE, { node: tabsetNodeId, windowId });\n  }\n  /**\n   * Updates the global model jsone attributes\n   * @param attributes the json for the model attributes to update (merge into the existing attributes)\n   * @returns {Action} the action\n   */\n  static updateModelAttributes(attributes) {\n    return new Action(_Actions.UPDATE_MODEL_ATTRIBUTES, { json: attributes });\n  }\n  /**\n   * Updates the given nodes json attributes\n   * @param nodeId the id of the node to update\n   * @param attributes the json attributes to update (merge with the existing attributes)\n   * @returns {Action} the action\n   */\n  static updateNodeAttributes(nodeId, attributes) {\n    return new Action(_Actions.UPDATE_NODE_ATTRIBUTES, { node: nodeId, json: attributes });\n  }\n  /**\n   * Pops out the given tab node into a new browser window\n   * @param nodeId the tab node to popout\n   * @returns \n   */\n  static popoutTab(nodeId) {\n    return new Action(_Actions.POPOUT_TAB, { node: nodeId });\n  }\n  /**\n   * Pops out the given tab set node into a new browser window\n   * @param nodeId the tab set node to popout\n   * @returns \n   */\n  static popoutTabset(nodeId) {\n    return new Action(_Actions.POPOUT_TABSET, { node: nodeId });\n  }\n  /**\n   * Closes the popout window\n   * @param windowId the id of the popout window to close\n   * @returns \n   */\n  static closeWindow(windowId) {\n    return new Action(_Actions.CLOSE_WINDOW, { windowId });\n  }\n  /**\n   * Creates a new empty popout window with the given layout\n   * @param layout the json layout for the new window\n   * @param rect the window rectangle in screen coordinates\n   * @returns \n   */\n  static createWindow(layout, rect) {\n    return new Action(_Actions.CREATE_WINDOW, { layout, rect });\n  }\n};\n__publicField(_Actions, \"ADD_NODE\", \"FlexLayout_AddNode\");\n__publicField(_Actions, \"MOVE_NODE\", \"FlexLayout_MoveNode\");\n__publicField(_Actions, \"DELETE_TAB\", \"FlexLayout_DeleteTab\");\n__publicField(_Actions, \"DELETE_TABSET\", \"FlexLayout_DeleteTabset\");\n__publicField(_Actions, \"RENAME_TAB\", \"FlexLayout_RenameTab\");\n__publicField(_Actions, \"SELECT_TAB\", \"FlexLayout_SelectTab\");\n__publicField(_Actions, \"SET_ACTIVE_TABSET\", \"FlexLayout_SetActiveTabset\");\n__publicField(_Actions, \"ADJUST_WEIGHTS\", \"FlexLayout_AdjustWeights\");\n__publicField(_Actions, \"ADJUST_BORDER_SPLIT\", \"FlexLayout_AdjustBorderSplit\");\n__publicField(_Actions, \"MAXIMIZE_TOGGLE\", \"FlexLayout_MaximizeToggle\");\n__publicField(_Actions, \"UPDATE_MODEL_ATTRIBUTES\", \"FlexLayout_UpdateModelAttributes\");\n__publicField(_Actions, \"UPDATE_NODE_ATTRIBUTES\", \"FlexLayout_UpdateNodeAttributes\");\n__publicField(_Actions, \"POPOUT_TAB\", \"FlexLayout_PopoutTab\");\n__publicField(_Actions, \"POPOUT_TABSET\", \"FlexLayout_PopoutTabset\");\n__publicField(_Actions, \"CLOSE_WINDOW\", \"FlexLayout_CloseWindow\");\n__publicField(_Actions, \"CREATE_WINDOW\", \"FlexLayout_CreateWindow\");\nlet Actions = _Actions;\nclass Attribute {\n  constructor(name, modelName, defaultValue, alwaysWriteJson) {\n    __publicField(this, \"name\");\n    __publicField(this, \"alias\");\n    __publicField(this, \"modelName\");\n    __publicField(this, \"pairedAttr\");\n    __publicField(this, \"pairedType\");\n    __publicField(this, \"defaultValue\");\n    __publicField(this, \"alwaysWriteJson\");\n    __publicField(this, \"type\");\n    __publicField(this, \"required\");\n    __publicField(this, \"fixed\");\n    __publicField(this, \"description\");\n    this.name = name;\n    this.alias = void 0;\n    this.modelName = modelName;\n    this.defaultValue = defaultValue;\n    this.alwaysWriteJson = alwaysWriteJson;\n    this.required = false;\n    this.fixed = false;\n    this.type = \"any\";\n  }\n  setType(value) {\n    this.type = value;\n    return this;\n  }\n  setAlias(value) {\n    this.alias = value;\n    return this;\n  }\n  setDescription(value) {\n    this.description = value;\n  }\n  setRequired() {\n    this.required = true;\n    return this;\n  }\n  setFixed() {\n    this.fixed = true;\n    return this;\n  }\n  // sets modelAttr for nodes, and nodeAttr for model\n  setpairedAttr(value) {\n    this.pairedAttr = value;\n  }\n  setPairedType(value) {\n    this.pairedType = value;\n  }\n}\n__publicField(Attribute, \"NUMBER\", \"number\");\n__publicField(Attribute, \"STRING\", \"string\");\n__publicField(Attribute, \"BOOLEAN\", \"boolean\");\nclass AttributeDefinitions {\n  constructor() {\n    __publicField(this, \"attributes\");\n    __publicField(this, \"nameToAttribute\");\n    this.attributes = [];\n    this.nameToAttribute = /* @__PURE__ */ new Map();\n  }\n  addWithAll(name, modelName, defaultValue, alwaysWriteJson) {\n    const attr = new Attribute(name, modelName, defaultValue, alwaysWriteJson);\n    this.attributes.push(attr);\n    this.nameToAttribute.set(name, attr);\n    return attr;\n  }\n  addInherited(name, modelName) {\n    return this.addWithAll(name, modelName, void 0, false);\n  }\n  add(name, defaultValue, alwaysWriteJson) {\n    return this.addWithAll(name, void 0, defaultValue, alwaysWriteJson);\n  }\n  getAttributes() {\n    return this.attributes;\n  }\n  getModelName(name) {\n    const conversion = this.nameToAttribute.get(name);\n    if (conversion !== void 0) {\n      return conversion.modelName;\n    }\n    return void 0;\n  }\n  toJson(jsonObj, obj) {\n    for (const attr of this.attributes) {\n      const fromValue = obj[attr.name];\n      if (attr.alwaysWriteJson || fromValue !== attr.defaultValue) {\n        jsonObj[attr.name] = fromValue;\n      }\n    }\n  }\n  fromJson(jsonObj, obj) {\n    for (const attr of this.attributes) {\n      let fromValue = jsonObj[attr.name];\n      if (fromValue === void 0 && attr.alias) {\n        fromValue = jsonObj[attr.alias];\n      }\n      if (fromValue === void 0) {\n        obj[attr.name] = attr.defaultValue;\n      } else {\n        obj[attr.name] = fromValue;\n      }\n    }\n  }\n  update(jsonObj, obj) {\n    for (const attr of this.attributes) {\n      if (Object.prototype.hasOwnProperty.call(jsonObj, attr.name)) {\n        const fromValue = jsonObj[attr.name];\n        if (fromValue === void 0) {\n          delete obj[attr.name];\n        } else {\n          obj[attr.name] = fromValue;\n        }\n      }\n    }\n  }\n  setDefaults(obj) {\n    for (const attr of this.attributes) {\n      obj[attr.name] = attr.defaultValue;\n    }\n  }\n  pairAttributes(type, childAttributes) {\n    for (const attr of childAttributes.attributes) {\n      if (attr.modelName && this.nameToAttribute.has(attr.modelName)) {\n        const pairedAttr = this.nameToAttribute.get(attr.modelName);\n        pairedAttr.setpairedAttr(attr);\n        attr.setpairedAttr(pairedAttr);\n        pairedAttr.setPairedType(type);\n      }\n    }\n  }\n  toTypescriptInterface(name, parentAttributes) {\n    var _a, _b;\n    const lines = [];\n    const sorted = this.attributes.sort((a, b) => a.name.localeCompare(b.name));\n    lines.push(\"export interface I\" + name + \"Attributes {\");\n    for (let i = 0; i < sorted.length; i++) {\n      const c = sorted[i];\n      let type = c.type;\n      let defaultValue = void 0;\n      let attr = c;\n      let inherited = void 0;\n      if (attr.defaultValue !== void 0) {\n        defaultValue = attr.defaultValue;\n      } else if (attr.modelName !== void 0 && parentAttributes !== void 0 && parentAttributes.nameToAttribute.get(attr.modelName) !== void 0) {\n        inherited = attr.modelName;\n        attr = parentAttributes.nameToAttribute.get(inherited);\n        defaultValue = attr.defaultValue;\n        type = attr.type;\n      }\n      const defValue = JSON.stringify(defaultValue);\n      const required = attr.required ? \"\" : \"?\";\n      let sb = \"\t/**\\n\t  \";\n      if (c.description) {\n        sb += c.description;\n      } else if (c.pairedType && ((_a = c.pairedAttr) == null ? void 0 : _a.description)) {\n        sb += `Value for ${c.pairedType} attribute ${c.pairedAttr.name} if not overridden`;\n        sb += \"\\n\\n\t  \";\n        sb += (_b = c.pairedAttr) == null ? void 0 : _b.description;\n      }\n      sb += \"\\n\\n\t  \";\n      if (c.fixed) {\n        sb += `Fixed value: ${defValue}`;\n      } else if (inherited) {\n        sb += `Default: inherited from Global attribute ${c.modelName} (default ${defValue})`;\n      } else {\n        sb += `Default: ${defValue}`;\n      }\n      sb += \"\\n\t */\";\n      lines.push(sb);\n      lines.push(\"\t\" + c.name + required + \": \" + type + \";\\n\");\n    }\n    lines.push(\"}\");\n    return lines.join(\"\\n\");\n  }\n}\nclass DropInfo {\n  constructor(node, rect, location, index, className) {\n    __publicField(this, \"node\");\n    __publicField(this, \"rect\");\n    __publicField(this, \"location\");\n    __publicField(this, \"index\");\n    __publicField(this, \"className\");\n    this.node = node;\n    this.rect = rect;\n    this.location = location;\n    this.index = index;\n    this.className = className;\n  }\n}\nclass BorderSet {\n  /** @internal */\n  constructor(_model) {\n    /** @internal */\n    __publicField(this, \"borders\");\n    /** @internal */\n    __publicField(this, \"borderMap\");\n    /** @internal */\n    __publicField(this, \"layoutHorizontal\");\n    this.borders = [];\n    this.borderMap = /* @__PURE__ */ new Map();\n    this.layoutHorizontal = true;\n  }\n  /** @internal */\n  static fromJson(json, model) {\n    const borderSet = new BorderSet(model);\n    borderSet.borders = json.map((borderJson) => BorderNode.fromJson(borderJson, model));\n    for (const border of borderSet.borders) {\n      borderSet.borderMap.set(border.getLocation(), border);\n    }\n    return borderSet;\n  }\n  toJson() {\n    return this.borders.map((borderNode) => borderNode.toJson());\n  }\n  /** @internal */\n  getLayoutHorizontal() {\n    return this.layoutHorizontal;\n  }\n  /** @internal */\n  getBorders() {\n    return this.borders;\n  }\n  /** @internal */\n  getBorderMap() {\n    return this.borderMap;\n  }\n  /** @internal */\n  forEachNode(fn) {\n    for (const borderNode of this.borders) {\n      fn(borderNode, 0);\n      for (const node of borderNode.getChildren()) {\n        node.forEachNode(fn, 1);\n      }\n    }\n  }\n  /** @internal */\n  setPaths() {\n    for (const borderNode of this.borders) {\n      const path = \"/border/\" + borderNode.getLocation().getName();\n      borderNode.setPath(path);\n      let i = 0;\n      for (const node of borderNode.getChildren()) {\n        node.setPath(path + \"/t\" + i);\n        i++;\n      }\n    }\n  }\n  /** @internal */\n  findDropTargetNode(dragNode, x, y) {\n    for (const border of this.borders) {\n      if (border.isShowing()) {\n        const dropInfo = border.canDrop(dragNode, x, y);\n        if (dropInfo !== void 0) {\n          return dropInfo;\n        }\n      }\n    }\n    return void 0;\n  }\n}\nclass Node {\n  /** @internal */\n  constructor(_model) {\n    /** @internal */\n    __publicField(this, \"model\");\n    /** @internal */\n    __publicField(this, \"attributes\");\n    /** @internal */\n    __publicField(this, \"parent\");\n    /** @internal */\n    __publicField(this, \"children\");\n    /** @internal */\n    __publicField(this, \"rect\");\n    /** @internal */\n    __publicField(this, \"path\");\n    /** @internal */\n    __publicField(this, \"listeners\");\n    this.model = _model;\n    this.attributes = {};\n    this.children = [];\n    this.rect = Rect.empty();\n    this.listeners = /* @__PURE__ */ new Map();\n    this.path = \"\";\n  }\n  getId() {\n    let id = this.attributes.id;\n    if (id !== void 0) {\n      return id;\n    }\n    id = this.model.nextUniqueId();\n    this.setId(id);\n    return id;\n  }\n  getModel() {\n    return this.model;\n  }\n  getType() {\n    return this.attributes.type;\n  }\n  getParent() {\n    return this.parent;\n  }\n  getChildren() {\n    return this.children;\n  }\n  getRect() {\n    return this.rect;\n  }\n  getPath() {\n    return this.path;\n  }\n  getOrientation() {\n    if (this.parent === void 0) {\n      return this.model.isRootOrientationVertical() ? Orientation.VERT : Orientation.HORZ;\n    } else {\n      return Orientation.flip(this.parent.getOrientation());\n    }\n  }\n  // event can be: resize, visibility, maximize (on tabset), close\n  setEventListener(event, callback) {\n    this.listeners.set(event, callback);\n  }\n  removeEventListener(event) {\n    this.listeners.delete(event);\n  }\n  /** @internal */\n  setId(id) {\n    this.attributes.id = id;\n  }\n  /** @internal */\n  fireEvent(event, params) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event)(params);\n    }\n  }\n  /** @internal */\n  getAttr(name) {\n    let val = this.attributes[name];\n    if (val === void 0) {\n      const modelName = this.getAttributeDefinitions().getModelName(name);\n      if (modelName !== void 0) {\n        val = this.model.getAttribute(modelName);\n      }\n    }\n    return val;\n  }\n  /** @internal */\n  forEachNode(fn, level) {\n    fn(this, level);\n    level++;\n    for (const node of this.children) {\n      node.forEachNode(fn, level);\n    }\n  }\n  /** @internal */\n  setPaths(path) {\n    let i = 0;\n    for (const node of this.children) {\n      let newPath = path;\n      if (node.getType() === \"row\") {\n        newPath += \"/r\" + i;\n      } else if (node.getType() === \"tabset\") {\n        newPath += \"/ts\" + i;\n      } else if (node.getType() === \"tab\") {\n        newPath += \"/t\" + i;\n      }\n      node.path = newPath;\n      node.setPaths(newPath);\n      i++;\n    }\n  }\n  /** @internal */\n  setParent(parent) {\n    this.parent = parent;\n  }\n  /** @internal */\n  setRect(rect) {\n    this.rect = rect;\n  }\n  /** @internal */\n  setPath(path) {\n    this.path = path;\n  }\n  /** @internal */\n  setWeight(weight) {\n    this.attributes.weight = weight;\n  }\n  /** @internal */\n  setSelected(index) {\n    this.attributes.selected = index;\n  }\n  /** @internal */\n  findDropTargetNode(windowId, dragNode, x, y) {\n    let rtn;\n    if (this.rect.contains(x, y)) {\n      if (this.model.getMaximizedTabset(windowId) !== void 0) {\n        rtn = this.model.getMaximizedTabset(windowId).canDrop(dragNode, x, y);\n      } else {\n        rtn = this.canDrop(dragNode, x, y);\n        if (rtn === void 0) {\n          if (this.children.length !== 0) {\n            for (const child of this.children) {\n              rtn = child.findDropTargetNode(windowId, dragNode, x, y);\n              if (rtn !== void 0) {\n                break;\n              }\n            }\n          }\n        }\n      }\n    }\n    return rtn;\n  }\n  /** @internal */\n  canDrop(dragNode, x, y) {\n    return void 0;\n  }\n  /** @internal */\n  canDockInto(dragNode, dropInfo) {\n    if (dropInfo != null) {\n      if (dropInfo.location === DockLocation.CENTER && dropInfo.node.isEnableDrop() === false) {\n        return false;\n      }\n      if (dropInfo.location === DockLocation.CENTER && dragNode.getType() === \"tabset\" && dragNode.getName() !== void 0) {\n        return false;\n      }\n      if (dropInfo.location !== DockLocation.CENTER && dropInfo.node.isEnableDivide() === false) {\n        return false;\n      }\n      if (this.model.getOnAllowDrop()) {\n        return this.model.getOnAllowDrop()(dragNode, dropInfo);\n      }\n    }\n    return true;\n  }\n  /** @internal */\n  removeChild(childNode) {\n    const pos = this.children.indexOf(childNode);\n    if (pos !== -1) {\n      this.children.splice(pos, 1);\n    }\n    return pos;\n  }\n  /** @internal */\n  addChild(childNode, pos) {\n    if (pos != null) {\n      this.children.splice(pos, 0, childNode);\n    } else {\n      this.children.push(childNode);\n      pos = this.children.length - 1;\n    }\n    childNode.parent = this;\n    return pos;\n  }\n  /** @internal */\n  removeAll() {\n    this.children = [];\n  }\n  /** @internal */\n  styleWithPosition(style2) {\n    if (style2 == null) {\n      style2 = {};\n    }\n    return this.rect.styleWithPosition(style2);\n  }\n  /** @internal */\n  isEnableDivide() {\n    return true;\n  }\n  /** @internal */\n  toAttributeString() {\n    return JSON.stringify(this.attributes, void 0, \"\t\");\n  }\n}\nconst _TabNode = class _TabNode extends Node {\n  /** @internal */\n  constructor(model, json, addToModel = true) {\n    super(model);\n    /** @internal */\n    __publicField(this, \"tabRect\", Rect.empty());\n    /** @internal */\n    __publicField(this, \"moveableElement\");\n    /** @internal */\n    __publicField(this, \"tabStamp\");\n    /** @internal */\n    __publicField(this, \"renderedName\");\n    /** @internal */\n    __publicField(this, \"extra\");\n    /** @internal */\n    __publicField(this, \"visible\");\n    /** @internal */\n    __publicField(this, \"rendered\");\n    /** @internal */\n    __publicField(this, \"scrollTop\");\n    /** @internal */\n    __publicField(this, \"scrollLeft\");\n    this.extra = {};\n    this.moveableElement = null;\n    this.tabStamp = null;\n    this.rendered = false;\n    this.visible = false;\n    _TabNode.attributeDefinitions.fromJson(json, this.attributes);\n    if (addToModel === true) {\n      model.addNode(this);\n    }\n  }\n  /** @internal */\n  static fromJson(json, model, addToModel = true) {\n    const newLayoutNode = new _TabNode(model, json, addToModel);\n    return newLayoutNode;\n  }\n  getName() {\n    return this.getAttr(\"name\");\n  }\n  getHelpText() {\n    return this.getAttr(\"helpText\");\n  }\n  getComponent() {\n    return this.getAttr(\"component\");\n  }\n  getWindowId() {\n    if (this.parent instanceof TabSetNode) {\n      return this.parent.getWindowId();\n    }\n    return Model.MAIN_WINDOW_ID;\n  }\n  getWindow() {\n    const layoutWindow = this.model.getwindowsMap().get(this.getWindowId());\n    if (layoutWindow) {\n      return layoutWindow.window;\n    }\n    return void 0;\n  }\n  /**\n   * Returns the config attribute that can be used to store node specific data that\n   * WILL be saved to the json. The config attribute should be changed via the action Actions.updateNodeAttributes rather\n   * than directly, for example:\n   * this.state.model.doAction(\n   *   FlexLayout.Actions.updateNodeAttributes(node.getId(), {config:myConfigObject}));\n   */\n  getConfig() {\n    return this.attributes.config;\n  }\n  /**\n   * Returns an object that can be used to store transient node specific data that will\n   * NOT be saved in the json.\n   */\n  getExtraData() {\n    return this.extra;\n  }\n  isPoppedOut() {\n    return this.getWindowId() !== Model.MAIN_WINDOW_ID;\n  }\n  isSelected() {\n    return this.getParent().getSelectedNode() === this;\n  }\n  getIcon() {\n    return this.getAttr(\"icon\");\n  }\n  isEnableClose() {\n    return this.getAttr(\"enableClose\");\n  }\n  getCloseType() {\n    return this.getAttr(\"closeType\");\n  }\n  isEnablePopout() {\n    return this.getAttr(\"enablePopout\");\n  }\n  isEnablePopoutIcon() {\n    return this.getAttr(\"enablePopoutIcon\");\n  }\n  isEnablePopoutOverlay() {\n    return this.getAttr(\"enablePopoutOverlay\");\n  }\n  isEnableDrag() {\n    return this.getAttr(\"enableDrag\");\n  }\n  isEnableRename() {\n    return this.getAttr(\"enableRename\");\n  }\n  isEnableWindowReMount() {\n    return this.getAttr(\"enableWindowReMount\");\n  }\n  getClassName() {\n    return this.getAttr(\"className\");\n  }\n  getContentClassName() {\n    return this.getAttr(\"contentClassName\");\n  }\n  getTabSetClassName() {\n    return this.getAttr(\"tabsetClassName\");\n  }\n  isEnableRenderOnDemand() {\n    return this.getAttr(\"enableRenderOnDemand\");\n  }\n  getMinWidth() {\n    return this.getAttr(\"minWidth\");\n  }\n  getMinHeight() {\n    return this.getAttr(\"minHeight\");\n  }\n  getMaxWidth() {\n    return this.getAttr(\"maxWidth\");\n  }\n  getMaxHeight() {\n    return this.getAttr(\"maxHeight\");\n  }\n  isVisible() {\n    return this.visible;\n  }\n  toJson() {\n    const json = {};\n    _TabNode.attributeDefinitions.toJson(json, this.attributes);\n    return json;\n  }\n  /** @internal */\n  saveScrollPosition() {\n    if (this.moveableElement) {\n      this.scrollLeft = this.moveableElement.scrollLeft;\n      this.scrollTop = this.moveableElement.scrollTop;\n    }\n  }\n  /** @internal */\n  restoreScrollPosition() {\n    if (this.scrollTop) {\n      requestAnimationFrame(() => {\n        if (this.moveableElement) {\n          if (this.scrollTop) {\n            this.moveableElement.scrollTop = this.scrollTop;\n            this.moveableElement.scrollLeft = this.scrollLeft;\n          }\n        }\n      });\n    }\n  }\n  /** @internal */\n  setRect(rect) {\n    if (!rect.equals(this.rect)) {\n      this.fireEvent(\"resize\", { rect });\n      this.rect = rect;\n    }\n  }\n  /** @internal */\n  setVisible(visible) {\n    if (visible !== this.visible) {\n      this.visible = visible;\n      this.fireEvent(\"visibility\", { visible });\n    }\n  }\n  /** @internal */\n  getScrollTop() {\n    return this.scrollTop;\n  }\n  /** @internal */\n  setScrollTop(scrollTop) {\n    this.scrollTop = scrollTop;\n  }\n  /** @internal */\n  getScrollLeft() {\n    return this.scrollLeft;\n  }\n  /** @internal */\n  setScrollLeft(scrollLeft) {\n    this.scrollLeft = scrollLeft;\n  }\n  /** @internal */\n  isRendered() {\n    return this.rendered;\n  }\n  /** @internal */\n  setRendered(rendered) {\n    this.rendered = rendered;\n  }\n  /** @internal */\n  getTabRect() {\n    return this.tabRect;\n  }\n  /** @internal */\n  setTabRect(rect) {\n    this.tabRect = rect;\n  }\n  /** @internal */\n  getTabStamp() {\n    return this.tabStamp;\n  }\n  /** @internal */\n  setTabStamp(stamp) {\n    this.tabStamp = stamp;\n  }\n  /** @internal */\n  getMoveableElement() {\n    return this.moveableElement;\n  }\n  /** @internal */\n  setMoveableElement(element) {\n    this.moveableElement = element;\n  }\n  /** @internal */\n  setRenderedName(name) {\n    this.renderedName = name;\n  }\n  /** @internal */\n  getNameForOverflowMenu() {\n    const altName = this.getAttr(\"altName\");\n    if (altName !== void 0) {\n      return altName;\n    }\n    return this.renderedName;\n  }\n  /** @internal */\n  setName(name) {\n    this.attributes.name = name;\n  }\n  /** @internal */\n  delete() {\n    this.parent.remove(this);\n    this.fireEvent(\"close\", {});\n  }\n  /** @internal */\n  updateAttrs(json) {\n    _TabNode.attributeDefinitions.update(json, this.attributes);\n  }\n  /** @internal */\n  getAttributeDefinitions() {\n    return _TabNode.attributeDefinitions;\n  }\n  /** @internal */\n  setBorderWidth(width) {\n    this.attributes.borderWidth = width;\n  }\n  /** @internal */\n  setBorderHeight(height) {\n    this.attributes.borderHeight = height;\n  }\n  /** @internal */\n  static getAttributeDefinitions() {\n    return _TabNode.attributeDefinitions;\n  }\n  /** @internal */\n  static createAttributeDefinitions() {\n    const attributeDefinitions = new AttributeDefinitions();\n    attributeDefinitions.add(\"type\", _TabNode.TYPE, true).setType(Attribute.STRING).setFixed();\n    attributeDefinitions.add(\"id\", void 0).setType(Attribute.STRING).setDescription(\n      `the unique id of the tab, if left undefined a uuid will be assigned`\n    );\n    attributeDefinitions.add(\"name\", \"[Unnamed Tab]\").setType(Attribute.STRING).setDescription(\n      `name of tab to be displayed in the tab button`\n    );\n    attributeDefinitions.add(\"altName\", void 0).setType(Attribute.STRING).setDescription(\n      `if there is no name specifed then this value will be used in the overflow menu`\n    );\n    attributeDefinitions.add(\"helpText\", void 0).setType(Attribute.STRING).setDescription(\n      `An optional help text for the tab to be displayed upon tab hover.`\n    );\n    attributeDefinitions.add(\"component\", void 0).setType(Attribute.STRING).setDescription(\n      `string identifying which component to run (for factory)`\n    );\n    attributeDefinitions.add(\"config\", void 0).setType(\"any\").setDescription(\n      `a place to hold json config for the hosted component`\n    );\n    attributeDefinitions.add(\"tabsetClassName\", void 0).setType(Attribute.STRING).setDescription(\n      `class applied to parent tabset when this is the only tab and it is stretched to fill the tabset`\n    );\n    attributeDefinitions.add(\"enableWindowReMount\", false).setType(Attribute.BOOLEAN).setDescription(\n      `if enabled the tab will re-mount when popped out/in`\n    );\n    attributeDefinitions.addInherited(\"enableClose\", \"tabEnableClose\").setType(Attribute.BOOLEAN).setDescription(\n      `allow user to close tab via close button`\n    );\n    attributeDefinitions.addInherited(\"closeType\", \"tabCloseType\").setType(\"ICloseType\").setDescription(\n      `see values in ICloseType`\n    );\n    attributeDefinitions.addInherited(\"enableDrag\", \"tabEnableDrag\").setType(Attribute.BOOLEAN).setDescription(\n      `allow user to drag tab to new location`\n    );\n    attributeDefinitions.addInherited(\"enableRename\", \"tabEnableRename\").setType(Attribute.BOOLEAN).setDescription(\n      `allow user to rename tabs by double clicking`\n    );\n    attributeDefinitions.addInherited(\"className\", \"tabClassName\").setType(Attribute.STRING).setDescription(\n      `class applied to tab button`\n    );\n    attributeDefinitions.addInherited(\"contentClassName\", \"tabContentClassName\").setType(Attribute.STRING).setDescription(\n      `class applied to tab content`\n    );\n    attributeDefinitions.addInherited(\"icon\", \"tabIcon\").setType(Attribute.STRING).setDescription(\n      `the tab icon`\n    );\n    attributeDefinitions.addInherited(\"enableRenderOnDemand\", \"tabEnableRenderOnDemand\").setType(Attribute.BOOLEAN).setDescription(\n      `whether to avoid rendering component until tab is visible`\n    );\n    attributeDefinitions.addInherited(\"enablePopout\", \"tabEnablePopout\").setType(Attribute.BOOLEAN).setAlias(\"enableFloat\").setDescription(\n      `enable popout (in popout capable browser)`\n    );\n    attributeDefinitions.addInherited(\"enablePopoutIcon\", \"tabEnablePopoutIcon\").setType(Attribute.BOOLEAN).setDescription(\n      `whether to show the popout icon in the tabset header if this tab enables popouts`\n    );\n    attributeDefinitions.addInherited(\"enablePopoutOverlay\", \"tabEnablePopoutOverlay\").setType(Attribute.BOOLEAN).setDescription(\n      `if this tab will not work correctly in a popout window when the main window is backgrounded (inactive)\n            then enabling this option will gray out this tab`\n    );\n    attributeDefinitions.addInherited(\"borderWidth\", \"tabBorderWidth\").setType(Attribute.NUMBER).setDescription(\n      `width when added to border, -1 will use border size`\n    );\n    attributeDefinitions.addInherited(\"borderHeight\", \"tabBorderHeight\").setType(Attribute.NUMBER).setDescription(\n      `height when added to border, -1 will use border size`\n    );\n    attributeDefinitions.addInherited(\"minWidth\", \"tabMinWidth\").setType(Attribute.NUMBER).setDescription(\n      `the min width of this tab`\n    );\n    attributeDefinitions.addInherited(\"minHeight\", \"tabMinHeight\").setType(Attribute.NUMBER).setDescription(\n      `the min height of this tab`\n    );\n    attributeDefinitions.addInherited(\"maxWidth\", \"tabMaxWidth\").setType(Attribute.NUMBER).setDescription(\n      `the max width of this tab`\n    );\n    attributeDefinitions.addInherited(\"maxHeight\", \"tabMaxHeight\").setType(Attribute.NUMBER).setDescription(\n      `the max height of this tab`\n    );\n    return attributeDefinitions;\n  }\n};\n__publicField(_TabNode, \"TYPE\", \"tab\");\n/** @internal */\n__publicField(_TabNode, \"attributeDefinitions\", _TabNode.createAttributeDefinitions());\nlet TabNode = _TabNode;\nfunction isDesktop() {\n  const desktop = typeof window !== \"undefined\" && window.matchMedia && window.matchMedia(\"(hover: hover) and (pointer: fine)\").matches;\n  return desktop;\n}\nfunction getRenderStateEx(layout, node, iconAngle) {\n  let leadingContent = void 0;\n  const titleContent = node.getName();\n  const name = node.getName();\n  if (iconAngle === void 0) {\n    iconAngle = 0;\n  }\n  if (leadingContent === void 0 && node.getIcon() !== void 0) {\n    if (iconAngle !== 0) {\n      leadingContent = /* @__PURE__ */ jsx(\"img\", { style: { width: \"1em\", height: \"1em\", transform: \"rotate(\" + iconAngle + \"deg)\" }, src: node.getIcon(), alt: \"leadingContent\" });\n    } else {\n      leadingContent = /* @__PURE__ */ jsx(\"img\", { style: { width: \"1em\", height: \"1em\" }, src: node.getIcon(), alt: \"leadingContent\" });\n    }\n  }\n  const buttons = [];\n  const renderState = { leading: leadingContent, content: titleContent, name, buttons };\n  layout.customizeTab(node, renderState);\n  node.setRenderedName(renderState.name);\n  return renderState;\n}\nfunction isAuxMouseEvent(event) {\n  let auxEvent = false;\n  if (event.nativeEvent instanceof MouseEvent) {\n    if (event.nativeEvent.button !== 0 || event.ctrlKey || event.altKey || event.metaKey || event.shiftKey) {\n      auxEvent = true;\n    }\n  }\n  return auxEvent;\n}\nfunction enablePointerOnIFrames(enable, currentDocument) {\n  const iframes = [\n    ...getElementsByTagName(\"iframe\", currentDocument),\n    ...getElementsByTagName(\"webview\", currentDocument)\n  ];\n  for (const iframe of iframes) {\n    iframe.style.pointerEvents = enable ? \"auto\" : \"none\";\n  }\n}\nfunction getElementsByTagName(tag, currentDocument) {\n  return [...currentDocument.getElementsByTagName(tag)];\n}\nfunction startDrag(doc, event, drag, dragEnd, dragCancel) {\n  event.preventDefault();\n  const pointerMove = (ev) => {\n    ev.preventDefault();\n    drag(ev.clientX, ev.clientY);\n  };\n  const pointerCancel = (ev) => {\n    ev.preventDefault();\n    dragCancel();\n  };\n  const pointerUp = () => {\n    doc.removeEventListener(\"pointermove\", pointerMove);\n    doc.removeEventListener(\"pointerup\", pointerUp);\n    doc.removeEventListener(\"pointercancel\", pointerCancel);\n    dragEnd();\n  };\n  doc.addEventListener(\"pointermove\", pointerMove);\n  doc.addEventListener(\"pointerup\", pointerUp);\n  doc.addEventListener(\"pointercancel\", pointerCancel);\n}\nfunction canDockToWindow(node) {\n  if (node instanceof TabNode) {\n    return node.isEnablePopout();\n  } else if (node instanceof TabSetNode) {\n    for (const child of node.getChildren()) {\n      if (child.isEnablePopout() === false) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return false;\n}\nfunction copyInlineStyles(source, target) {\n  const sourceStyle = source.getAttribute(\"style\");\n  const targetStyle = target.getAttribute(\"style\");\n  if (sourceStyle === targetStyle) return false;\n  if (sourceStyle) {\n    target.setAttribute(\"style\", sourceStyle);\n  } else {\n    target.removeAttribute(\"style\");\n  }\n  return true;\n}\nfunction isSafari() {\n  const userAgent = navigator.userAgent;\n  return userAgent.includes(\"Safari\") && !userAgent.includes(\"Chrome\") && !userAgent.includes(\"Chromium\");\n}\nfunction adjustSelectedIndex(parent, removedIndex) {\n  if (parent !== void 0 && (parent instanceof TabSetNode || parent instanceof BorderNode)) {\n    const selectedIndex = parent.getSelected();\n    if (selectedIndex !== -1) {\n      if (removedIndex === selectedIndex && parent.getChildren().length > 0) {\n        if (removedIndex >= parent.getChildren().length) {\n          parent.setSelected(parent.getChildren().length - 1);\n        }\n      } else if (removedIndex < selectedIndex) {\n        parent.setSelected(selectedIndex - 1);\n      } else if (removedIndex > selectedIndex) ;\n      else {\n        parent.setSelected(-1);\n      }\n    }\n  }\n}\nfunction randomUUID() {\n  return (\"10000000-1000-4000-8000\" + -1e11).replace(\n    /[018]/g,\n    (c) => (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)\n  );\n}\nconst _TabSetNode = class _TabSetNode extends Node {\n  /** @internal */\n  constructor(model, json) {\n    super(model);\n    /** @internal */\n    __publicField(this, \"tabStripRect\", Rect.empty());\n    /** @internal */\n    __publicField(this, \"contentRect\", Rect.empty());\n    /** @internal */\n    __publicField(this, \"calculatedMinHeight\");\n    /** @internal */\n    __publicField(this, \"calculatedMinWidth\");\n    /** @internal */\n    __publicField(this, \"calculatedMaxHeight\");\n    /** @internal */\n    __publicField(this, \"calculatedMaxWidth\");\n    this.calculatedMinHeight = 0;\n    this.calculatedMinWidth = 0;\n    this.calculatedMaxHeight = 0;\n    this.calculatedMaxWidth = 0;\n    _TabSetNode.attributeDefinitions.fromJson(json, this.attributes);\n    model.addNode(this);\n  }\n  /** @internal */\n  static fromJson(json, model, layoutWindow) {\n    const newLayoutNode = new _TabSetNode(model, json);\n    if (json.children != null) {\n      for (const jsonChild of json.children) {\n        const child = TabNode.fromJson(jsonChild, model);\n        newLayoutNode.addChild(child);\n      }\n    }\n    if (newLayoutNode.children.length === 0) {\n      newLayoutNode.setSelected(-1);\n    }\n    if (json.maximized && json.maximized === true) {\n      layoutWindow.maximizedTabSet = newLayoutNode;\n    }\n    if (json.active && json.active === true) {\n      layoutWindow.activeTabSet = newLayoutNode;\n    }\n    return newLayoutNode;\n  }\n  getName() {\n    return this.getAttr(\"name\");\n  }\n  isEnableActiveIcon() {\n    return this.getAttr(\"enableActiveIcon\");\n  }\n  getSelected() {\n    const selected = this.attributes.selected;\n    if (selected !== void 0) {\n      return selected;\n    }\n    return -1;\n  }\n  getSelectedNode() {\n    const selected = this.getSelected();\n    if (selected !== -1) {\n      return this.children[selected];\n    }\n    return void 0;\n  }\n  getWeight() {\n    return this.getAttr(\"weight\");\n  }\n  getAttrMinWidth() {\n    return this.getAttr(\"minWidth\");\n  }\n  getAttrMinHeight() {\n    return this.getAttr(\"minHeight\");\n  }\n  getMinWidth() {\n    return this.calculatedMinWidth;\n  }\n  getMinHeight() {\n    return this.calculatedMinHeight;\n  }\n  /** @internal */\n  getMinSize(orientation) {\n    if (orientation === Orientation.HORZ) {\n      return this.getMinWidth();\n    } else {\n      return this.getMinHeight();\n    }\n  }\n  getAttrMaxWidth() {\n    return this.getAttr(\"maxWidth\");\n  }\n  getAttrMaxHeight() {\n    return this.getAttr(\"maxHeight\");\n  }\n  getMaxWidth() {\n    return this.calculatedMaxWidth;\n  }\n  getMaxHeight() {\n    return this.calculatedMaxHeight;\n  }\n  /** @internal */\n  getMaxSize(orientation) {\n    if (orientation === Orientation.HORZ) {\n      return this.getMaxWidth();\n    } else {\n      return this.getMaxHeight();\n    }\n  }\n  /**\n   * Returns the config attribute that can be used to store node specific data that\n   * WILL be saved to the json. The config attribute should be changed via the action Actions.updateNodeAttributes rather\n   * than directly, for example:\n   * this.state.model.doAction(\n   *   FlexLayout.Actions.updateNodeAttributes(node.getId(), {config:myConfigObject}));\n   */\n  getConfig() {\n    return this.attributes.config;\n  }\n  isMaximized() {\n    return this.model.getMaximizedTabset(this.getWindowId()) === this;\n  }\n  isActive() {\n    return this.model.getActiveTabset(this.getWindowId()) === this;\n  }\n  isEnableDeleteWhenEmpty() {\n    return this.getAttr(\"enableDeleteWhenEmpty\");\n  }\n  isEnableDrop() {\n    return this.getAttr(\"enableDrop\");\n  }\n  isEnableTabWrap() {\n    return this.getAttr(\"enableTabWrap\");\n  }\n  isEnableDrag() {\n    return this.getAttr(\"enableDrag\");\n  }\n  isEnableDivide() {\n    return this.getAttr(\"enableDivide\");\n  }\n  isEnableMaximize() {\n    return this.getAttr(\"enableMaximize\");\n  }\n  isEnableClose() {\n    return this.getAttr(\"enableClose\");\n  }\n  isEnableSingleTabStretch() {\n    return this.getAttr(\"enableSingleTabStretch\");\n  }\n  isEnableTabStrip() {\n    return this.getAttr(\"enableTabStrip\");\n  }\n  isAutoSelectTab() {\n    return this.getAttr(\"autoSelectTab\");\n  }\n  isEnableTabScrollbar() {\n    return this.getAttr(\"enableTabScrollbar\");\n  }\n  getClassNameTabStrip() {\n    return this.getAttr(\"classNameTabStrip\");\n  }\n  getTabLocation() {\n    return this.getAttr(\"tabLocation\");\n  }\n  toJson() {\n    const json = {};\n    _TabSetNode.attributeDefinitions.toJson(json, this.attributes);\n    json.children = this.children.map((child) => child.toJson());\n    if (this.isActive()) {\n      json.active = true;\n    }\n    if (this.isMaximized()) {\n      json.maximized = true;\n    }\n    return json;\n  }\n  /** @internal */\n  calcMinMaxSize() {\n    this.calculatedMinHeight = this.getAttrMinHeight();\n    this.calculatedMinWidth = this.getAttrMinWidth();\n    this.calculatedMaxHeight = this.getAttrMaxHeight();\n    this.calculatedMaxWidth = this.getAttrMaxWidth();\n    for (const child of this.children) {\n      const c = child;\n      this.calculatedMinWidth = Math.max(this.calculatedMinWidth, c.getMinWidth());\n      this.calculatedMinHeight = Math.max(this.calculatedMinHeight, c.getMinHeight());\n      this.calculatedMaxWidth = Math.min(this.calculatedMaxWidth, c.getMaxWidth());\n      this.calculatedMaxHeight = Math.min(this.calculatedMaxHeight, c.getMaxHeight());\n    }\n    this.calculatedMinHeight += this.tabStripRect.height;\n    this.calculatedMaxHeight += this.tabStripRect.height;\n  }\n  /** @internal */\n  canMaximize() {\n    if (this.isEnableMaximize()) {\n      if (this.getModel().getMaximizedTabset(this.getWindowId()) === this) {\n        return true;\n      }\n      if (this.getParent() === this.getModel().getRoot(this.getWindowId()) && this.getModel().getRoot(this.getWindowId()).getChildren().length === 1) {\n        return false;\n      }\n      return true;\n    }\n    return false;\n  }\n  /** @internal */\n  setContentRect(rect) {\n    this.contentRect = rect;\n  }\n  /** @internal */\n  getContentRect() {\n    return this.contentRect;\n  }\n  /** @internal */\n  setTabStripRect(rect) {\n    this.tabStripRect = rect;\n  }\n  /** @internal */\n  setWeight(weight) {\n    this.attributes.weight = weight;\n  }\n  /** @internal */\n  setSelected(index) {\n    this.attributes.selected = index;\n  }\n  getWindowId() {\n    return this.parent.getWindowId();\n  }\n  /** @internal */\n  canDrop(dragNode, x, y) {\n    let dropInfo;\n    if (dragNode === this) {\n      const dockLocation = DockLocation.CENTER;\n      const outlineRect = this.tabStripRect;\n      dropInfo = new DropInfo(this, outlineRect, dockLocation, -1, CLASSES.FLEXLAYOUT__OUTLINE_RECT);\n    } else if (this.getWindowId() !== Model.MAIN_WINDOW_ID && !canDockToWindow(dragNode)) {\n      return void 0;\n    } else if (this.contentRect.contains(x, y)) {\n      let dockLocation = DockLocation.CENTER;\n      if (this.model.getMaximizedTabset(this.parent.getWindowId()) === void 0) {\n        dockLocation = DockLocation.getLocation(this.contentRect, x, y);\n      }\n      const outlineRect = dockLocation.getDockRect(this.rect);\n      dropInfo = new DropInfo(this, outlineRect, dockLocation, -1, CLASSES.FLEXLAYOUT__OUTLINE_RECT);\n    } else if (this.tabStripRect != null && this.tabStripRect.contains(x, y)) {\n      let r;\n      let yy;\n      let h;\n      if (this.children.length === 0) {\n        r = this.tabStripRect.clone();\n        yy = r.y + 3;\n        h = r.height - 4;\n        r.width = 2;\n      } else {\n        let child = this.children[0];\n        r = child.getTabRect();\n        yy = r.y;\n        h = r.height;\n        let p = this.tabStripRect.x;\n        let childCenter = 0;\n        for (let i = 0; i < this.children.length; i++) {\n          child = this.children[i];\n          r = child.getTabRect();\n          if (r.y !== yy) {\n            yy = r.y;\n            p = this.tabStripRect.x;\n          }\n          childCenter = r.x + r.width / 2;\n          if (p <= x && x < childCenter && r.y < y && y < r.getBottom()) {\n            const dockLocation = DockLocation.CENTER;\n            const outlineRect = new Rect(r.x - 2, r.y, 3, r.height);\n            if (this.rect.x < r.x && r.x < this.rect.getRight()) {\n              dropInfo = new DropInfo(this, outlineRect, dockLocation, i, CLASSES.FLEXLAYOUT__OUTLINE_RECT);\n              break;\n            } else {\n              return void 0;\n            }\n          }\n          p = childCenter;\n        }\n      }\n      if (dropInfo == null && r.getRight() < this.rect.getRight()) {\n        const dockLocation = DockLocation.CENTER;\n        const outlineRect = new Rect(r.getRight() - 2, yy, 3, h);\n        dropInfo = new DropInfo(this, outlineRect, dockLocation, this.children.length, CLASSES.FLEXLAYOUT__OUTLINE_RECT);\n      }\n    }\n    if (!dragNode.canDockInto(dragNode, dropInfo)) {\n      return void 0;\n    }\n    return dropInfo;\n  }\n  /** @internal */\n  delete() {\n    this.parent.removeChild(this);\n  }\n  /** @internal */\n  remove(node) {\n    const removedIndex = this.removeChild(node);\n    this.model.tidy();\n    adjustSelectedIndex(this, removedIndex);\n  }\n  /** @internal */\n  drop(dragNode, location, index, select) {\n    const dockLocation = location;\n    if (this === dragNode) {\n      return;\n    }\n    let dragParent = dragNode.getParent();\n    let fromIndex = 0;\n    if (dragParent !== void 0) {\n      fromIndex = dragParent.removeChild(dragNode);\n      if (dragParent instanceof BorderNode && dragParent.getSelected() === fromIndex) {\n        dragParent.setSelected(-1);\n      } else {\n        adjustSelectedIndex(dragParent, fromIndex);\n      }\n    }\n    if (dragNode instanceof TabNode && dragParent === this && fromIndex < index && index > 0) {\n      index--;\n    }\n    if (dockLocation === DockLocation.CENTER) {\n      let insertPos = index;\n      if (insertPos === -1) {\n        insertPos = this.children.length;\n      }\n      if (dragNode instanceof TabNode) {\n        this.addChild(dragNode, insertPos);\n        if (select || select !== false && this.isAutoSelectTab()) {\n          this.setSelected(insertPos);\n        }\n      } else if (dragNode instanceof RowNode) {\n        dragNode.forEachNode((child, level) => {\n          if (child instanceof TabNode) {\n            this.addChild(child, insertPos);\n            insertPos++;\n          }\n        }, 0);\n      } else {\n        for (let i = 0; i < dragNode.getChildren().length; i++) {\n          const child = dragNode.getChildren()[i];\n          this.addChild(child, insertPos);\n          insertPos++;\n        }\n        if (this.getSelected() === -1 && this.children.length > 0) {\n          this.setSelected(0);\n        }\n      }\n      this.model.setActiveTabset(this, this.parent.getWindowId());\n    } else {\n      let moveNode = dragNode;\n      if (dragNode instanceof TabNode) {\n        const callback = this.model.getOnCreateTabSet();\n        moveNode = new _TabSetNode(this.model, callback ? callback(dragNode) : {});\n        moveNode.addChild(dragNode);\n        dragParent = moveNode;\n      } else if (dragNode instanceof RowNode) {\n        const parent = this.getParent();\n        if (dragNode.getOrientation() === parent.getOrientation() && (location.getOrientation() === parent.getOrientation() || location === DockLocation.CENTER)) {\n          const node = new RowNode(this.model, this.getWindowId(), {});\n          node.addChild(dragNode);\n          moveNode = node;\n        }\n      } else {\n        moveNode = dragNode;\n      }\n      const parentRow = this.parent;\n      const pos = parentRow.getChildren().indexOf(this);\n      if (parentRow.getOrientation() === dockLocation.orientation) {\n        moveNode.setWeight(this.getWeight() / 2);\n        this.setWeight(this.getWeight() / 2);\n        parentRow.addChild(moveNode, pos + dockLocation.indexPlus);\n      } else {\n        const newRow = new RowNode(this.model, this.getWindowId(), {});\n        newRow.setWeight(this.getWeight());\n        newRow.addChild(this);\n        this.setWeight(50);\n        moveNode.setWeight(50);\n        newRow.addChild(moveNode, dockLocation.indexPlus);\n        parentRow.removeChild(this);\n        parentRow.addChild(newRow, pos);\n      }\n      if (moveNode instanceof _TabSetNode) {\n        this.model.setActiveTabset(moveNode, this.getWindowId());\n      }\n    }\n    this.model.tidy();\n  }\n  /** @internal */\n  updateAttrs(json) {\n    _TabSetNode.attributeDefinitions.update(json, this.attributes);\n  }\n  /** @internal */\n  getAttributeDefinitions() {\n    return _TabSetNode.attributeDefinitions;\n  }\n  /** @internal */\n  static getAttributeDefinitions() {\n    return _TabSetNode.attributeDefinitions;\n  }\n  /** @internal */\n  static createAttributeDefinitions() {\n    const attributeDefinitions = new AttributeDefinitions();\n    attributeDefinitions.add(\"type\", _TabSetNode.TYPE, true).setType(Attribute.STRING).setFixed();\n    attributeDefinitions.add(\"id\", void 0).setType(Attribute.STRING).setDescription(\n      `the unique id of the tab set, if left undefined a uuid will be assigned`\n    );\n    attributeDefinitions.add(\"weight\", 100).setType(Attribute.NUMBER).setDescription(\n      `relative weight for sizing of this tabset in parent row`\n    );\n    attributeDefinitions.add(\"selected\", 0).setType(Attribute.NUMBER).setDescription(\n      `index of selected/visible tab in tabset`\n    );\n    attributeDefinitions.add(\"name\", void 0).setType(Attribute.STRING);\n    attributeDefinitions.add(\"config\", void 0).setType(\"any\").setDescription(\n      `a place to hold json config used in your own code`\n    );\n    attributeDefinitions.addInherited(\"enableDeleteWhenEmpty\", \"tabSetEnableDeleteWhenEmpty\").setDescription(\n      `whether to delete this tabset when is has no tabs`\n    );\n    attributeDefinitions.addInherited(\"enableDrop\", \"tabSetEnableDrop\").setDescription(\n      `allow user to drag tabs into this tabset`\n    );\n    attributeDefinitions.addInherited(\"enableDrag\", \"tabSetEnableDrag\").setDescription(\n      `allow user to drag tabs out this tabset`\n    );\n    attributeDefinitions.addInherited(\"enableDivide\", \"tabSetEnableDivide\").setDescription(\n      `allow user to drag tabs to region of this tabset, splitting into new tabset`\n    );\n    attributeDefinitions.addInherited(\"enableMaximize\", \"tabSetEnableMaximize\").setDescription(\n      `allow user to maximize tabset to fill view via maximize button`\n    );\n    attributeDefinitions.addInherited(\"enableClose\", \"tabSetEnableClose\").setDescription(\n      `allow user to close tabset via a close button`\n    );\n    attributeDefinitions.addInherited(\"enableSingleTabStretch\", \"tabSetEnableSingleTabStretch\").setDescription(\n      `if the tabset has only a single tab then stretch the single tab to fill area and display in a header style`\n    );\n    attributeDefinitions.addInherited(\"classNameTabStrip\", \"tabSetClassNameTabStrip\").setDescription(\n      `a class name to apply to the tab strip`\n    );\n    attributeDefinitions.addInherited(\"enableTabStrip\", \"tabSetEnableTabStrip\").setDescription(\n      `enable tab strip and allow multiple tabs in this tabset`\n    );\n    attributeDefinitions.addInherited(\"minWidth\", \"tabSetMinWidth\").setDescription(\n      `minimum width (in px) for this tabset`\n    );\n    attributeDefinitions.addInherited(\"minHeight\", \"tabSetMinHeight\").setDescription(\n      `minimum height (in px) for this tabset`\n    );\n    attributeDefinitions.addInherited(\"maxWidth\", \"tabSetMaxWidth\").setDescription(\n      `maximum width (in px) for this tabset`\n    );\n    attributeDefinitions.addInherited(\"maxHeight\", \"tabSetMaxHeight\").setDescription(\n      `maximum height (in px) for this tabset`\n    );\n    attributeDefinitions.addInherited(\"enableTabWrap\", \"tabSetEnableTabWrap\").setDescription(\n      `wrap tabs onto multiple lines`\n    );\n    attributeDefinitions.addInherited(\"tabLocation\", \"tabSetTabLocation\").setDescription(\n      `the location of the tabs either top or bottom`\n    );\n    attributeDefinitions.addInherited(\"autoSelectTab\", \"tabSetAutoSelectTab\").setType(Attribute.BOOLEAN).setDescription(\n      `whether to select new/moved tabs in tabset`\n    );\n    attributeDefinitions.addInherited(\"enableActiveIcon\", \"tabSetEnableActiveIcon\").setType(Attribute.BOOLEAN).setDescription(\n      `whether the active icon (*) should be displayed when the tabset is active`\n    );\n    attributeDefinitions.addInherited(\"enableTabScrollbar\", \"tabSetEnableTabScrollbar\").setType(Attribute.BOOLEAN).setDescription(\n      `whether to show a mini scrollbar for the tabs`\n    );\n    return attributeDefinitions;\n  }\n};\n__publicField(_TabSetNode, \"TYPE\", \"tabset\");\n/** @internal */\n__publicField(_TabSetNode, \"attributeDefinitions\", _TabSetNode.createAttributeDefinitions());\nlet TabSetNode = _TabSetNode;\nconst _RowNode = class _RowNode extends Node {\n  /** @internal */\n  constructor(model, windowId, json) {\n    super(model);\n    /** @internal */\n    __publicField(this, \"windowId\");\n    /** @internal */\n    __publicField(this, \"minHeight\");\n    /** @internal */\n    __publicField(this, \"minWidth\");\n    /** @internal */\n    __publicField(this, \"maxHeight\");\n    /** @internal */\n    __publicField(this, \"maxWidth\");\n    this.windowId = windowId;\n    this.minHeight = DefaultMin;\n    this.minWidth = DefaultMin;\n    this.maxHeight = DefaultMax;\n    this.maxWidth = DefaultMax;\n    _RowNode.attributeDefinitions.fromJson(json, this.attributes);\n    this.normalizeWeights();\n    model.addNode(this);\n  }\n  /** @internal */\n  static fromJson(json, model, layoutWindow) {\n    const newLayoutNode = new _RowNode(model, layoutWindow.windowId, json);\n    if (json.children != null) {\n      for (const jsonChild of json.children) {\n        if (jsonChild.type === TabSetNode.TYPE) {\n          const child = TabSetNode.fromJson(jsonChild, model, layoutWindow);\n          newLayoutNode.addChild(child);\n        } else {\n          const child = _RowNode.fromJson(jsonChild, model, layoutWindow);\n          newLayoutNode.addChild(child);\n        }\n      }\n    }\n    return newLayoutNode;\n  }\n  getWeight() {\n    return this.attributes.weight;\n  }\n  toJson() {\n    const json = {};\n    _RowNode.attributeDefinitions.toJson(json, this.attributes);\n    json.children = [];\n    for (const child of this.children) {\n      json.children.push(child.toJson());\n    }\n    return json;\n  }\n  /** @internal */\n  getWindowId() {\n    return this.windowId;\n  }\n  setWindowId(windowId) {\n    this.windowId = windowId;\n  }\n  /** @internal */\n  setWeight(weight) {\n    this.attributes.weight = weight;\n  }\n  /** @internal */\n  getSplitterBounds(index) {\n    const h = this.getOrientation() === Orientation.HORZ;\n    const c = this.getChildren();\n    const ss = this.model.getSplitterSize();\n    const fr = c[0].getRect();\n    const lr = c[c.length - 1].getRect();\n    let p = h ? [fr.x, lr.getRight()] : [fr.y, lr.getBottom()];\n    const q = h ? [fr.x, lr.getRight()] : [fr.y, lr.getBottom()];\n    for (let i = 0; i < index; i++) {\n      const n = c[i];\n      p[0] += h ? n.getMinWidth() : n.getMinHeight();\n      q[0] += h ? n.getMaxWidth() : n.getMaxHeight();\n      if (i > 0) {\n        p[0] += ss;\n        q[0] += ss;\n      }\n    }\n    for (let i = c.length - 1; i >= index; i--) {\n      const n = c[i];\n      p[1] -= (h ? n.getMinWidth() : n.getMinHeight()) + ss;\n      q[1] -= (h ? n.getMaxWidth() : n.getMaxHeight()) + ss;\n    }\n    p = [Math.max(q[1], p[0]), Math.min(q[0], p[1])];\n    return p;\n  }\n  /** @internal */\n  getSplitterInitials(index) {\n    const h = this.getOrientation() === Orientation.HORZ;\n    const c = this.getChildren();\n    const ss = this.model.getSplitterSize();\n    const initialSizes = [];\n    let sum = 0;\n    for (let i = 0; i < c.length; i++) {\n      const n = c[i];\n      const r = n.getRect();\n      const s = h ? r.width : r.height;\n      initialSizes.push(s);\n      sum += s;\n    }\n    const startRect = c[index].getRect();\n    const startPosition = (h ? startRect.x : startRect.y) - ss;\n    return { initialSizes, sum, startPosition };\n  }\n  /** @internal */\n  calculateSplit(index, splitterPos, initialSizes, sum, startPosition) {\n    const h = this.getOrientation() === Orientation.HORZ;\n    const c = this.getChildren();\n    const sn = c[index];\n    const smax = h ? sn.getMaxWidth() : sn.getMaxHeight();\n    const sizes = [...initialSizes];\n    if (splitterPos < startPosition) {\n      let shift = startPosition - splitterPos;\n      let altShift = 0;\n      if (sizes[index] + shift > smax) {\n        altShift = sizes[index] + shift - smax;\n        sizes[index] = smax;\n      } else {\n        sizes[index] += shift;\n      }\n      for (let i = index - 1; i >= 0; i--) {\n        const n = c[i];\n        const m = h ? n.getMinWidth() : n.getMinHeight();\n        if (sizes[i] - shift > m) {\n          sizes[i] -= shift;\n          break;\n        } else {\n          shift -= sizes[i] - m;\n          sizes[i] = m;\n        }\n      }\n      for (let i = index + 1; i < c.length; i++) {\n        const n = c[i];\n        const m = h ? n.getMaxWidth() : n.getMaxHeight();\n        if (sizes[i] + altShift < m) {\n          sizes[i] += altShift;\n          break;\n        } else {\n          altShift -= m - sizes[i];\n          sizes[i] = m;\n        }\n      }\n    } else {\n      let shift = splitterPos - startPosition;\n      let altShift = 0;\n      if (sizes[index - 1] + shift > smax) {\n        altShift = sizes[index - 1] + shift - smax;\n        sizes[index - 1] = smax;\n      } else {\n        sizes[index - 1] += shift;\n      }\n      for (let i = index; i < c.length; i++) {\n        const n = c[i];\n        const m = h ? n.getMinWidth() : n.getMinHeight();\n        if (sizes[i] - shift > m) {\n          sizes[i] -= shift;\n          break;\n        } else {\n          shift -= sizes[i] - m;\n          sizes[i] = m;\n        }\n      }\n      for (let i = index - 1; i >= 0; i--) {\n        const n = c[i];\n        const m = h ? n.getMaxWidth() : n.getMaxHeight();\n        if (sizes[i] + altShift < m) {\n          sizes[i] += altShift;\n          break;\n        } else {\n          altShift -= m - sizes[i];\n          sizes[i] = m;\n        }\n      }\n    }\n    const weights = sizes.map((s) => Math.max(0.1, s) * 100 / sum);\n    return weights;\n  }\n  /** @internal */\n  getMinSize(orientation) {\n    if (orientation === Orientation.HORZ) {\n      return this.getMinWidth();\n    } else {\n      return this.getMinHeight();\n    }\n  }\n  /** @internal */\n  getMinWidth() {\n    return this.minWidth;\n  }\n  /** @internal */\n  getMinHeight() {\n    return this.minHeight;\n  }\n  /** @internal */\n  getMaxSize(orientation) {\n    if (orientation === Orientation.HORZ) {\n      return this.getMaxWidth();\n    } else {\n      return this.getMaxHeight();\n    }\n  }\n  /** @internal */\n  getMaxWidth() {\n    return this.maxWidth;\n  }\n  /** @internal */\n  getMaxHeight() {\n    return this.maxHeight;\n  }\n  /** @internal */\n  calcMinMaxSize() {\n    this.minHeight = DefaultMin;\n    this.minWidth = DefaultMin;\n    this.maxHeight = DefaultMax;\n    this.maxWidth = DefaultMax;\n    let first = true;\n    for (const child of this.children) {\n      const c = child;\n      c.calcMinMaxSize();\n      if (this.getOrientation() === Orientation.VERT) {\n        this.minHeight += c.getMinHeight();\n        this.maxHeight += c.getMaxHeight();\n        if (!first) {\n          this.minHeight += this.model.getSplitterSize();\n          this.maxHeight += this.model.getSplitterSize();\n        }\n        this.minWidth = Math.max(this.minWidth, c.getMinWidth());\n        this.maxWidth = Math.min(this.maxWidth, c.getMaxWidth());\n      } else {\n        this.minWidth += c.getMinWidth();\n        this.maxWidth += c.getMaxWidth();\n        if (!first) {\n          this.minWidth += this.model.getSplitterSize();\n          this.maxWidth += this.model.getSplitterSize();\n        }\n        this.minHeight = Math.max(this.minHeight, c.getMinHeight());\n        this.maxHeight = Math.min(this.maxHeight, c.getMaxHeight());\n      }\n      first = false;\n    }\n  }\n  /** @internal */\n  tidy() {\n    let i = 0;\n    while (i < this.children.length) {\n      const child = this.children[i];\n      if (child instanceof _RowNode) {\n        child.tidy();\n        const childChildren = child.getChildren();\n        if (childChildren.length === 0) {\n          this.removeChild(child);\n        } else if (childChildren.length === 1) {\n          const subchild = childChildren[0];\n          this.removeChild(child);\n          if (subchild instanceof _RowNode) {\n            let subChildrenTotal = 0;\n            const subChildChildren = subchild.getChildren();\n            for (const ssc of subChildChildren) {\n              const subsubChild = ssc;\n              subChildrenTotal += subsubChild.getWeight();\n            }\n            for (let j = 0; j < subChildChildren.length; j++) {\n              const subsubChild = subChildChildren[j];\n              subsubChild.setWeight(child.getWeight() * subsubChild.getWeight() / subChildrenTotal);\n              this.addChild(subsubChild, i + j);\n            }\n          } else {\n            subchild.setWeight(child.getWeight());\n            this.addChild(subchild, i);\n          }\n        } else {\n          i++;\n        }\n      } else if (child instanceof TabSetNode && child.getChildren().length === 0) {\n        if (child.isEnableDeleteWhenEmpty()) {\n          this.removeChild(child);\n          if (child === this.model.getMaximizedTabset(this.windowId)) {\n            this.model.setMaximizedTabset(void 0, this.windowId);\n          }\n        } else {\n          i++;\n        }\n      } else {\n        i++;\n      }\n    }\n    if (this === this.model.getRoot(this.windowId) && this.children.length === 0) {\n      const callback = this.model.getOnCreateTabSet();\n      let attrs = callback ? callback() : {};\n      attrs = { ...attrs, selected: -1 };\n      const child = new TabSetNode(this.model, attrs);\n      this.model.setActiveTabset(child, this.windowId);\n      this.addChild(child);\n    }\n  }\n  /** @internal */\n  canDrop(dragNode, x, y) {\n    const yy = y - this.rect.y;\n    const xx = x - this.rect.x;\n    const w = this.rect.width;\n    const h = this.rect.height;\n    const margin = 10;\n    const half = 50;\n    let dropInfo;\n    if (this.getWindowId() !== Model.MAIN_WINDOW_ID && !canDockToWindow(dragNode)) {\n      return void 0;\n    }\n    if (this.model.isEnableEdgeDock() && this.parent === void 0) {\n      if (x < this.rect.x + margin && yy > h / 2 - half && yy < h / 2 + half) {\n        const dockLocation = DockLocation.LEFT;\n        const outlineRect = dockLocation.getDockRect(this.rect);\n        outlineRect.width = outlineRect.width / 2;\n        dropInfo = new DropInfo(this, outlineRect, dockLocation, -1, CLASSES.FLEXLAYOUT__OUTLINE_RECT_EDGE);\n      } else if (x > this.rect.getRight() - margin && yy > h / 2 - half && yy < h / 2 + half) {\n        const dockLocation = DockLocation.RIGHT;\n        const outlineRect = dockLocation.getDockRect(this.rect);\n        outlineRect.width = outlineRect.width / 2;\n        outlineRect.x += outlineRect.width;\n        dropInfo = new DropInfo(this, outlineRect, dockLocation, -1, CLASSES.FLEXLAYOUT__OUTLINE_RECT_EDGE);\n      } else if (y < this.rect.y + margin && xx > w / 2 - half && xx < w / 2 + half) {\n        const dockLocation = DockLocation.TOP;\n        const outlineRect = dockLocation.getDockRect(this.rect);\n        outlineRect.height = outlineRect.height / 2;\n        dropInfo = new DropInfo(this, outlineRect, dockLocation, -1, CLASSES.FLEXLAYOUT__OUTLINE_RECT_EDGE);\n      } else if (y > this.rect.getBottom() - margin && xx > w / 2 - half && xx < w / 2 + half) {\n        const dockLocation = DockLocation.BOTTOM;\n        const outlineRect = dockLocation.getDockRect(this.rect);\n        outlineRect.height = outlineRect.height / 2;\n        outlineRect.y += outlineRect.height;\n        dropInfo = new DropInfo(this, outlineRect, dockLocation, -1, CLASSES.FLEXLAYOUT__OUTLINE_RECT_EDGE);\n      }\n      if (dropInfo !== void 0) {\n        if (!dragNode.canDockInto(dragNode, dropInfo)) {\n          return void 0;\n        }\n      }\n    }\n    return dropInfo;\n  }\n  /** @internal */\n  drop(dragNode, location, index) {\n    const dockLocation = location;\n    const parent = dragNode.getParent();\n    if (parent) {\n      parent.removeChild(dragNode);\n    }\n    if (parent !== void 0 && parent instanceof TabSetNode) {\n      parent.setSelected(0);\n    }\n    if (parent !== void 0 && parent instanceof BorderNode) {\n      parent.setSelected(-1);\n    }\n    let node;\n    if (dragNode instanceof TabSetNode || dragNode instanceof _RowNode) {\n      node = dragNode;\n      if (node instanceof _RowNode && node.getOrientation() === this.getOrientation() && (location.getOrientation() === this.getOrientation() || location === DockLocation.CENTER)) {\n        node = new _RowNode(this.model, this.windowId, {});\n        node.addChild(dragNode);\n      }\n    } else {\n      const callback = this.model.getOnCreateTabSet();\n      node = new TabSetNode(this.model, callback ? callback(dragNode) : {});\n      node.addChild(dragNode);\n    }\n    let size = this.children.reduce((sum, child) => {\n      return sum + child.getWeight();\n    }, 0);\n    if (size === 0) {\n      size = 100;\n    }\n    node.setWeight(size / 3);\n    const horz = !this.model.isRootOrientationVertical();\n    if (dockLocation === DockLocation.CENTER) {\n      if (index === -1) {\n        this.addChild(node, this.children.length);\n      } else {\n        this.addChild(node, index);\n      }\n    } else if (horz && dockLocation === DockLocation.LEFT || !horz && dockLocation === DockLocation.TOP) {\n      this.addChild(node, 0);\n    } else if (horz && dockLocation === DockLocation.RIGHT || !horz && dockLocation === DockLocation.BOTTOM) {\n      this.addChild(node);\n    } else if (horz && dockLocation === DockLocation.TOP || !horz && dockLocation === DockLocation.LEFT) {\n      const vrow = new _RowNode(this.model, this.windowId, {});\n      const hrow = new _RowNode(this.model, this.windowId, {});\n      hrow.setWeight(75);\n      node.setWeight(25);\n      for (const child of this.children) {\n        hrow.addChild(child);\n      }\n      this.removeAll();\n      vrow.addChild(node);\n      vrow.addChild(hrow);\n      this.addChild(vrow);\n    } else if (horz && dockLocation === DockLocation.BOTTOM || !horz && dockLocation === DockLocation.RIGHT) {\n      const vrow = new _RowNode(this.model, this.windowId, {});\n      const hrow = new _RowNode(this.model, this.windowId, {});\n      hrow.setWeight(75);\n      node.setWeight(25);\n      for (const child of this.children) {\n        hrow.addChild(child);\n      }\n      this.removeAll();\n      vrow.addChild(hrow);\n      vrow.addChild(node);\n      this.addChild(vrow);\n    }\n    if (node instanceof TabSetNode) {\n      this.model.setActiveTabset(node, this.windowId);\n    }\n    this.model.tidy();\n  }\n  /** @internal */\n  isEnableDrop() {\n    return true;\n  }\n  /** @internal */\n  getAttributeDefinitions() {\n    return _RowNode.attributeDefinitions;\n  }\n  /** @internal */\n  updateAttrs(json) {\n    _RowNode.attributeDefinitions.update(json, this.attributes);\n  }\n  /** @internal */\n  static getAttributeDefinitions() {\n    return _RowNode.attributeDefinitions;\n  }\n  // NOTE:  flex-grow cannot have values < 1 otherwise will not fill parent, need to normalize \n  normalizeWeights() {\n    let sum = 0;\n    for (const n of this.children) {\n      const node = n;\n      sum += node.getWeight();\n    }\n    if (sum === 0) {\n      sum = 1;\n    }\n    for (const n of this.children) {\n      const node = n;\n      node.setWeight(Math.max(1e-3, 100 * node.getWeight() / sum));\n    }\n  }\n  /** @internal */\n  static createAttributeDefinitions() {\n    const attributeDefinitions = new AttributeDefinitions();\n    attributeDefinitions.add(\"type\", _RowNode.TYPE, true).setType(Attribute.STRING).setFixed();\n    attributeDefinitions.add(\"id\", void 0).setType(Attribute.STRING).setDescription(\n      `the unique id of the row, if left undefined a uuid will be assigned`\n    );\n    attributeDefinitions.add(\"weight\", 100).setType(Attribute.NUMBER).setDescription(\n      `relative weight for sizing of this row in parent row`\n    );\n    return attributeDefinitions;\n  }\n};\n__publicField(_RowNode, \"TYPE\", \"row\");\n/** @internal */\n__publicField(_RowNode, \"attributeDefinitions\", _RowNode.createAttributeDefinitions());\nlet RowNode = _RowNode;\nclass LayoutWindow {\n  constructor(windowId, rect) {\n    __publicField(this, \"_windowId\");\n    __publicField(this, \"_layout\");\n    __publicField(this, \"_rect\");\n    __publicField(this, \"_window\");\n    __publicField(this, \"_root\");\n    __publicField(this, \"_maximizedTabSet\");\n    __publicField(this, \"_activeTabSet\");\n    __publicField(this, \"_toScreenRectFunction\");\n    this._windowId = windowId;\n    this._rect = rect;\n    this._toScreenRectFunction = (r) => r;\n  }\n  visitNodes(fn) {\n    this.root.forEachNode(fn, 0);\n  }\n  get windowId() {\n    return this._windowId;\n  }\n  get rect() {\n    return this._rect;\n  }\n  get layout() {\n    return this._layout;\n  }\n  get window() {\n    return this._window;\n  }\n  get root() {\n    return this._root;\n  }\n  get maximizedTabSet() {\n    return this._maximizedTabSet;\n  }\n  get activeTabSet() {\n    return this._activeTabSet;\n  }\n  /** @internal */\n  set rect(value) {\n    this._rect = value;\n  }\n  /** @internal */\n  set layout(value) {\n    this._layout = value;\n  }\n  /** @internal */\n  set window(value) {\n    this._window = value;\n  }\n  /** @internal */\n  set root(value) {\n    this._root = value;\n  }\n  /** @internal */\n  set maximizedTabSet(value) {\n    this._maximizedTabSet = value;\n  }\n  /** @internal */\n  set activeTabSet(value) {\n    this._activeTabSet = value;\n  }\n  /** @internal */\n  get toScreenRectFunction() {\n    return this._toScreenRectFunction;\n  }\n  /** @internal */\n  set toScreenRectFunction(value) {\n    this._toScreenRectFunction = value;\n  }\n  toJson() {\n    if (this._window && this._window.screenTop > -1e4) {\n      this.rect = new Rect(\n        this._window.screenLeft,\n        this._window.screenTop,\n        this._window.outerWidth,\n        this._window.outerHeight\n      );\n    }\n    return { layout: this.root.toJson(), rect: this.rect.toJson() };\n  }\n  static fromJson(windowJson, model, windowId) {\n    const count = model.getwindowsMap().size;\n    const rect = windowJson.rect ? Rect.fromJson(windowJson.rect) : new Rect(50 + 50 * count, 50 + 50 * count, 600, 400);\n    rect.snap(10);\n    const layoutWindow = new LayoutWindow(windowId, rect);\n    layoutWindow.root = RowNode.fromJson(windowJson.layout, model, layoutWindow);\n    return layoutWindow;\n  }\n}\nconst DefaultMin = 0;\nconst DefaultMax = 99999;\nconst _Model = class _Model {\n  /**\n   * 'private' constructor. Use the static method Model.fromJson(json) to create a model\n   *  @internal\n   */\n  constructor() {\n    /** @internal */\n    __publicField(this, \"attributes\");\n    /** @internal */\n    __publicField(this, \"idMap\");\n    /** @internal */\n    __publicField(this, \"changeListeners\");\n    /** @internal */\n    __publicField(this, \"borders\");\n    /** @internal */\n    __publicField(this, \"onAllowDrop\");\n    /** @internal */\n    __publicField(this, \"onCreateTabSet\");\n    /** @internal */\n    __publicField(this, \"windows\");\n    /** @internal */\n    __publicField(this, \"rootWindow\");\n    this.attributes = {};\n    this.idMap = /* @__PURE__ */ new Map();\n    this.borders = new BorderSet(this);\n    this.windows = /* @__PURE__ */ new Map();\n    this.rootWindow = new LayoutWindow(_Model.MAIN_WINDOW_ID, Rect.empty());\n    this.windows.set(_Model.MAIN_WINDOW_ID, this.rootWindow);\n    this.changeListeners = [];\n  }\n  /**\n   * Update the node tree by performing the given action,\n   * Actions should be generated via static methods on the Actions class\n   * @param action the action to perform\n   * @returns added Node for Actions.addNode, windowId for createWindow\n   */\n  doAction(action) {\n    var _a;\n    let returnVal = void 0;\n    switch (action.type) {\n      case Actions.ADD_NODE: {\n        const newNode = new TabNode(this, action.data.json, true);\n        const toNode = this.idMap.get(action.data.toNode);\n        if (toNode instanceof TabSetNode || toNode instanceof BorderNode || toNode instanceof RowNode) {\n          toNode.drop(newNode, DockLocation.getByName(action.data.location), action.data.index, action.data.select);\n          returnVal = newNode;\n        }\n        break;\n      }\n      case Actions.MOVE_NODE: {\n        const fromNode = this.idMap.get(action.data.fromNode);\n        if (fromNode instanceof TabNode || fromNode instanceof TabSetNode || fromNode instanceof RowNode) {\n          if (fromNode === this.getMaximizedTabset(fromNode.getWindowId())) {\n            const fromWindow = this.windows.get(fromNode.getWindowId());\n            fromWindow.maximizedTabSet = void 0;\n          }\n          const toNode = this.idMap.get(action.data.toNode);\n          if (toNode instanceof TabSetNode || toNode instanceof BorderNode || toNode instanceof RowNode) {\n            toNode.drop(fromNode, DockLocation.getByName(action.data.location), action.data.index, action.data.select);\n          }\n        }\n        this.removeEmptyWindows();\n        break;\n      }\n      case Actions.DELETE_TAB: {\n        const node = this.idMap.get(action.data.node);\n        if (node instanceof TabNode) {\n          node.delete();\n        }\n        this.removeEmptyWindows();\n        break;\n      }\n      case Actions.DELETE_TABSET: {\n        const node = this.idMap.get(action.data.node);\n        if (node instanceof TabSetNode) {\n          const children = [...node.getChildren()];\n          for (let i = 0; i < children.length; i++) {\n            const child = children[i];\n            if (child.isEnableClose()) {\n              child.delete();\n            }\n          }\n          if (node.getChildren().length === 0) {\n            node.delete();\n          }\n          this.tidy();\n        }\n        this.removeEmptyWindows();\n        break;\n      }\n      case Actions.POPOUT_TABSET: {\n        const node = this.idMap.get(action.data.node);\n        if (node instanceof TabSetNode) {\n          const isMaximized = node.isMaximized();\n          const oldLayoutWindow = this.windows.get(node.getWindowId());\n          const windowId = randomUUID();\n          const layoutWindow = new LayoutWindow(windowId, oldLayoutWindow.toScreenRectFunction(node.getRect()));\n          const json = {\n            type: \"row\",\n            children: []\n          };\n          const row = RowNode.fromJson(json, this, layoutWindow);\n          layoutWindow.root = row;\n          this.windows.set(windowId, layoutWindow);\n          row.drop(node, DockLocation.CENTER, 0);\n          if (isMaximized) {\n            this.rootWindow.maximizedTabSet = void 0;\n          }\n        }\n        this.removeEmptyWindows();\n        break;\n      }\n      case Actions.POPOUT_TAB: {\n        const node = this.idMap.get(action.data.node);\n        if (node instanceof TabNode) {\n          const windowId = randomUUID();\n          let r = Rect.empty();\n          if (node.getParent() instanceof TabSetNode) {\n            r = node.getParent().getRect();\n          } else {\n            r = node.getParent().getContentRect();\n          }\n          const oldLayoutWindow = this.windows.get(node.getWindowId());\n          const layoutWindow = new LayoutWindow(windowId, oldLayoutWindow.toScreenRectFunction(r));\n          const tabsetId = randomUUID();\n          const json = {\n            type: \"row\",\n            children: [\n              { type: \"tabset\", id: tabsetId }\n            ]\n          };\n          const row = RowNode.fromJson(json, this, layoutWindow);\n          layoutWindow.root = row;\n          this.windows.set(windowId, layoutWindow);\n          const tabset = this.idMap.get(tabsetId);\n          tabset.drop(node, DockLocation.CENTER, 0, true);\n        }\n        this.removeEmptyWindows();\n        break;\n      }\n      case Actions.CLOSE_WINDOW: {\n        const window2 = this.windows.get(action.data.windowId);\n        if (window2) {\n          (_a = this.rootWindow.root) == null ? void 0 : _a.drop(window2.root, DockLocation.CENTER, -1);\n          this.rootWindow.visitNodes((node, level) => {\n            if (node instanceof RowNode) {\n              node.setWindowId(_Model.MAIN_WINDOW_ID);\n            }\n          });\n          this.windows.delete(action.data.windowId);\n        }\n        break;\n      }\n      case Actions.CREATE_WINDOW: {\n        const windowId = randomUUID();\n        const layoutWindow = new LayoutWindow(windowId, Rect.fromJson(action.data.rect));\n        const row = RowNode.fromJson(action.data.layout, this, layoutWindow);\n        layoutWindow.root = row;\n        this.windows.set(windowId, layoutWindow);\n        returnVal = windowId;\n        break;\n      }\n      case Actions.RENAME_TAB: {\n        const node = this.idMap.get(action.data.node);\n        if (node instanceof TabNode) {\n          node.setName(action.data.text);\n        }\n        break;\n      }\n      case Actions.SELECT_TAB: {\n        const tabNode = this.idMap.get(action.data.tabNode);\n        const windowId = action.data.windowId ? action.data.windowId : _Model.MAIN_WINDOW_ID;\n        const window2 = this.windows.get(windowId);\n        if (tabNode instanceof TabNode) {\n          const parent = tabNode.getParent();\n          const pos = parent.getChildren().indexOf(tabNode);\n          if (parent instanceof BorderNode) {\n            if (parent.getSelected() === pos) {\n              parent.setSelected(-1);\n            } else {\n              parent.setSelected(pos);\n            }\n          } else if (parent instanceof TabSetNode) {\n            if (parent.getSelected() !== pos) {\n              parent.setSelected(pos);\n            }\n            window2.activeTabSet = parent;\n          }\n        }\n        break;\n      }\n      case Actions.SET_ACTIVE_TABSET: {\n        const windowId = action.data.windowId ? action.data.windowId : _Model.MAIN_WINDOW_ID;\n        const window2 = this.windows.get(windowId);\n        if (action.data.tabsetNode === void 0) {\n          window2.activeTabSet = void 0;\n        } else {\n          const tabsetNode = this.idMap.get(action.data.tabsetNode);\n          if (tabsetNode instanceof TabSetNode) {\n            window2.activeTabSet = tabsetNode;\n          }\n        }\n        break;\n      }\n      case Actions.ADJUST_WEIGHTS: {\n        const row = this.idMap.get(action.data.nodeId);\n        const c = row.getChildren();\n        for (let i = 0; i < c.length; i++) {\n          const n = c[i];\n          n.setWeight(action.data.weights[i]);\n        }\n        break;\n      }\n      case Actions.ADJUST_BORDER_SPLIT: {\n        const node = this.idMap.get(action.data.node);\n        if (node instanceof BorderNode) {\n          node.setSize(action.data.pos);\n        }\n        break;\n      }\n      case Actions.MAXIMIZE_TOGGLE: {\n        const windowId = action.data.windowId ? action.data.windowId : _Model.MAIN_WINDOW_ID;\n        const window2 = this.windows.get(windowId);\n        const node = this.idMap.get(action.data.node);\n        if (node instanceof TabSetNode) {\n          if (node === window2.maximizedTabSet) {\n            window2.maximizedTabSet = void 0;\n          } else {\n            window2.maximizedTabSet = node;\n            window2.activeTabSet = node;\n          }\n        }\n        break;\n      }\n      case Actions.UPDATE_MODEL_ATTRIBUTES: {\n        this.updateAttrs(action.data.json);\n        break;\n      }\n      case Actions.UPDATE_NODE_ATTRIBUTES: {\n        const node = this.idMap.get(action.data.node);\n        node.updateAttrs(action.data.json);\n        break;\n      }\n    }\n    this.updateIdMap();\n    for (const listener of this.changeListeners) {\n      listener(action);\n    }\n    return returnVal;\n  }\n  /**\n   * Get the currently active tabset node\n   */\n  getActiveTabset(windowId = _Model.MAIN_WINDOW_ID) {\n    const window2 = this.windows.get(windowId);\n    if (window2 && window2.activeTabSet && this.getNodeById(window2.activeTabSet.getId())) {\n      return window2.activeTabSet;\n    } else {\n      return void 0;\n    }\n  }\n  /**\n   * Get the currently maximized tabset node\n   */\n  getMaximizedTabset(windowId = _Model.MAIN_WINDOW_ID) {\n    return this.windows.get(windowId).maximizedTabSet;\n  }\n  /**\n   * Gets the root RowNode of the model\n   * @returns {RowNode}\n   */\n  getRoot(windowId = _Model.MAIN_WINDOW_ID) {\n    return this.windows.get(windowId).root;\n  }\n  isRootOrientationVertical() {\n    return this.attributes.rootOrientationVertical;\n  }\n  isEnableRotateBorderIcons() {\n    return this.attributes.enableRotateBorderIcons;\n  }\n  /**\n   * Gets the\n   * @returns {BorderSet|*}\n   */\n  getBorderSet() {\n    return this.borders;\n  }\n  getwindowsMap() {\n    return this.windows;\n  }\n  /**\n   * Visits all the nodes in the model and calls the given function for each\n   * @param fn a function that takes visited node and a integer level as parameters\n   */\n  visitNodes(fn) {\n    this.borders.forEachNode(fn);\n    for (const [_, w] of this.windows) {\n      w.root.forEachNode(fn, 0);\n    }\n  }\n  visitWindowNodes(windowId, fn) {\n    if (this.windows.has(windowId)) {\n      if (windowId === _Model.MAIN_WINDOW_ID) {\n        this.borders.forEachNode(fn);\n      }\n      this.windows.get(windowId).visitNodes(fn);\n    }\n  }\n  /**\n   * Gets a node by its id\n   * @param id the id to find\n   */\n  getNodeById(id) {\n    return this.idMap.get(id);\n  }\n  /**\n   * Finds the first/top left tab set of the given node.\n   * @param node The top node you want to begin searching from, deafults to the root node\n   * @returns The first Tab Set\n   */\n  getFirstTabSet(node = this.windows.get(_Model.MAIN_WINDOW_ID).root) {\n    const child = node.getChildren()[0];\n    if (child instanceof TabSetNode) {\n      return child;\n    } else {\n      return this.getFirstTabSet(child);\n    }\n  }\n  /**\n  * Loads the model from the given json object\n  * @param json the json model to load\n  * @returns {Model} a new Model object\n  */\n  static fromJson(json) {\n    const model = new _Model();\n    _Model.attributeDefinitions.fromJson(json.global, model.attributes);\n    if (json.borders) {\n      model.borders = BorderSet.fromJson(json.borders, model);\n    }\n    if (json.popouts) {\n      for (const windowId in json.popouts) {\n        const windowJson = json.popouts[windowId];\n        const layoutWindow = LayoutWindow.fromJson(windowJson, model, windowId);\n        model.windows.set(windowId, layoutWindow);\n      }\n    }\n    model.rootWindow.root = RowNode.fromJson(json.layout, model, model.getwindowsMap().get(_Model.MAIN_WINDOW_ID));\n    model.tidy();\n    return model;\n  }\n  /**\n   * Converts the model to a json object\n   * @returns {IJsonModel} json object that represents this model\n   */\n  toJson() {\n    const global = {};\n    _Model.attributeDefinitions.toJson(global, this.attributes);\n    this.visitNodes((node) => {\n      node.fireEvent(\"save\", {});\n    });\n    const windows = {};\n    for (const [id, window2] of this.windows) {\n      if (id !== _Model.MAIN_WINDOW_ID) {\n        windows[id] = window2.toJson();\n      }\n    }\n    return {\n      global,\n      borders: this.borders.toJson(),\n      layout: this.rootWindow.root.toJson(),\n      popouts: windows\n    };\n  }\n  getSplitterSize() {\n    return this.attributes.splitterSize;\n  }\n  getSplitterExtra() {\n    return this.attributes.splitterExtra;\n  }\n  isEnableEdgeDock() {\n    return this.attributes.enableEdgeDock;\n  }\n  isSplitterEnableHandle() {\n    return this.attributes.splitterEnableHandle;\n  }\n  /**\n   * Sets a function to allow/deny dropping a node\n   * @param onAllowDrop function that takes the drag node and DropInfo and returns true if the drop is allowed\n   */\n  setOnAllowDrop(onAllowDrop) {\n    this.onAllowDrop = onAllowDrop;\n  }\n  /**\n   * set callback called when a new TabSet is created.\n   * The tabNode can be undefined if it's the auto created first tabset in the root row (when the last\n   * tab is deleted, the root tabset can be recreated)\n   * @param onCreateTabSet \n   */\n  setOnCreateTabSet(onCreateTabSet) {\n    this.onCreateTabSet = onCreateTabSet;\n  }\n  addChangeListener(listener) {\n    this.changeListeners.push(listener);\n  }\n  removeChangeListener(listener) {\n    const pos = this.changeListeners.findIndex((l) => l === listener);\n    if (pos !== -1) {\n      this.changeListeners.splice(pos, 1);\n    }\n  }\n  toString() {\n    return JSON.stringify(this.toJson());\n  }\n  /***********************internal ********************************/\n  /** @internal */\n  removeEmptyWindows() {\n    const emptyWindows = /* @__PURE__ */ new Set();\n    for (const [windowId] of this.windows) {\n      if (windowId !== _Model.MAIN_WINDOW_ID) {\n        let count = 0;\n        this.visitWindowNodes(windowId, (node) => {\n          if (node instanceof TabNode) {\n            count++;\n          }\n        });\n        if (count === 0) {\n          emptyWindows.add(windowId);\n        }\n      }\n    }\n    for (const windowId of emptyWindows) {\n      this.windows.delete(windowId);\n    }\n  }\n  /** @internal */\n  setActiveTabset(tabsetNode, windowId) {\n    const window2 = this.windows.get(windowId);\n    if (window2) {\n      if (tabsetNode) {\n        window2.activeTabSet = tabsetNode;\n      } else {\n        window2.activeTabSet = void 0;\n      }\n    }\n  }\n  /** @internal */\n  setMaximizedTabset(tabsetNode, windowId) {\n    const window2 = this.windows.get(windowId);\n    if (window2) {\n      if (tabsetNode) {\n        window2.maximizedTabSet = tabsetNode;\n      } else {\n        window2.maximizedTabSet = void 0;\n      }\n    }\n  }\n  /** @internal */\n  updateIdMap() {\n    this.idMap.clear();\n    this.visitNodes((node) => {\n      this.idMap.set(node.getId(), node);\n    });\n  }\n  /** @internal */\n  addNode(node) {\n    const id = node.getId();\n    if (this.idMap.has(id)) {\n      throw new Error(`Error: each node must have a unique id, duplicate id:${node.getId()}`);\n    }\n    this.idMap.set(id, node);\n  }\n  /** @internal */\n  findDropTargetNode(windowId, dragNode, x, y) {\n    let node = this.windows.get(windowId).root.findDropTargetNode(windowId, dragNode, x, y);\n    if (node === void 0 && windowId === _Model.MAIN_WINDOW_ID) {\n      node = this.borders.findDropTargetNode(dragNode, x, y);\n    }\n    return node;\n  }\n  /** @internal */\n  tidy() {\n    for (const [_, window2] of this.windows) {\n      window2.root.tidy();\n    }\n  }\n  /** @internal */\n  updateAttrs(json) {\n    _Model.attributeDefinitions.update(json, this.attributes);\n  }\n  /** @internal */\n  nextUniqueId() {\n    return \"#\" + randomUUID();\n  }\n  /** @internal */\n  getAttribute(name) {\n    return this.attributes[name];\n  }\n  /** @internal */\n  getOnAllowDrop() {\n    return this.onAllowDrop;\n  }\n  /** @internal */\n  getOnCreateTabSet() {\n    return this.onCreateTabSet;\n  }\n  static toTypescriptInterfaces() {\n    _Model.attributeDefinitions.pairAttributes(\"RowNode\", RowNode.getAttributeDefinitions());\n    _Model.attributeDefinitions.pairAttributes(\"TabSetNode\", TabSetNode.getAttributeDefinitions());\n    _Model.attributeDefinitions.pairAttributes(\"TabNode\", TabNode.getAttributeDefinitions());\n    _Model.attributeDefinitions.pairAttributes(\"BorderNode\", BorderNode.getAttributeDefinitions());\n    const sb = [];\n    sb.push(_Model.attributeDefinitions.toTypescriptInterface(\"Global\", void 0));\n    sb.push(RowNode.getAttributeDefinitions().toTypescriptInterface(\"Row\", _Model.attributeDefinitions));\n    sb.push(TabSetNode.getAttributeDefinitions().toTypescriptInterface(\"TabSet\", _Model.attributeDefinitions));\n    sb.push(TabNode.getAttributeDefinitions().toTypescriptInterface(\"Tab\", _Model.attributeDefinitions));\n    sb.push(BorderNode.getAttributeDefinitions().toTypescriptInterface(\"Border\", _Model.attributeDefinitions));\n    console.log(sb.join(\"\\n\"));\n  }\n  /** @internal */\n  static createAttributeDefinitions() {\n    const attributeDefinitions = new AttributeDefinitions();\n    attributeDefinitions.add(\"enableEdgeDock\", true).setType(Attribute.BOOLEAN).setDescription(\n      `enable docking to the edges of the layout, this will show the edge indicators`\n    );\n    attributeDefinitions.add(\"rootOrientationVertical\", false).setType(Attribute.BOOLEAN).setDescription(\n      `the top level 'row' will layout horizontally by default, set this option true to make it layout vertically`\n    );\n    attributeDefinitions.add(\"enableRotateBorderIcons\", true).setType(Attribute.BOOLEAN).setDescription(\n      `boolean indicating if tab icons should rotate with the text in the left and right borders`\n    );\n    attributeDefinitions.add(\"splitterSize\", 8).setType(Attribute.NUMBER).setDescription(\n      `width in pixels of all splitters between tabsets/borders`\n    );\n    attributeDefinitions.add(\"splitterExtra\", 0).setType(Attribute.NUMBER).setDescription(\n      `additional width in pixels of the splitter hit test area`\n    );\n    attributeDefinitions.add(\"splitterEnableHandle\", false).setType(Attribute.BOOLEAN).setDescription(\n      `enable a small centralized handle on all splitters`\n    );\n    attributeDefinitions.add(\"tabEnableClose\", true).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabCloseType\", 1).setType(\"ICloseType\");\n    attributeDefinitions.add(\"tabEnablePopout\", false).setType(Attribute.BOOLEAN).setAlias(\"tabEnableFloat\");\n    attributeDefinitions.add(\"tabEnablePopoutIcon\", true).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabEnablePopoutOverlay\", false).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabEnableDrag\", true).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabEnableRename\", true).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabContentClassName\", void 0).setType(Attribute.STRING);\n    attributeDefinitions.add(\"tabClassName\", void 0).setType(Attribute.STRING);\n    attributeDefinitions.add(\"tabIcon\", void 0).setType(Attribute.STRING);\n    attributeDefinitions.add(\"tabEnableRenderOnDemand\", true).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabDragSpeed\", 0.3).setType(Attribute.NUMBER);\n    attributeDefinitions.add(\"tabBorderWidth\", -1).setType(Attribute.NUMBER);\n    attributeDefinitions.add(\"tabBorderHeight\", -1).setType(Attribute.NUMBER);\n    attributeDefinitions.add(\"tabSetEnableDeleteWhenEmpty\", true).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabSetEnableDrop\", true).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabSetEnableDrag\", true).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabSetEnableDivide\", true).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabSetEnableMaximize\", true).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabSetEnableClose\", false).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabSetEnableSingleTabStretch\", false).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabSetAutoSelectTab\", true).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabSetEnableActiveIcon\", false).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabSetClassNameTabStrip\", void 0).setType(Attribute.STRING);\n    attributeDefinitions.add(\"tabSetEnableTabStrip\", true).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabSetEnableTabWrap\", false).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"tabSetTabLocation\", \"top\").setType(\"ITabLocation\");\n    attributeDefinitions.add(\"tabMinWidth\", DefaultMin).setType(Attribute.NUMBER);\n    attributeDefinitions.add(\"tabMinHeight\", DefaultMin).setType(Attribute.NUMBER);\n    attributeDefinitions.add(\"tabSetMinWidth\", DefaultMin).setType(Attribute.NUMBER);\n    attributeDefinitions.add(\"tabSetMinHeight\", DefaultMin).setType(Attribute.NUMBER);\n    attributeDefinitions.add(\"tabMaxWidth\", DefaultMax).setType(Attribute.NUMBER);\n    attributeDefinitions.add(\"tabMaxHeight\", DefaultMax).setType(Attribute.NUMBER);\n    attributeDefinitions.add(\"tabSetMaxWidth\", DefaultMax).setType(Attribute.NUMBER);\n    attributeDefinitions.add(\"tabSetMaxHeight\", DefaultMax).setType(Attribute.NUMBER);\n    attributeDefinitions.add(\"tabSetEnableTabScrollbar\", false).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"borderSize\", 200).setType(Attribute.NUMBER);\n    attributeDefinitions.add(\"borderMinSize\", DefaultMin).setType(Attribute.NUMBER);\n    attributeDefinitions.add(\"borderMaxSize\", DefaultMax).setType(Attribute.NUMBER);\n    attributeDefinitions.add(\"borderEnableDrop\", true).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"borderAutoSelectTabWhenOpen\", true).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"borderAutoSelectTabWhenClosed\", false).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"borderClassName\", void 0).setType(Attribute.STRING);\n    attributeDefinitions.add(\"borderEnableAutoHide\", false).setType(Attribute.BOOLEAN);\n    attributeDefinitions.add(\"borderEnableTabScrollbar\", false).setType(Attribute.BOOLEAN);\n    return attributeDefinitions;\n  }\n};\n__publicField(_Model, \"MAIN_WINDOW_ID\", \"__main_window_id__\");\n/** @internal */\n__publicField(_Model, \"attributeDefinitions\", _Model.createAttributeDefinitions());\nlet Model = _Model;\nconst _BorderNode = class _BorderNode extends Node {\n  /** @internal */\n  constructor(location, json, model) {\n    super(model);\n    /** @internal */\n    __publicField(this, \"contentRect\", Rect.empty());\n    /** @internal */\n    __publicField(this, \"tabHeaderRect\", Rect.empty());\n    /** @internal */\n    __publicField(this, \"location\");\n    this.location = location;\n    this.attributes.id = `border_${location.getName()}`;\n    _BorderNode.attributeDefinitions.fromJson(json, this.attributes);\n    model.addNode(this);\n  }\n  /** @internal */\n  static fromJson(json, model) {\n    const location = DockLocation.getByName(json.location);\n    const border = new _BorderNode(location, json, model);\n    if (json.children) {\n      border.children = json.children.map((jsonChild) => {\n        const child = TabNode.fromJson(jsonChild, model);\n        child.setParent(border);\n        return child;\n      });\n    }\n    return border;\n  }\n  getLocation() {\n    return this.location;\n  }\n  getClassName() {\n    return this.getAttr(\"className\");\n  }\n  isHorizontal() {\n    return this.location.orientation === Orientation.HORZ;\n  }\n  getSize() {\n    const defaultSize = this.getAttr(\"size\");\n    const selected = this.getSelected();\n    if (selected === -1) {\n      return defaultSize;\n    } else {\n      const tabNode = this.children[selected];\n      const tabBorderSize = this.isHorizontal() ? tabNode.getAttr(\"borderWidth\") : tabNode.getAttr(\"borderHeight\");\n      if (tabBorderSize === -1) {\n        return defaultSize;\n      } else {\n        return tabBorderSize;\n      }\n    }\n  }\n  getMinSize() {\n    const selectedNode = this.getSelectedNode();\n    let min = this.getAttr(\"minSize\");\n    if (selectedNode) {\n      const nodeMin = this.isHorizontal() ? selectedNode.getMinWidth() : selectedNode.getMinHeight();\n      min = Math.max(min, nodeMin);\n    }\n    return min;\n  }\n  getMaxSize() {\n    const selectedNode = this.getSelectedNode();\n    let max = this.getAttr(\"maxSize\");\n    if (selectedNode) {\n      const nodeMax = this.isHorizontal() ? selectedNode.getMaxWidth() : selectedNode.getMaxHeight();\n      max = Math.min(max, nodeMax);\n    }\n    return max;\n  }\n  getSelected() {\n    return this.attributes.selected;\n  }\n  isAutoHide() {\n    return this.getAttr(\"enableAutoHide\");\n  }\n  getSelectedNode() {\n    if (this.getSelected() !== -1) {\n      return this.children[this.getSelected()];\n    }\n    return void 0;\n  }\n  getOrientation() {\n    return this.location.getOrientation();\n  }\n  /**\n   * Returns the config attribute that can be used to store node specific data that\n   * WILL be saved to the json. The config attribute should be changed via the action Actions.updateNodeAttributes rather\n   * than directly, for example:\n   * this.state.model.doAction(\n   *   FlexLayout.Actions.updateNodeAttributes(node.getId(), {config:myConfigObject}));\n   */\n  getConfig() {\n    return this.attributes.config;\n  }\n  isMaximized() {\n    return false;\n  }\n  isShowing() {\n    return this.attributes.show;\n  }\n  toJson() {\n    const json = {};\n    _BorderNode.attributeDefinitions.toJson(json, this.attributes);\n    json.location = this.location.getName();\n    json.children = this.children.map((child) => child.toJson());\n    return json;\n  }\n  /** @internal */\n  isAutoSelectTab(whenOpen) {\n    if (whenOpen == null) {\n      whenOpen = this.getSelected() !== -1;\n    }\n    if (whenOpen) {\n      return this.getAttr(\"autoSelectTabWhenOpen\");\n    } else {\n      return this.getAttr(\"autoSelectTabWhenClosed\");\n    }\n  }\n  isEnableTabScrollbar() {\n    return this.getAttr(\"enableTabScrollbar\");\n  }\n  /** @internal */\n  setSelected(index) {\n    this.attributes.selected = index;\n  }\n  /** @internal */\n  getTabHeaderRect() {\n    return this.tabHeaderRect;\n  }\n  /** @internal */\n  setTabHeaderRect(r) {\n    this.tabHeaderRect = r;\n  }\n  /** @internal */\n  getRect() {\n    return this.tabHeaderRect;\n  }\n  /** @internal */\n  getContentRect() {\n    return this.contentRect;\n  }\n  /** @internal */\n  setContentRect(r) {\n    this.contentRect = r;\n  }\n  /** @internal */\n  isEnableDrop() {\n    return this.getAttr(\"enableDrop\");\n  }\n  /** @internal */\n  setSize(pos) {\n    const selected = this.getSelected();\n    if (selected === -1) {\n      this.attributes.size = pos;\n    } else {\n      const tabNode = this.children[selected];\n      const tabBorderSize = this.isHorizontal() ? tabNode.getAttr(\"borderWidth\") : tabNode.getAttr(\"borderHeight\");\n      if (tabBorderSize === -1) {\n        this.attributes.size = pos;\n      } else {\n        if (this.isHorizontal()) {\n          tabNode.setBorderWidth(pos);\n        } else {\n          tabNode.setBorderHeight(pos);\n        }\n      }\n    }\n  }\n  /** @internal */\n  updateAttrs(json) {\n    _BorderNode.attributeDefinitions.update(json, this.attributes);\n  }\n  /** @internal */\n  remove(node) {\n    const removedIndex = this.removeChild(node);\n    if (this.getSelected() !== -1) {\n      adjustSelectedIndex(this, removedIndex);\n    }\n  }\n  /** @internal */\n  canDrop(dragNode, x, y) {\n    if (!(dragNode instanceof TabNode)) {\n      return void 0;\n    }\n    let dropInfo;\n    const dockLocation = DockLocation.CENTER;\n    if (this.tabHeaderRect.contains(x, y)) {\n      if (this.location.orientation === Orientation.VERT) {\n        if (this.children.length > 0) {\n          let child = this.children[0];\n          let childRect = child.getTabRect();\n          const childY = childRect.y;\n          const childHeight = childRect.height;\n          let pos = this.tabHeaderRect.x;\n          let childCenter = 0;\n          for (let i = 0; i < this.children.length; i++) {\n            child = this.children[i];\n            childRect = child.getTabRect();\n            childCenter = childRect.x + childRect.width / 2;\n            if (x >= pos && x < childCenter) {\n              const outlineRect = new Rect(childRect.x - 2, childY, 3, childHeight);\n              dropInfo = new DropInfo(this, outlineRect, dockLocation, i, CLASSES.FLEXLAYOUT__OUTLINE_RECT);\n              break;\n            }\n            pos = childCenter;\n          }\n          if (dropInfo == null) {\n            const outlineRect = new Rect(childRect.getRight() - 2, childY, 3, childHeight);\n            dropInfo = new DropInfo(this, outlineRect, dockLocation, this.children.length, CLASSES.FLEXLAYOUT__OUTLINE_RECT);\n          }\n        } else {\n          const outlineRect = new Rect(this.tabHeaderRect.x + 1, this.tabHeaderRect.y + 2, 3, 18);\n          dropInfo = new DropInfo(this, outlineRect, dockLocation, 0, CLASSES.FLEXLAYOUT__OUTLINE_RECT);\n        }\n      } else {\n        if (this.children.length > 0) {\n          let child = this.children[0];\n          let childRect = child.getTabRect();\n          const childX = childRect.x;\n          const childWidth = childRect.width;\n          let pos = this.tabHeaderRect.y;\n          let childCenter = 0;\n          for (let i = 0; i < this.children.length; i++) {\n            child = this.children[i];\n            childRect = child.getTabRect();\n            childCenter = childRect.y + childRect.height / 2;\n            if (y >= pos && y < childCenter) {\n              const outlineRect = new Rect(childX, childRect.y - 2, childWidth, 3);\n              dropInfo = new DropInfo(this, outlineRect, dockLocation, i, CLASSES.FLEXLAYOUT__OUTLINE_RECT);\n              break;\n            }\n            pos = childCenter;\n          }\n          if (dropInfo == null) {\n            const outlineRect = new Rect(childX, childRect.getBottom() - 2, childWidth, 3);\n            dropInfo = new DropInfo(this, outlineRect, dockLocation, this.children.length, CLASSES.FLEXLAYOUT__OUTLINE_RECT);\n          }\n        } else {\n          const outlineRect = new Rect(this.tabHeaderRect.x + 2, this.tabHeaderRect.y + 1, 18, 3);\n          dropInfo = new DropInfo(this, outlineRect, dockLocation, 0, CLASSES.FLEXLAYOUT__OUTLINE_RECT);\n        }\n      }\n      if (!dragNode.canDockInto(dragNode, dropInfo)) {\n        return void 0;\n      }\n    } else if (this.getSelected() !== -1 && this.contentRect.contains(x, y)) {\n      const outlineRect = this.contentRect;\n      dropInfo = new DropInfo(this, outlineRect, dockLocation, -1, CLASSES.FLEXLAYOUT__OUTLINE_RECT);\n      if (!dragNode.canDockInto(dragNode, dropInfo)) {\n        return void 0;\n      }\n    }\n    return dropInfo;\n  }\n  /** @internal */\n  drop(dragNode, location, index, select) {\n    let fromIndex = 0;\n    const dragParent = dragNode.getParent();\n    if (dragParent !== void 0) {\n      fromIndex = dragParent.removeChild(dragNode);\n      if (dragParent !== this && dragParent instanceof _BorderNode && dragParent.getSelected() === fromIndex) {\n        dragParent.setSelected(-1);\n      } else {\n        adjustSelectedIndex(dragParent, fromIndex);\n      }\n    }\n    if (dragNode instanceof TabNode && dragParent === this && fromIndex < index && index > 0) {\n      index--;\n    }\n    let insertPos = index;\n    if (insertPos === -1) {\n      insertPos = this.children.length;\n    }\n    if (dragNode instanceof TabNode) {\n      this.addChild(dragNode, insertPos);\n    }\n    if (select || select !== false && this.isAutoSelectTab()) {\n      this.setSelected(insertPos);\n    }\n    this.model.tidy();\n  }\n  /** @internal */\n  getSplitterBounds(index, useMinSize = false) {\n    const pBounds = [0, 0];\n    const minSize = useMinSize ? this.getMinSize() : 0;\n    const maxSize = useMinSize ? this.getMaxSize() : 99999;\n    const rootRow = this.model.getRoot(Model.MAIN_WINDOW_ID);\n    const innerRect = rootRow.getRect();\n    const splitterSize = this.model.getSplitterSize();\n    if (this.location === DockLocation.TOP) {\n      pBounds[0] = this.tabHeaderRect.getBottom() + minSize;\n      const maxPos = this.tabHeaderRect.getBottom() + maxSize;\n      pBounds[1] = Math.max(pBounds[0], innerRect.getBottom() - rootRow.getMinHeight() - splitterSize);\n      pBounds[1] = Math.min(pBounds[1], maxPos);\n    } else if (this.location === DockLocation.LEFT) {\n      pBounds[0] = this.tabHeaderRect.getRight() + minSize;\n      const maxPos = this.tabHeaderRect.getRight() + maxSize;\n      pBounds[1] = Math.max(pBounds[0], innerRect.getRight() - rootRow.getMinWidth() - splitterSize);\n      pBounds[1] = Math.min(pBounds[1], maxPos);\n    } else if (this.location === DockLocation.BOTTOM) {\n      pBounds[1] = this.tabHeaderRect.y - minSize - splitterSize;\n      const maxPos = this.tabHeaderRect.y - maxSize - splitterSize;\n      pBounds[0] = Math.min(pBounds[1], innerRect.y + rootRow.getMinHeight());\n      pBounds[0] = Math.max(pBounds[0], maxPos);\n    } else if (this.location === DockLocation.RIGHT) {\n      pBounds[1] = this.tabHeaderRect.x - minSize - splitterSize;\n      const maxPos = this.tabHeaderRect.x - maxSize - splitterSize;\n      pBounds[0] = Math.min(pBounds[1], innerRect.x + rootRow.getMinWidth());\n      pBounds[0] = Math.max(pBounds[0], maxPos);\n    }\n    return pBounds;\n  }\n  /** @internal */\n  calculateSplit(splitter, splitterPos) {\n    const pBounds = this.getSplitterBounds(splitterPos);\n    if (this.location === DockLocation.BOTTOM || this.location === DockLocation.RIGHT) {\n      return Math.max(0, pBounds[1] - splitterPos);\n    } else {\n      return Math.max(0, splitterPos - pBounds[0]);\n    }\n  }\n  /** @internal */\n  getAttributeDefinitions() {\n    return _BorderNode.attributeDefinitions;\n  }\n  /** @internal */\n  static getAttributeDefinitions() {\n    return _BorderNode.attributeDefinitions;\n  }\n  /** @internal */\n  static createAttributeDefinitions() {\n    const attributeDefinitions = new AttributeDefinitions();\n    attributeDefinitions.add(\"type\", _BorderNode.TYPE, true).setType(Attribute.STRING).setFixed();\n    attributeDefinitions.add(\"selected\", -1).setType(Attribute.NUMBER).setDescription(\n      `index of selected/visible tab in border; -1 means no tab selected`\n    );\n    attributeDefinitions.add(\"show\", true).setType(Attribute.BOOLEAN).setDescription(\n      `show/hide this border`\n    );\n    attributeDefinitions.add(\"config\", void 0).setType(\"any\").setDescription(\n      `a place to hold json config used in your own code`\n    );\n    attributeDefinitions.addInherited(\"enableDrop\", \"borderEnableDrop\").setType(Attribute.BOOLEAN).setDescription(\n      `whether tabs can be dropped into this border`\n    );\n    attributeDefinitions.addInherited(\"className\", \"borderClassName\").setType(Attribute.STRING).setDescription(\n      `class applied to tab button`\n    );\n    attributeDefinitions.addInherited(\"autoSelectTabWhenOpen\", \"borderAutoSelectTabWhenOpen\").setType(Attribute.BOOLEAN).setDescription(\n      `whether to select new/moved tabs in border when the border is already open`\n    );\n    attributeDefinitions.addInherited(\"autoSelectTabWhenClosed\", \"borderAutoSelectTabWhenClosed\").setType(Attribute.BOOLEAN).setDescription(\n      `whether to select new/moved tabs in border when the border is currently closed`\n    );\n    attributeDefinitions.addInherited(\"size\", \"borderSize\").setType(Attribute.NUMBER).setDescription(\n      `size of the tab area when selected`\n    );\n    attributeDefinitions.addInherited(\"minSize\", \"borderMinSize\").setType(Attribute.NUMBER).setDescription(\n      `the minimum size of the tab area`\n    );\n    attributeDefinitions.addInherited(\"maxSize\", \"borderMaxSize\").setType(Attribute.NUMBER).setDescription(\n      `the maximum size of the tab area`\n    );\n    attributeDefinitions.addInherited(\"enableAutoHide\", \"borderEnableAutoHide\").setType(Attribute.BOOLEAN).setDescription(\n      `hide border if it has zero tabs`\n    );\n    attributeDefinitions.addInherited(\"enableTabScrollbar\", \"borderEnableTabScrollbar\").setType(Attribute.BOOLEAN).setDescription(\n      `whether to show a mini scrollbar for the tabs`\n    );\n    return attributeDefinitions;\n  }\n};\n__publicField(_BorderNode, \"TYPE\", \"border\");\n/** @internal */\n__publicField(_BorderNode, \"attributeDefinitions\", _BorderNode.createAttributeDefinitions());\nlet BorderNode = _BorderNode;\nlet splitterDragging = false;\nconst Splitter = (props) => {\n  const { layout, node, index, horizontal } = props;\n  const [dragging, setDragging] = React.useState(false);\n  const selfRef = React.useRef(null);\n  const extendedRef = React.useRef(null);\n  const pBounds = React.useRef([]);\n  const outlineDiv = React.useRef(void 0);\n  const handleDiv = React.useRef(void 0);\n  const dragStartX = React.useRef(0);\n  const dragStartY = React.useRef(0);\n  const initalSizes = React.useRef({ initialSizes: [], sum: 0, startPosition: 0 });\n  const size = node.getModel().getSplitterSize();\n  let extra = node.getModel().getSplitterExtra();\n  if (!isDesktop()) {\n    extra = Math.max(20, extra + size) - size;\n  }\n  React.useEffect(() => {\n    var _a, _b;\n    (_a = selfRef.current) == null ? void 0 : _a.addEventListener(\"touchstart\", onTouchStart, { passive: false });\n    (_b = extendedRef.current) == null ? void 0 : _b.addEventListener(\"touchstart\", onTouchStart, { passive: false });\n    return () => {\n      var _a2, _b2;\n      (_a2 = selfRef.current) == null ? void 0 : _a2.removeEventListener(\"touchstart\", onTouchStart);\n      (_b2 = extendedRef.current) == null ? void 0 : _b2.removeEventListener(\"touchstart\", onTouchStart);\n    };\n  }, []);\n  const onTouchStart = (event) => {\n    event.preventDefault();\n    event.stopImmediatePropagation();\n  };\n  const onPointerDown = (event) => {\n    var _a;\n    event.stopPropagation();\n    if (node instanceof RowNode) {\n      initalSizes.current = node.getSplitterInitials(index);\n    }\n    enablePointerOnIFrames(false, layout.getCurrentDocument());\n    startDrag(event.currentTarget.ownerDocument, event, onDragMove, onDragEnd, onDragCancel);\n    pBounds.current = node.getSplitterBounds(index, true);\n    const rootdiv = layout.getRootDiv();\n    outlineDiv.current = layout.getCurrentDocument().createElement(\"div\");\n    outlineDiv.current.style.flexDirection = horizontal ? \"row\" : \"column\";\n    outlineDiv.current.className = layout.getClassName(CLASSES.FLEXLAYOUT__SPLITTER_DRAG);\n    outlineDiv.current.style.cursor = node.getOrientation() === Orientation.VERT ? \"ns-resize\" : \"ew-resize\";\n    if (node.getModel().isSplitterEnableHandle()) {\n      handleDiv.current = layout.getCurrentDocument().createElement(\"div\");\n      handleDiv.current.className = cm(CLASSES.FLEXLAYOUT__SPLITTER_HANDLE) + \" \" + (horizontal ? cm(CLASSES.FLEXLAYOUT__SPLITTER_HANDLE_HORZ) : cm(CLASSES.FLEXLAYOUT__SPLITTER_HANDLE_VERT));\n      outlineDiv.current.appendChild(handleDiv.current);\n    }\n    const r = (_a = selfRef.current) == null ? void 0 : _a.getBoundingClientRect();\n    const rect = new Rect(\n      r.x - layout.getDomRect().x,\n      r.y - layout.getDomRect().y,\n      r.width,\n      r.height\n    );\n    dragStartX.current = event.clientX - r.x;\n    dragStartY.current = event.clientY - r.y;\n    rect.positionElement(outlineDiv.current);\n    if (rootdiv) {\n      rootdiv.appendChild(outlineDiv.current);\n    }\n    setDragging(true);\n    splitterDragging = true;\n  };\n  const onDragCancel = () => {\n    const rootdiv = layout.getRootDiv();\n    if (rootdiv && outlineDiv.current) {\n      rootdiv.removeChild(outlineDiv.current);\n    }\n    outlineDiv.current = void 0;\n    setDragging(false);\n    splitterDragging = false;\n  };\n  const onDragMove = (x, y) => {\n    if (outlineDiv.current) {\n      const clientRect = layout.getDomRect();\n      if (!clientRect) {\n        return;\n      }\n      if (node.getOrientation() === Orientation.VERT) {\n        outlineDiv.current.style.top = getBoundPosition(y - clientRect.y - dragStartY.current) + \"px\";\n      } else {\n        outlineDiv.current.style.left = getBoundPosition(x - clientRect.x - dragStartX.current) + \"px\";\n      }\n      if (layout.isRealtimeResize()) {\n        updateLayout();\n      }\n    }\n  };\n  const onDragEnd = () => {\n    if (outlineDiv.current) {\n      updateLayout();\n      const rootdiv = layout.getRootDiv();\n      if (rootdiv && outlineDiv.current) {\n        rootdiv.removeChild(outlineDiv.current);\n      }\n      outlineDiv.current = void 0;\n    }\n    enablePointerOnIFrames(true, layout.getCurrentDocument());\n    setDragging(false);\n    splitterDragging = false;\n  };\n  const updateLayout = (realtime) => {\n    const redraw = () => {\n      if (outlineDiv.current) {\n        let value = 0;\n        if (node.getOrientation() === Orientation.VERT) {\n          value = outlineDiv.current.offsetTop;\n        } else {\n          value = outlineDiv.current.offsetLeft;\n        }\n        if (node instanceof BorderNode) {\n          const pos = node.calculateSplit(node, value);\n          layout.doAction(Actions.adjustBorderSplit(node.getId(), pos));\n        } else {\n          const init = initalSizes.current;\n          const weights = node.calculateSplit(index, value, init.initialSizes, init.sum, init.startPosition);\n          layout.doAction(Actions.adjustWeights(node.getId(), weights));\n        }\n      }\n    };\n    redraw();\n  };\n  const getBoundPosition = (p) => {\n    const bounds = pBounds.current;\n    let rtn = p;\n    if (p < bounds[0]) {\n      rtn = bounds[0];\n    }\n    if (p > bounds[1]) {\n      rtn = bounds[1];\n    }\n    return rtn;\n  };\n  const cm = layout.getClassName;\n  const style2 = {\n    cursor: horizontal ? \"ew-resize\" : \"ns-resize\",\n    flexDirection: horizontal ? \"column\" : \"row\"\n  };\n  let className = cm(CLASSES.FLEXLAYOUT__SPLITTER) + \" \" + cm(CLASSES.FLEXLAYOUT__SPLITTER_ + node.getOrientation().getName());\n  if (node instanceof BorderNode) {\n    className += \" \" + cm(CLASSES.FLEXLAYOUT__SPLITTER_BORDER);\n  } else {\n    if (node.getModel().getMaximizedTabset(layout.getWindowId()) !== void 0) {\n      style2.display = \"none\";\n    }\n  }\n  if (horizontal) {\n    style2.width = size + \"px\";\n    style2.minWidth = size + \"px\";\n  } else {\n    style2.height = size + \"px\";\n    style2.minHeight = size + \"px\";\n  }\n  let handle;\n  if (!dragging && node.getModel().isSplitterEnableHandle()) {\n    handle = /* @__PURE__ */ jsx(\n      \"div\",\n      {\n        className: cm(CLASSES.FLEXLAYOUT__SPLITTER_HANDLE) + \" \" + (horizontal ? cm(CLASSES.FLEXLAYOUT__SPLITTER_HANDLE_HORZ) : cm(CLASSES.FLEXLAYOUT__SPLITTER_HANDLE_VERT))\n      }\n    );\n  }\n  if (extra === 0) {\n    return /* @__PURE__ */ jsx(\n      \"div\",\n      {\n        className,\n        style: style2,\n        ref: selfRef,\n        \"data-layout-path\": node.getPath() + \"/s\" + (index - 1),\n        onPointerDown,\n        children: handle\n      }\n    );\n  } else {\n    const style22 = {};\n    if (node.getOrientation() === Orientation.HORZ) {\n      style22.height = \"100%\";\n      style22.width = size + extra + \"px\";\n      style22.cursor = \"ew-resize\";\n    } else {\n      style22.height = size + extra + \"px\";\n      style22.width = \"100%\";\n      style22.cursor = \"ns-resize\";\n    }\n    const className2 = cm(CLASSES.FLEXLAYOUT__SPLITTER_EXTRA);\n    return /* @__PURE__ */ jsx(\n      \"div\",\n      {\n        className,\n        style: style2,\n        ref: selfRef,\n        \"data-layout-path\": node.getPath() + \"/s\" + (index - 1),\n        onPointerDown,\n        children: /* @__PURE__ */ jsx(\n          \"div\",\n          {\n            style: style22,\n            ref: extendedRef,\n            className: className2,\n            onPointerDown\n          }\n        )\n      }\n    );\n  }\n};\nfunction BorderTab(props) {\n  const { layout, border, show } = props;\n  const selfRef = React.useRef(null);\n  const timer = React.useRef(void 0);\n  React.useLayoutEffect(() => {\n    const contentRect = layout.getBoundingClientRect(selfRef.current);\n    if (!isNaN(contentRect.x) && contentRect.width > 0) {\n      if (!border.getContentRect().equals(contentRect)) {\n        border.setContentRect(contentRect);\n        if (splitterDragging) {\n          if (timer.current) {\n            clearTimeout(timer.current);\n          }\n          timer.current = setTimeout(() => {\n            layout.redrawInternal(\"border content rect \" + contentRect);\n            timer.current = void 0;\n          }, 50);\n        } else {\n          layout.redrawInternal(\"border content rect \" + contentRect);\n        }\n      }\n    }\n  });\n  let horizontal = true;\n  const style2 = {};\n  if (border.getOrientation() === Orientation.HORZ) {\n    style2.width = border.getSize();\n    style2.minWidth = border.getMinSize();\n    style2.maxWidth = border.getMaxSize();\n  } else {\n    style2.height = border.getSize();\n    style2.minHeight = border.getMinSize();\n    style2.maxHeight = border.getMaxSize();\n    horizontal = false;\n  }\n  style2.display = show ? \"flex\" : \"none\";\n  const className = layout.getClassName(CLASSES.FLEXLAYOUT__BORDER_TAB_CONTENTS);\n  if (border.getLocation() === DockLocation.LEFT || border.getLocation() === DockLocation.TOP) {\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(\"div\", { ref: selfRef, style: style2, className }),\n      show && /* @__PURE__ */ jsx(Splitter, { layout, node: border, index: 0, horizontal })\n    ] });\n  } else {\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      show && /* @__PURE__ */ jsx(Splitter, { layout, node: border, index: 0, horizontal }),\n      /* @__PURE__ */ jsx(\"div\", { ref: selfRef, style: style2, className })\n    ] });\n  }\n}\nvar ICloseType = /* @__PURE__ */ ((ICloseType2) => {\n  ICloseType2[ICloseType2[\"Visible\"] = 1] = \"Visible\";\n  ICloseType2[ICloseType2[\"Always\"] = 2] = \"Always\";\n  ICloseType2[ICloseType2[\"Selected\"] = 3] = \"Selected\";\n  return ICloseType2;\n})(ICloseType || {});\nconst BorderButton = (props) => {\n  const { layout, node, selected, border, icons, path } = props;\n  const selfRef = React.useRef(null);\n  const contentRef = React.useRef(null);\n  const onDragStart = (event) => {\n    if (node.isEnableDrag()) {\n      event.stopPropagation();\n      layout.setDragNode(event.nativeEvent, node);\n    } else {\n      event.preventDefault();\n    }\n  };\n  const onDragEnd = (event) => {\n    event.stopPropagation();\n    layout.clearDragMain();\n  };\n  const onAuxMouseClick = (event) => {\n    if (isAuxMouseEvent(event)) {\n      layout.auxMouseClick(node, event);\n    }\n  };\n  const onContextMenu = (event) => {\n    layout.showContextMenu(node, event);\n  };\n  const onClick = () => {\n    layout.doAction(Actions.selectTab(node.getId()));\n  };\n  const isClosable = () => {\n    const closeType = node.getCloseType();\n    if (selected || closeType === ICloseType.Always) {\n      return true;\n    }\n    if (closeType === ICloseType.Visible) {\n      if (window.matchMedia && window.matchMedia(\"(hover: hover) and (pointer: fine)\").matches) {\n        return true;\n      }\n    }\n    return false;\n  };\n  const onClose = (event) => {\n    if (isClosable()) {\n      layout.doAction(Actions.deleteTab(node.getId()));\n      event.stopPropagation();\n    }\n  };\n  const onClosePointerDown = (event) => {\n    event.stopPropagation();\n  };\n  React.useLayoutEffect(() => {\n    node.setTabRect(layout.getBoundingClientRect(selfRef.current));\n    if (layout.getEditingTab() === node) {\n      contentRef.current.select();\n    }\n  });\n  const onTextBoxPointerDown = (event) => {\n    event.stopPropagation();\n  };\n  const onTextBoxKeyPress = (event) => {\n    if (event.code === \"Escape\") {\n      layout.setEditingTab(void 0);\n    } else if (event.code === \"Enter\" || event.code === \"NumpadEnter\") {\n      layout.setEditingTab(void 0);\n      layout.doAction(Actions.renameTab(node.getId(), event.target.value));\n    }\n  };\n  const cm = layout.getClassName;\n  let classNames = cm(CLASSES.FLEXLAYOUT__BORDER_BUTTON) + \" \" + cm(CLASSES.FLEXLAYOUT__BORDER_BUTTON_ + border);\n  if (selected) {\n    classNames += \" \" + cm(CLASSES.FLEXLAYOUT__BORDER_BUTTON__SELECTED);\n  } else {\n    classNames += \" \" + cm(CLASSES.FLEXLAYOUT__BORDER_BUTTON__UNSELECTED);\n  }\n  if (node.getClassName() !== void 0) {\n    classNames += \" \" + node.getClassName();\n  }\n  let iconAngle = 0;\n  if (node.getModel().isEnableRotateBorderIcons() === false) {\n    if (border === \"left\") {\n      iconAngle = 90;\n    } else if (border === \"right\") {\n      iconAngle = -90;\n    }\n  }\n  const renderState = getRenderStateEx(layout, node, iconAngle);\n  let content = renderState.content ? /* @__PURE__ */ jsx(\"div\", { className: cm(CLASSES.FLEXLAYOUT__BORDER_BUTTON_CONTENT), children: renderState.content }) : null;\n  const leading = renderState.leading ? /* @__PURE__ */ jsx(\"div\", { className: cm(CLASSES.FLEXLAYOUT__BORDER_BUTTON_LEADING), children: renderState.leading }) : null;\n  if (layout.getEditingTab() === node) {\n    content = /* @__PURE__ */ jsx(\n      \"input\",\n      {\n        ref: contentRef,\n        className: cm(CLASSES.FLEXLAYOUT__TAB_BUTTON_TEXTBOX),\n        \"data-layout-path\": path + \"/textbox\",\n        type: \"text\",\n        autoFocus: true,\n        defaultValue: node.getName(),\n        onKeyDown: onTextBoxKeyPress,\n        onPointerDown: onTextBoxPointerDown\n      }\n    );\n  }\n  if (node.isEnableClose()) {\n    const closeTitle = layout.i18nName(I18nLabel.Close_Tab);\n    renderState.buttons.push(\n      /* @__PURE__ */ jsx(\n        \"div\",\n        {\n          \"data-layout-path\": path + \"/button/close\",\n          title: closeTitle,\n          className: cm(CLASSES.FLEXLAYOUT__BORDER_BUTTON_TRAILING),\n          onPointerDown: onClosePointerDown,\n          onClick: onClose,\n          children: typeof icons.close === \"function\" ? icons.close(node) : icons.close\n        },\n        \"close\"\n      )\n    );\n  }\n  return /* @__PURE__ */ jsxs(\n    \"div\",\n    {\n      ref: selfRef,\n      \"data-layout-path\": path,\n      className: classNames,\n      onClick,\n      onAuxClick: onAuxMouseClick,\n      onContextMenu,\n      title: node.getHelpText(),\n      draggable: true,\n      onDragStart,\n      onDragEnd,\n      children: [\n        leading,\n        content,\n        renderState.buttons\n      ]\n    }\n  );\n};\nconst TabButtonStamp = (props) => {\n  const { layout, node } = props;\n  const cm = layout.getClassName;\n  const classNames = cm(CLASSES.FLEXLAYOUT__TAB_BUTTON_STAMP);\n  const renderState = getRenderStateEx(layout, node);\n  const content = renderState.content ? /* @__PURE__ */ jsx(\"div\", { className: cm(CLASSES.FLEXLAYOUT__TAB_BUTTON_CONTENT), children: renderState.content }) : node.getNameForOverflowMenu();\n  const leading = renderState.leading ? /* @__PURE__ */ jsx(\"div\", { className: cm(CLASSES.FLEXLAYOUT__TAB_BUTTON_LEADING), children: renderState.leading }) : null;\n  return /* @__PURE__ */ jsxs(\n    \"div\",\n    {\n      className: classNames,\n      title: node.getHelpText(),\n      children: [\n        leading,\n        content\n      ]\n    }\n  );\n};\nfunction showPopup(triggerElement, parentNode, items, onSelect, layout) {\n  const layoutDiv = layout.getRootDiv();\n  const classNameMapper = layout.getClassName;\n  const currentDocument = triggerElement.ownerDocument;\n  const triggerRect = triggerElement.getBoundingClientRect();\n  const layoutRect = (layoutDiv == null ? void 0 : layoutDiv.getBoundingClientRect()) ?? new DOMRect(0, 0, 100, 100);\n  const elm = currentDocument.createElement(\"div\");\n  elm.className = classNameMapper(CLASSES.FLEXLAYOUT__POPUP_MENU_CONTAINER);\n  if (triggerRect.left < layoutRect.left + layoutRect.width / 2) {\n    elm.style.left = triggerRect.left - layoutRect.left + \"px\";\n  } else {\n    elm.style.right = layoutRect.right - triggerRect.right + \"px\";\n  }\n  if (triggerRect.top < layoutRect.top + layoutRect.height / 2) {\n    elm.style.top = triggerRect.top - layoutRect.top + \"px\";\n  } else {\n    elm.style.bottom = layoutRect.bottom - triggerRect.bottom + \"px\";\n  }\n  layout.showOverlay(true);\n  if (layoutDiv) {\n    layoutDiv.appendChild(elm);\n  }\n  const onHide = () => {\n    layout.hideControlInPortal();\n    layout.showOverlay(false);\n    if (layoutDiv) {\n      layoutDiv.removeChild(elm);\n    }\n    elm.removeEventListener(\"pointerdown\", onElementPointerDown);\n    currentDocument.removeEventListener(\"pointerdown\", onDocPointerDown);\n  };\n  const onElementPointerDown = (event) => {\n    event.stopPropagation();\n  };\n  const onDocPointerDown = (_event) => {\n    onHide();\n  };\n  elm.addEventListener(\"pointerdown\", onElementPointerDown);\n  currentDocument.addEventListener(\"pointerdown\", onDocPointerDown);\n  layout.showControlInPortal(/* @__PURE__ */ jsx(\n    PopupMenu,\n    {\n      currentDocument,\n      parentNode,\n      onSelect,\n      onHide,\n      items,\n      classNameMapper,\n      layout\n    }\n  ), elm);\n}\nconst PopupMenu = (props) => {\n  const { parentNode, items, onHide, onSelect, classNameMapper, layout } = props;\n  const divRef = useRef(null);\n  useEffect(() => {\n    if (divRef.current) {\n      divRef.current.focus();\n    }\n  }, []);\n  const onItemClick = (item, event) => {\n    onSelect(item);\n    onHide();\n    event.stopPropagation();\n  };\n  const onDragStart = (event, node) => {\n    event.stopPropagation();\n    layout.setDragNode(event.nativeEvent, node);\n    setTimeout(() => {\n      onHide();\n    }, 0);\n  };\n  const onDragEnd = (event) => {\n    layout.clearDragMain();\n  };\n  const handleKeyDown = (event) => {\n    if (event.key === \"Escape\") {\n      onHide();\n    }\n  };\n  const itemElements = items.map(\n    (item, i) => {\n      let classes = classNameMapper(CLASSES.FLEXLAYOUT__POPUP_MENU_ITEM);\n      if (parentNode.getSelected() === item.index) {\n        classes += \" \" + classNameMapper(CLASSES.FLEXLAYOUT__POPUP_MENU_ITEM__SELECTED);\n      }\n      return /* @__PURE__ */ jsx(\n        \"div\",\n        {\n          className: classes,\n          \"data-layout-path\": \"/popup-menu/tb\" + i,\n          onClick: (event) => onItemClick(item, event),\n          draggable: true,\n          onDragStart: (e) => onDragStart(e, item.node),\n          onDragEnd,\n          title: item.node.getHelpText(),\n          children: /* @__PURE__ */ jsx(\n            TabButtonStamp,\n            {\n              node: item.node,\n              layout\n            }\n          )\n        },\n        item.index\n      );\n    }\n  );\n  return /* @__PURE__ */ jsx(\n    \"div\",\n    {\n      className: classNameMapper(CLASSES.FLEXLAYOUT__POPUP_MENU),\n      ref: divRef,\n      tabIndex: 0,\n      onKeyDown: handleKeyDown,\n      \"data-layout-path\": \"/popup-menu\",\n      children: itemElements\n    }\n  );\n};\nconst useTabOverflow = (layout, node, orientation, tabStripRef, miniScrollRef, tabClassName) => {\n  const [hiddenTabs, setHiddenTabs] = React.useState([]);\n  const [isShowHiddenTabs, setShowHiddenTabs] = React.useState(false);\n  const [isDockStickyButtons, setDockStickyButtons] = React.useState(false);\n  const selfRef = React.useRef(null);\n  const userControlledPositionRef = React.useRef(false);\n  const updateHiddenTabsTimerRef = React.useRef(void 0);\n  const hiddenTabsRef = React.useRef([]);\n  const thumbInternalPos = React.useRef(0);\n  const repositioningRef = React.useRef(false);\n  hiddenTabsRef.current = hiddenTabs;\n  React.useLayoutEffect(() => {\n    if (tabStripRef.current) {\n      setScrollPosition(0);\n    }\n  }, [node.getId()]);\n  React.useLayoutEffect(() => {\n    userControlledPositionRef.current = false;\n  }, [node.getSelectedNode(), node.getRect().width, node.getRect().height]);\n  React.useLayoutEffect(() => {\n    checkForOverflow();\n    if (userControlledPositionRef.current === false) {\n      scrollIntoView();\n    }\n    updateScrollMetrics();\n    updateHiddenTabs();\n  });\n  React.useEffect(() => {\n    var _a;\n    (_a = selfRef.current) == null ? void 0 : _a.addEventListener(\"wheel\", onWheel, { passive: false });\n    return () => {\n      var _a2;\n      (_a2 = selfRef.current) == null ? void 0 : _a2.removeEventListener(\"wheel\", onWheel);\n    };\n  }, [selfRef.current]);\n  const onWheel = (event) => {\n    event.preventDefault();\n  };\n  function scrollIntoView() {\n    const selectedTabNode = node.getSelectedNode();\n    if (selectedTabNode && tabStripRef.current) {\n      const stripRect = layout.getBoundingClientRect(tabStripRef.current);\n      const selectedRect = selectedTabNode.getTabRect();\n      let shift = getNear(stripRect) - getNear(selectedRect);\n      if (shift > 0 || getSize(selectedRect) > getSize(stripRect)) {\n        setScrollPosition(getScrollPosition(tabStripRef.current) - shift);\n        repositioningRef.current = true;\n      } else {\n        shift = getFar(selectedRect) - getFar(stripRect);\n        if (shift > 0) {\n          setScrollPosition(getScrollPosition(tabStripRef.current) + shift);\n          repositioningRef.current = true;\n        }\n      }\n    }\n  }\n  const updateScrollMetrics = () => {\n    if (tabStripRef.current && miniScrollRef.current) {\n      const t = tabStripRef.current;\n      const s = miniScrollRef.current;\n      const size = getElementSize(t);\n      const scrollSize = getScrollSize(t);\n      const position = getScrollPosition(t);\n      if (scrollSize > size && scrollSize > 0) {\n        let thumbSize = size * size / scrollSize;\n        let adjust = 0;\n        if (thumbSize < 20) {\n          adjust = 20 - thumbSize;\n          thumbSize = 20;\n        }\n        const thumbPos = position * (size - adjust) / scrollSize;\n        if (orientation === Orientation.HORZ) {\n          s.style.width = thumbSize + \"px\";\n          s.style.left = thumbPos + \"px\";\n        } else {\n          s.style.height = thumbSize + \"px\";\n          s.style.top = thumbPos + \"px\";\n        }\n        s.style.display = \"block\";\n      } else {\n        s.style.display = \"none\";\n      }\n      if (orientation === Orientation.HORZ) {\n        s.style.bottom = \"0px\";\n      } else {\n        s.style.right = \"0px\";\n      }\n    }\n  };\n  const updateHiddenTabs = () => {\n    const newHiddenTabs = findHiddenTabs();\n    const showHidden = newHiddenTabs.length > 0;\n    if (showHidden !== isShowHiddenTabs) {\n      setShowHiddenTabs(showHidden);\n    }\n    if (updateHiddenTabsTimerRef.current === void 0) {\n      updateHiddenTabsTimerRef.current = setTimeout(() => {\n        const newHiddenTabs2 = findHiddenTabs();\n        if (!arraysEqual(newHiddenTabs2, hiddenTabsRef.current)) {\n          setHiddenTabs(newHiddenTabs2);\n        }\n        updateHiddenTabsTimerRef.current = void 0;\n      }, 100);\n    }\n  };\n  const onScroll = () => {\n    if (!repositioningRef.current) {\n      userControlledPositionRef.current = true;\n    }\n    repositioningRef.current = false;\n    updateScrollMetrics();\n    updateHiddenTabs();\n  };\n  const onScrollPointerDown = (event) => {\n    var _a;\n    event.stopPropagation();\n    miniScrollRef.current.setPointerCapture(event.pointerId);\n    const r = (_a = miniScrollRef.current) == null ? void 0 : _a.getBoundingClientRect();\n    if (orientation === Orientation.HORZ) {\n      thumbInternalPos.current = event.clientX - r.x;\n    } else {\n      thumbInternalPos.current = event.clientY - r.y;\n    }\n    startDrag(event.currentTarget.ownerDocument, event, onDragMove, onDragEnd, onDragCancel);\n  };\n  const onDragMove = (x, y) => {\n    if (tabStripRef.current && miniScrollRef.current) {\n      const t = tabStripRef.current;\n      const s = miniScrollRef.current;\n      const size = getElementSize(t);\n      const scrollSize = getScrollSize(t);\n      const thumbSize = getElementSize(s);\n      const r = t.getBoundingClientRect();\n      let thumb = 0;\n      if (orientation === Orientation.HORZ) {\n        thumb = x - r.x - thumbInternalPos.current;\n      } else {\n        thumb = y - r.y - thumbInternalPos.current;\n      }\n      thumb = Math.max(0, Math.min(scrollSize - thumbSize, thumb));\n      if (size > 0) {\n        const scrollPos = thumb * scrollSize / size;\n        setScrollPosition(scrollPos);\n      }\n    }\n  };\n  const onDragEnd = () => {\n  };\n  const onDragCancel = () => {\n  };\n  const checkForOverflow = () => {\n    if (tabStripRef.current) {\n      const strip = tabStripRef.current;\n      const tabContainer = strip.firstElementChild;\n      const offset = isDockStickyButtons ? 10 : 0;\n      const dock = getElementSize(tabContainer) + offset > getElementSize(tabStripRef.current);\n      if (dock !== isDockStickyButtons) {\n        setDockStickyButtons(dock);\n      }\n    }\n  };\n  const findHiddenTabs = () => {\n    const hidden = [];\n    if (tabStripRef.current) {\n      const strip = tabStripRef.current;\n      const stripRect = strip.getBoundingClientRect();\n      const visibleNear = getNear(stripRect) - 1;\n      const visibleFar = getFar(stripRect) + 1;\n      const tabContainer = strip.firstElementChild;\n      let i = 0;\n      Array.from(tabContainer.children).forEach((child) => {\n        const tabRect = child.getBoundingClientRect();\n        if (child.classList.contains(tabClassName)) {\n          if (getNear(tabRect) < visibleNear || getFar(tabRect) > visibleFar) {\n            hidden.push(i);\n          }\n          i++;\n        }\n      });\n    }\n    return hidden;\n  };\n  const onMouseWheel = (event) => {\n    if (tabStripRef.current) {\n      if (node.getChildren().length === 0) return;\n      let delta = 0;\n      if (Math.abs(event.deltaY) > 0) {\n        delta = -event.deltaY;\n        if (event.deltaMode === 1) {\n          delta *= 40;\n        }\n        const newPos = getScrollPosition(tabStripRef.current) - delta;\n        const maxScroll = getScrollSize(tabStripRef.current) - getElementSize(tabStripRef.current);\n        const p = Math.max(0, Math.min(maxScroll, newPos));\n        setScrollPosition(p);\n        event.stopPropagation();\n      }\n    }\n  };\n  const getNear = (rect) => {\n    if (orientation === Orientation.HORZ) {\n      return rect.x;\n    } else {\n      return rect.y;\n    }\n  };\n  const getFar = (rect) => {\n    if (orientation === Orientation.HORZ) {\n      return rect.right;\n    } else {\n      return rect.bottom;\n    }\n  };\n  const getElementSize = (elm) => {\n    if (orientation === Orientation.HORZ) {\n      return elm.clientWidth;\n    } else {\n      return elm.clientHeight;\n    }\n  };\n  const getSize = (rect) => {\n    if (orientation === Orientation.HORZ) {\n      return rect.width;\n    } else {\n      return rect.height;\n    }\n  };\n  const getScrollSize = (elm) => {\n    if (orientation === Orientation.HORZ) {\n      return elm.scrollWidth;\n    } else {\n      return elm.scrollHeight;\n    }\n  };\n  const setScrollPosition = (p) => {\n    if (orientation === Orientation.HORZ) {\n      tabStripRef.current.scrollLeft = p;\n    } else {\n      tabStripRef.current.scrollTop = p;\n    }\n  };\n  const getScrollPosition = (elm) => {\n    if (orientation === Orientation.HORZ) {\n      return elm.scrollLeft;\n    } else {\n      return elm.scrollTop;\n    }\n  };\n  return { selfRef, userControlledPositionRef, onScroll, onScrollPointerDown, hiddenTabs, onMouseWheel, isDockStickyButtons, isShowHiddenTabs };\n};\nfunction arraysEqual(arr1, arr2) {\n  return arr1.length === arr2.length && arr1.every((val, index) => val === arr2[index]);\n}\nconst BorderTabSet = (props) => {\n  const { border, layout, size } = props;\n  const toolbarRef = React.useRef(null);\n  const miniScrollRef = React.useRef(null);\n  const overflowbuttonRef = React.useRef(null);\n  const stickyButtonsRef = React.useRef(null);\n  const tabStripInnerRef = React.useRef(null);\n  const icons = layout.getIcons();\n  React.useLayoutEffect(() => {\n    border.setTabHeaderRect(layout.getBoundingClientRect(selfRef.current));\n  });\n  const { selfRef, userControlledPositionRef, onScroll, onScrollPointerDown, hiddenTabs, onMouseWheel, isDockStickyButtons, isShowHiddenTabs } = useTabOverflow(\n    layout,\n    border,\n    Orientation.flip(border.getOrientation()),\n    tabStripInnerRef,\n    miniScrollRef,\n    layout.getClassName(CLASSES.FLEXLAYOUT__BORDER_BUTTON)\n  );\n  const onAuxMouseClick = (event) => {\n    if (isAuxMouseEvent(event)) {\n      layout.auxMouseClick(border, event);\n    }\n  };\n  const onContextMenu = (event) => {\n    layout.showContextMenu(border, event);\n  };\n  const onInterceptPointerDown = (event) => {\n    event.stopPropagation();\n  };\n  const onOverflowClick = (event) => {\n    const callback = layout.getShowOverflowMenu();\n    const items = hiddenTabs.map((h) => {\n      return { index: h, node: border.getChildren()[h] };\n    });\n    if (callback !== void 0) {\n      callback(border, event, items, onOverflowItemSelect);\n    } else {\n      const element = overflowbuttonRef.current;\n      showPopup(\n        element,\n        border,\n        items,\n        onOverflowItemSelect,\n        layout\n      );\n    }\n    event.stopPropagation();\n  };\n  const onOverflowItemSelect = (item) => {\n    layout.doAction(Actions.selectTab(item.node.getId()));\n    userControlledPositionRef.current = false;\n  };\n  const onPopoutTab = (event) => {\n    const selectedTabNode = border.getChildren()[border.getSelected()];\n    if (selectedTabNode !== void 0) {\n      layout.doAction(Actions.popoutTab(selectedTabNode.getId()));\n    }\n    event.stopPropagation();\n  };\n  const cm = layout.getClassName;\n  const tabButtons = [];\n  const layoutTab = (i) => {\n    const isSelected = border.getSelected() === i;\n    const child = border.getChildren()[i];\n    tabButtons.push(\n      /* @__PURE__ */ jsx(\n        BorderButton,\n        {\n          layout,\n          border: border.getLocation().getName(),\n          node: child,\n          path: border.getPath() + \"/tb\" + i,\n          selected: isSelected,\n          icons\n        },\n        child.getId()\n      )\n    );\n    if (i < border.getChildren().length - 1) {\n      tabButtons.push(\n        /* @__PURE__ */ jsx(\"div\", { className: cm(CLASSES.FLEXLAYOUT__BORDER_TAB_DIVIDER) }, \"divider\" + i)\n      );\n    }\n  };\n  for (let i = 0; i < border.getChildren().length; i++) {\n    layoutTab(i);\n  }\n  let borderClasses = cm(CLASSES.FLEXLAYOUT__BORDER) + \" \" + cm(CLASSES.FLEXLAYOUT__BORDER_ + border.getLocation().getName());\n  if (border.getClassName() !== void 0) {\n    borderClasses += \" \" + border.getClassName();\n  }\n  let leading = void 0;\n  let buttons = [];\n  let stickyButtons = [];\n  const renderState = { leading, buttons, stickyButtons, overflowPosition: void 0 };\n  layout.customizeTabSet(border, renderState);\n  leading = renderState.leading;\n  stickyButtons = renderState.stickyButtons;\n  buttons = renderState.buttons;\n  if (renderState.overflowPosition === void 0) {\n    renderState.overflowPosition = stickyButtons.length;\n  }\n  if (stickyButtons.length > 0) {\n    if (isDockStickyButtons) {\n      buttons = [...stickyButtons, ...buttons];\n    } else {\n      tabButtons.push(/* @__PURE__ */ jsx(\n        \"div\",\n        {\n          ref: stickyButtonsRef,\n          onPointerDown: onInterceptPointerDown,\n          onDragStart: (e) => {\n            e.preventDefault();\n          },\n          className: cm(CLASSES.FLEXLAYOUT__TAB_TOOLBAR_STICKY_BUTTONS_CONTAINER),\n          children: stickyButtons\n        },\n        \"sticky_buttons_container\"\n      ));\n    }\n  }\n  if (isShowHiddenTabs) {\n    const overflowTitle = layout.i18nName(I18nLabel.Overflow_Menu_Tooltip);\n    let overflowContent;\n    if (typeof icons.more === \"function\") {\n      const items = hiddenTabs.map((h) => {\n        return { index: h, node: border.getChildren()[h] };\n      });\n      overflowContent = icons.more(border, items);\n    } else {\n      overflowContent = /* @__PURE__ */ jsxs(Fragment, { children: [\n        icons.more,\n        /* @__PURE__ */ jsx(\"div\", { className: cm(CLASSES.FLEXLAYOUT__TAB_BUTTON_OVERFLOW_COUNT), children: hiddenTabs.length > 0 ? hiddenTabs.length : \"\" })\n      ] });\n    }\n    buttons.splice(\n      Math.min(renderState.overflowPosition, buttons.length),\n      0,\n      /* @__PURE__ */ jsx(\n        \"button\",\n        {\n          ref: overflowbuttonRef,\n          className: cm(CLASSES.FLEXLAYOUT__BORDER_TOOLBAR_BUTTON) + \" \" + cm(CLASSES.FLEXLAYOUT__BORDER_TOOLBAR_BUTTON_OVERFLOW) + \" \" + cm(CLASSES.FLEXLAYOUT__BORDER_TOOLBAR_BUTTON_OVERFLOW_ + border.getLocation().getName()),\n          title: overflowTitle,\n          onClick: onOverflowClick,\n          onPointerDown: onInterceptPointerDown,\n          children: overflowContent\n        },\n        \"overflowbutton\"\n      )\n    );\n  }\n  const selectedIndex = border.getSelected();\n  if (selectedIndex !== -1) {\n    const selectedTabNode = border.getChildren()[selectedIndex];\n    if (selectedTabNode !== void 0 && layout.isSupportsPopout() && selectedTabNode.isEnablePopout()) {\n      const popoutTitle = layout.i18nName(I18nLabel.Popout_Tab);\n      buttons.push(\n        /* @__PURE__ */ jsx(\n          \"button\",\n          {\n            title: popoutTitle,\n            className: cm(CLASSES.FLEXLAYOUT__BORDER_TOOLBAR_BUTTON) + \" \" + cm(CLASSES.FLEXLAYOUT__BORDER_TOOLBAR_BUTTON_FLOAT),\n            onClick: onPopoutTab,\n            onPointerDown: onInterceptPointerDown,\n            children: typeof icons.popout === \"function\" ? icons.popout(selectedTabNode) : icons.popout\n          },\n          \"popout\"\n        )\n      );\n    }\n  }\n  const toolbar = /* @__PURE__ */ jsx(\"div\", { ref: toolbarRef, className: cm(CLASSES.FLEXLAYOUT__BORDER_TOOLBAR) + \" \" + cm(CLASSES.FLEXLAYOUT__BORDER_TOOLBAR_ + border.getLocation().getName()), children: buttons }, \"toolbar\");\n  let innerStyle = {};\n  let outerStyle = {};\n  const borderHeight = size - 1;\n  if (border.getLocation() === DockLocation.LEFT) {\n    innerStyle = { right: \"100%\", top: 0 };\n    outerStyle = { width: borderHeight, overflowY: \"auto\" };\n  } else if (border.getLocation() === DockLocation.RIGHT) {\n    innerStyle = { left: \"100%\", top: 0 };\n    outerStyle = { width: borderHeight, overflowY: \"auto\" };\n  } else {\n    innerStyle = { left: 0 };\n    outerStyle = { height: borderHeight, overflowX: \"auto\" };\n  }\n  let miniScrollbar = void 0;\n  if (border.isEnableTabScrollbar()) {\n    miniScrollbar = /* @__PURE__ */ jsx(\n      \"div\",\n      {\n        ref: miniScrollRef,\n        className: cm(CLASSES.FLEXLAYOUT__MINI_SCROLLBAR),\n        onPointerDown: onScrollPointerDown\n      }\n    );\n  }\n  let leadingContainer = void 0;\n  if (leading) {\n    leadingContainer = /* @__PURE__ */ jsx(\"div\", { className: cm(CLASSES.FLEXLAYOUT__BORDER_LEADING), children: leading });\n  }\n  return /* @__PURE__ */ jsxs(\n    \"div\",\n    {\n      ref: selfRef,\n      style: {\n        display: \"flex\",\n        flexDirection: border.getOrientation() === Orientation.VERT ? \"row\" : \"column\"\n      },\n      className: borderClasses,\n      \"data-layout-path\": border.getPath(),\n      onClick: onAuxMouseClick,\n      onAuxClick: onAuxMouseClick,\n      onContextMenu,\n      onWheel: onMouseWheel,\n      children: [\n        leadingContainer,\n        /* @__PURE__ */ jsxs(\"div\", { className: cm(CLASSES.FLEXLAYOUT__MINI_SCROLLBAR_CONTAINER), children: [\n          /* @__PURE__ */ jsx(\n            \"div\",\n            {\n              ref: tabStripInnerRef,\n              className: cm(CLASSES.FLEXLAYOUT__BORDER_INNER) + \" \" + cm(CLASSES.FLEXLAYOUT__BORDER_INNER_ + border.getLocation().getName()),\n              style: outerStyle,\n              onScroll,\n              children: /* @__PURE__ */ jsx(\n                \"div\",\n                {\n                  style: innerStyle,\n                  className: cm(CLASSES.FLEXLAYOUT__BORDER_INNER_TAB_CONTAINER) + \" \" + cm(CLASSES.FLEXLAYOUT__BORDER_INNER_TAB_CONTAINER_ + border.getLocation().getName()),\n                  children: tabButtons\n                }\n              )\n            }\n          ),\n          miniScrollbar\n        ] }),\n        toolbar\n      ]\n    }\n  );\n};\nconst DragContainer = (props) => {\n  const { layout, node } = props;\n  const selfRef = React.useRef(null);\n  React.useEffect(() => {\n    node.setTabStamp(selfRef.current);\n  }, [node, selfRef.current]);\n  const cm = layout.getClassName;\n  const classNames = cm(CLASSES.FLEXLAYOUT__DRAG_RECT);\n  return /* @__PURE__ */ jsx(\n    \"div\",\n    {\n      ref: selfRef,\n      className: classNames,\n      children: /* @__PURE__ */ jsx(TabButtonStamp, { layout, node }, node.getId())\n    }\n  );\n};\nconst PopoutWindow = (props) => {\n  const { title, layout, layoutWindow, url, onCloseWindow, onSetWindow, children } = props;\n  const popoutWindow = React.useRef(null);\n  const [content, setContent] = React.useState(void 0);\n  const styleMap = /* @__PURE__ */ new Map();\n  React.useLayoutEffect(() => {\n    if (!popoutWindow.current) {\n      const windowId = layoutWindow.windowId;\n      const rect = layoutWindow.rect;\n      popoutWindow.current = window.open(url, windowId, `left=${rect.x},top=${rect.y},width=${rect.width},height=${rect.height}`);\n      if (popoutWindow.current) {\n        layoutWindow.window = popoutWindow.current;\n        onSetWindow(layoutWindow, popoutWindow.current);\n        window.addEventListener(\"beforeunload\", () => {\n          if (popoutWindow.current) {\n            const closedWindow = popoutWindow.current;\n            popoutWindow.current = null;\n            closedWindow.close();\n          }\n        });\n        popoutWindow.current.addEventListener(\"load\", () => {\n          if (popoutWindow.current) {\n            popoutWindow.current.focus();\n            popoutWindow.current.resizeTo(rect.width, rect.height);\n            popoutWindow.current.moveTo(rect.x, rect.y);\n            const popoutDocument = popoutWindow.current.document;\n            popoutDocument.title = title;\n            const popoutContent = popoutDocument.createElement(\"div\");\n            popoutContent.className = CLASSES.FLEXLAYOUT__FLOATING_WINDOW_CONTENT;\n            popoutDocument.body.appendChild(popoutContent);\n            copyStyles(popoutDocument, styleMap).then(() => {\n              setContent(popoutContent);\n            });\n            const observer = new MutationObserver((mutationsList) => handleStyleMutations(mutationsList, popoutDocument, styleMap));\n            observer.observe(document.head, { childList: true });\n            popoutWindow.current.addEventListener(\"beforeunload\", () => {\n              if (popoutWindow.current) {\n                onCloseWindow(layoutWindow);\n                popoutWindow.current = null;\n                observer.disconnect();\n              }\n            });\n          }\n        });\n      } else {\n        console.warn(`Unable to open window ${url}`);\n        onCloseWindow(layoutWindow);\n      }\n    }\n    return () => {\n      var _a;\n      if (!layout.getModel().getwindowsMap().has(layoutWindow.windowId)) {\n        (_a = popoutWindow.current) == null ? void 0 : _a.close();\n        popoutWindow.current = null;\n      }\n    };\n  }, []);\n  if (content !== void 0) {\n    return createPortal(children, content);\n  } else {\n    return null;\n  }\n};\nfunction handleStyleMutations(mutationsList, popoutDocument, styleMap) {\n  for (const mutation of mutationsList) {\n    if (mutation.type === \"childList\") {\n      for (const addition of mutation.addedNodes) {\n        if (addition instanceof HTMLLinkElement || addition instanceof HTMLStyleElement) {\n          copyStyle(popoutDocument, addition, styleMap);\n        }\n      }\n      for (const removal of mutation.removedNodes) {\n        if (removal instanceof HTMLLinkElement || removal instanceof HTMLStyleElement) {\n          const popoutStyle = styleMap.get(removal);\n          if (popoutStyle) {\n            popoutDocument.head.removeChild(popoutStyle);\n          }\n        }\n      }\n    }\n  }\n}\nfunction copyStyles(popoutDoc, styleMap) {\n  const promises = [];\n  const styleElements = document.querySelectorAll('style, link[rel=\"stylesheet\"]');\n  for (const element of styleElements) {\n    copyStyle(popoutDoc, element, styleMap, promises);\n  }\n  return Promise.all(promises);\n}\nfunction copyStyle(popoutDoc, element, styleMap, promises) {\n  if (element instanceof HTMLLinkElement) {\n    const linkElement = element.cloneNode(true);\n    popoutDoc.head.appendChild(linkElement);\n    styleMap.set(element, linkElement);\n    if (promises) {\n      promises.push(new Promise((resolve) => {\n        linkElement.onload = () => resolve(true);\n      }));\n    }\n  } else if (element instanceof HTMLStyleElement) {\n    try {\n      const styleElement = element.cloneNode(true);\n      popoutDoc.head.appendChild(styleElement);\n      styleMap.set(element, styleElement);\n    } catch (e) {\n    }\n  }\n}\nconst style = { width: \"1em\", height: \"1em\", display: \"flex\", alignItems: \"center\" };\nconst CloseIcon = () => {\n  return /* @__PURE__ */ jsxs(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", style, viewBox: \"0 0 24 24\", children: [\n    /* @__PURE__ */ jsx(\"path\", { fill: \"none\", d: \"M0 0h24v24H0z\" }),\n    /* @__PURE__ */ jsx(\"path\", { stroke: \"var(--color-icon)\", fill: \"var(--color-icon)\", d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\" })\n  ] });\n};\nconst MaximizeIcon = () => {\n  return /* @__PURE__ */ jsxs(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", style, viewBox: \"0 0 24 24\", fill: \"var(--color-icon)\", children: [\n    /* @__PURE__ */ jsx(\"path\", { d: \"M0 0h24v24H0z\", fill: \"none\" }),\n    /* @__PURE__ */ jsx(\"path\", { stroke: \"var(--color-icon)\", d: \"M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z\" })\n  ] });\n};\nconst OverflowIcon = () => {\n  return /* @__PURE__ */ jsxs(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", style, viewBox: \"0 0 24 24\", fill: \"var(--color-icon)\", children: [\n    /* @__PURE__ */ jsx(\"path\", { d: \"M0 0h24v24H0z\", fill: \"none\" }),\n    /* @__PURE__ */ jsx(\"path\", { stroke: \"var(--color-icon)\", d: \"M7 10l5 5 5-5z\" })\n  ] });\n};\nconst EdgeIcon = () => {\n  return /* @__PURE__ */ jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", style: { display: \"block\", width: 10, height: 10 }, preserveAspectRatio: \"none\", viewBox: \"0 0 100 100\", children: /* @__PURE__ */ jsx(\n    \"path\",\n    {\n      fill: \"var(--color-edge-icon)\",\n      stroke: \"var(--color-edge-icon)\",\n      d: \"M10 30 L90 30 l-40 40 Z\"\n    }\n  ) });\n};\nconst PopoutIcon = () => {\n  return (\n    // <svg xmlns=\"http://www.w3.org/2000/svg\"  style={style}  viewBox=\"0 0 24 24\" fill=\"var(--color-icon)\"><path d=\"M0 0h24v24H0z\" fill=\"none\"/><path stroke=\"var(--color-icon)\" d=\"M9 5v2h6.59L4 18.59 5.41 20 17 8.41V15h2V5z\"/></svg>\n    // <svg xmlns=\"http://www.w3.org/2000/svg\" style={style} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"var(--color-icon)\" stroke-width=\"2\">\n    //     <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n    // </svg>\n    /* @__PURE__ */ jsxs(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", style, viewBox: \"0 0 20 20\", fill: \"var(--color-icon)\", children: [\n      /* @__PURE__ */ jsx(\"path\", { d: \"M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z\" }),\n      /* @__PURE__ */ jsx(\"path\", { d: \"M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z\" })\n    ] })\n  );\n};\nconst RestoreIcon = () => {\n  return /* @__PURE__ */ jsxs(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", style, viewBox: \"0 0 24 24\", fill: \"var(--color-icon)\", children: [\n    /* @__PURE__ */ jsx(\"path\", { d: \"M0 0h24v24H0z\", fill: \"none\" }),\n    /* @__PURE__ */ jsx(\"path\", { stroke: \"var(--color-icon)\", d: \"M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z\" })\n  ] });\n};\nconst AsterickIcon = () => {\n  return /* @__PURE__ */ jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", style, height: \"24px\", viewBox: \"0 -960 960 960\", width: \"24px\", children: /* @__PURE__ */ jsx(\"path\", { fill: \"var(--color-icon)\", stroke: \"var(--color-icon)\", d: \"M440-120v-264L254-197l-57-57 187-186H120v-80h264L197-706l57-57 186 187v-264h80v264l186-187 57 57-187 186h264v80H576l187 186-57 57-186-187v264h-80Z\" }) });\n};\nconst AddIcon = () => {\n  return /* @__PURE__ */ jsxs(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", style, height: \"24px\", viewBox: \"0 0 24 24\", fill: \"var(--color-icon)\", children: [\n    /* @__PURE__ */ jsx(\"path\", { d: \"M0 0h24v24H0z\", fill: \"none\" }),\n    /* @__PURE__ */ jsx(\"path\", { stroke: \"var(--color-icon)\", d: \"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\" })\n  ] });\n};\nconst MenuIcon = () => {\n  return /* @__PURE__ */ jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", style, height: \"24px\", width: \"24px\", viewBox: \"0 -960 960 960\", fill: \"var(--color-icon)\", children: /* @__PURE__ */ jsx(\"path\", { d: \"M120-240v-80h720v80H120Zm0-200v-80h720v80H120Zm0-200v-80h720v80H120Z\" }) });\n};\nconst SettingsIcon = (props) => {\n  return /* @__PURE__ */ jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", ...props, style, viewBox: \"0 0 24 24\", fill: \"var(--color-icon)\", children: /* @__PURE__ */ jsxs(\"g\", { children: [\n    /* @__PURE__ */ jsx(\"path\", { d: \"M0,0h24v24H0V0z\", fill: \"none\" }),\n    /* @__PURE__ */ jsx(\"path\", { d: \"M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z\" })\n  ] }) });\n};\nconst Overlay = (props) => {\n  const { layout, show } = props;\n  return /* @__PURE__ */ jsx(\n    \"div\",\n    {\n      className: layout.getClassName(CLASSES.FLEXLAYOUT__LAYOUT_OVERLAY),\n      style: {\n        display: show ? \"flex\" : \"none\"\n      }\n    }\n  );\n};\nconst TabButton = (props) => {\n  const { layout, node, selected, path } = props;\n  const selfRef = React.useRef(null);\n  const contentRef = React.useRef(null);\n  const icons = layout.getIcons();\n  React.useLayoutEffect(() => {\n    node.setTabRect(layout.getBoundingClientRect(selfRef.current));\n    if (layout.getEditingTab() === node) {\n      contentRef.current.select();\n    }\n  });\n  const onDragStart = (event) => {\n    if (node.isEnableDrag()) {\n      event.stopPropagation();\n      layout.setDragNode(event.nativeEvent, node);\n    } else {\n      event.preventDefault();\n    }\n  };\n  const onDragEnd = (event) => {\n    layout.clearDragMain();\n  };\n  const onAuxMouseClick = (event) => {\n    if (isAuxMouseEvent(event)) {\n      layout.auxMouseClick(node, event);\n    }\n  };\n  const onContextMenu = (event) => {\n    layout.showContextMenu(node, event);\n  };\n  const onClick = () => {\n    layout.doAction(Actions.selectTab(node.getId()));\n  };\n  const onDoubleClick = (event) => {\n    if (node.isEnableRename()) {\n      onRename();\n      event.stopPropagation();\n    }\n  };\n  const onRename = () => {\n    layout.setEditingTab(node);\n    layout.getCurrentDocument().body.addEventListener(\"pointerdown\", onEndEdit);\n  };\n  const onEndEdit = (event) => {\n    if (event.target !== contentRef.current) {\n      layout.getCurrentDocument().body.removeEventListener(\"pointerdown\", onEndEdit);\n      layout.setEditingTab(void 0);\n    }\n  };\n  const isClosable = () => {\n    const closeType = node.getCloseType();\n    if (selected || closeType === ICloseType.Always) {\n      return true;\n    }\n    if (closeType === ICloseType.Visible) {\n      if (window.matchMedia && window.matchMedia(\"(hover: hover) and (pointer: fine)\").matches) {\n        return true;\n      }\n    }\n    return false;\n  };\n  const onClose = (event) => {\n    if (isClosable()) {\n      layout.doAction(Actions.deleteTab(node.getId()));\n      event.stopPropagation();\n    }\n  };\n  const onClosePointerDown = (event) => {\n    event.stopPropagation();\n  };\n  const onTextBoxPointerDown = (event) => {\n    event.stopPropagation();\n  };\n  const onTextBoxKeyPress = (event) => {\n    if (event.code === \"Escape\") {\n      layout.setEditingTab(void 0);\n    } else if (event.code === \"Enter\" || event.code === \"NumpadEnter\") {\n      layout.setEditingTab(void 0);\n      layout.doAction(Actions.renameTab(node.getId(), event.target.value));\n    }\n  };\n  const cm = layout.getClassName;\n  const parentNode = node.getParent();\n  const isStretch = parentNode.isEnableSingleTabStretch() && parentNode.getChildren().length === 1;\n  const baseClassName = isStretch ? CLASSES.FLEXLAYOUT__TAB_BUTTON_STRETCH : CLASSES.FLEXLAYOUT__TAB_BUTTON;\n  let classNames = cm(baseClassName);\n  classNames += \" \" + cm(baseClassName + \"_\" + parentNode.getTabLocation());\n  if (!isStretch) {\n    if (selected) {\n      classNames += \" \" + cm(baseClassName + \"--selected\");\n    } else {\n      classNames += \" \" + cm(baseClassName + \"--unselected\");\n    }\n  }\n  if (node.getClassName() !== void 0) {\n    classNames += \" \" + node.getClassName();\n  }\n  const renderState = getRenderStateEx(layout, node);\n  let content = renderState.content ? /* @__PURE__ */ jsx(\"div\", { className: cm(CLASSES.FLEXLAYOUT__TAB_BUTTON_CONTENT), children: renderState.content }) : null;\n  const leading = renderState.leading ? /* @__PURE__ */ jsx(\"div\", { className: cm(CLASSES.FLEXLAYOUT__TAB_BUTTON_LEADING), children: renderState.leading }) : null;\n  if (layout.getEditingTab() === node) {\n    content = /* @__PURE__ */ jsx(\n      \"input\",\n      {\n        ref: contentRef,\n        className: cm(CLASSES.FLEXLAYOUT__TAB_BUTTON_TEXTBOX),\n        \"data-layout-path\": path + \"/textbox\",\n        type: \"text\",\n        autoFocus: true,\n        defaultValue: node.getName(),\n        onKeyDown: onTextBoxKeyPress,\n        onPointerDown: onTextBoxPointerDown\n      }\n    );\n  }\n  if (node.isEnableClose() && !isStretch) {\n    const closeTitle = layout.i18nName(I18nLabel.Close_Tab);\n    renderState.buttons.push(\n      /* @__PURE__ */ jsx(\n        \"div\",\n        {\n          \"data-layout-path\": path + \"/button/close\",\n          title: closeTitle,\n          className: cm(CLASSES.FLEXLAYOUT__TAB_BUTTON_TRAILING),\n          onPointerDown: onClosePointerDown,\n          onClick: onClose,\n          children: typeof icons.close === \"function\" ? icons.close(node) : icons.close\n        },\n        \"close\"\n      )\n    );\n  }\n  return /* @__PURE__ */ jsxs(\n    \"div\",\n    {\n      ref: selfRef,\n      \"data-layout-path\": path,\n      className: classNames,\n      onClick,\n      onAuxClick: onAuxMouseClick,\n      onContextMenu,\n      title: node.getHelpText(),\n      draggable: true,\n      onDragStart,\n      onDragEnd,\n      onDoubleClick,\n      children: [\n        leading,\n        content,\n        renderState.buttons\n      ]\n    }\n  );\n};\nconst TabSet = (props) => {\n  const { node, layout } = props;\n  const tabStripRef = React.useRef(null);\n  const miniScrollRef = React.useRef(null);\n  const tabStripInnerRef = React.useRef(null);\n  const contentRef = React.useRef(null);\n  const buttonBarRef = React.useRef(null);\n  const overflowbuttonRef = React.useRef(null);\n  const stickyButtonsRef = React.useRef(null);\n  const timer = React.useRef(void 0);\n  const icons = layout.getIcons();\n  React.useLayoutEffect(() => {\n    node.setRect(layout.getBoundingClientRect(selfRef.current));\n    if (tabStripRef.current) {\n      node.setTabStripRect(layout.getBoundingClientRect(tabStripRef.current));\n    }\n    const newContentRect = layout.getBoundingClientRect(contentRef.current);\n    if (!node.getContentRect().equals(newContentRect) && !isNaN(newContentRect.x)) {\n      node.setContentRect(newContentRect);\n      if (splitterDragging) {\n        if (timer.current) {\n          clearTimeout(timer.current);\n        }\n        timer.current = setTimeout(() => {\n          layout.redrawInternal(\"border content rect \" + newContentRect);\n          timer.current = void 0;\n        }, 50);\n      } else {\n        layout.redrawInternal(\"border content rect \" + newContentRect);\n      }\n    }\n  });\n  const { selfRef, userControlledPositionRef, onScroll, onScrollPointerDown, hiddenTabs, onMouseWheel, isDockStickyButtons, isShowHiddenTabs } = useTabOverflow(\n    layout,\n    node,\n    Orientation.HORZ,\n    tabStripInnerRef,\n    miniScrollRef,\n    layout.getClassName(CLASSES.FLEXLAYOUT__TAB_BUTTON)\n  );\n  const onOverflowClick = (event) => {\n    const callback = layout.getShowOverflowMenu();\n    const items = hiddenTabs.map((h) => {\n      return { index: h, node: node.getChildren()[h] };\n    });\n    if (callback !== void 0) {\n      callback(node, event, items, onOverflowItemSelect);\n    } else {\n      const element = overflowbuttonRef.current;\n      showPopup(\n        element,\n        node,\n        items,\n        onOverflowItemSelect,\n        layout\n      );\n    }\n    event.stopPropagation();\n  };\n  const onOverflowItemSelect = (item) => {\n    layout.doAction(Actions.selectTab(item.node.getId()));\n    userControlledPositionRef.current = false;\n  };\n  const onDragStart = (event) => {\n    if (!layout.getEditingTab()) {\n      if (node.isEnableDrag()) {\n        event.stopPropagation();\n        layout.setDragNode(event.nativeEvent, node);\n      } else {\n        event.preventDefault();\n      }\n    } else {\n      event.preventDefault();\n    }\n  };\n  const onPointerDown = (event) => {\n    if (!isAuxMouseEvent(event)) {\n      layout.doAction(Actions.setActiveTabset(node.getId(), layout.getWindowId()));\n    }\n  };\n  const onAuxMouseClick = (event) => {\n    if (isAuxMouseEvent(event)) {\n      layout.auxMouseClick(node, event);\n    }\n  };\n  const onContextMenu = (event) => {\n    layout.showContextMenu(node, event);\n  };\n  const onInterceptPointerDown = (event) => {\n    event.stopPropagation();\n  };\n  const onMaximizeToggle = (event) => {\n    if (node.canMaximize()) {\n      layout.maximize(node);\n    }\n    event.stopPropagation();\n  };\n  const onClose = (event) => {\n    layout.doAction(Actions.deleteTabset(node.getId()));\n    event.stopPropagation();\n  };\n  const onCloseTab = (event) => {\n    layout.doAction(Actions.deleteTab(node.getChildren()[0].getId()));\n    event.stopPropagation();\n  };\n  const onPopoutTab = (event) => {\n    if (selectedTabNode !== void 0) {\n      layout.doAction(Actions.popoutTab(selectedTabNode.getId()));\n    }\n    event.stopPropagation();\n  };\n  const onDoubleClick = (event) => {\n    if (node.canMaximize()) {\n      layout.maximize(node);\n    }\n  };\n  const cm = layout.getClassName;\n  const selectedTabNode = node.getSelectedNode();\n  const path = node.getPath();\n  const tabs = [];\n  if (node.isEnableTabStrip()) {\n    for (let i = 0; i < node.getChildren().length; i++) {\n      const child = node.getChildren()[i];\n      const isSelected = node.getSelected() === i;\n      tabs.push(\n        /* @__PURE__ */ jsx(\n          TabButton,\n          {\n            layout,\n            node: child,\n            path: path + \"/tb\" + i,\n            selected: isSelected\n          },\n          child.getId()\n        )\n      );\n      if (i < node.getChildren().length - 1) {\n        tabs.push(\n          /* @__PURE__ */ jsx(\"div\", { className: cm(CLASSES.FLEXLAYOUT__TABSET_TAB_DIVIDER) }, \"divider\" + i)\n        );\n      }\n    }\n  }\n  let leading = void 0;\n  let stickyButtons = [];\n  let buttons = [];\n  const renderState = { leading, stickyButtons, buttons, overflowPosition: void 0 };\n  layout.customizeTabSet(node, renderState);\n  leading = renderState.leading;\n  stickyButtons = renderState.stickyButtons;\n  buttons = renderState.buttons;\n  const isTabStretch = node.isEnableSingleTabStretch() && node.getChildren().length === 1;\n  const showClose = isTabStretch && node.getChildren()[0].isEnableClose() || node.isEnableClose();\n  if (renderState.overflowPosition === void 0) {\n    renderState.overflowPosition = stickyButtons.length;\n  }\n  if (stickyButtons.length > 0) {\n    if (!node.isEnableTabWrap() && (isDockStickyButtons || isTabStretch)) {\n      buttons = [...stickyButtons, ...buttons];\n    } else {\n      tabs.push(/* @__PURE__ */ jsx(\n        \"div\",\n        {\n          ref: stickyButtonsRef,\n          onPointerDown: onInterceptPointerDown,\n          onDragStart: (e) => {\n            e.preventDefault();\n          },\n          className: cm(CLASSES.FLEXLAYOUT__TAB_TOOLBAR_STICKY_BUTTONS_CONTAINER),\n          children: stickyButtons\n        },\n        \"sticky_buttons_container\"\n      ));\n    }\n  }\n  if (!node.isEnableTabWrap()) {\n    if (isShowHiddenTabs) {\n      const overflowTitle = layout.i18nName(I18nLabel.Overflow_Menu_Tooltip);\n      let overflowContent;\n      if (typeof icons.more === \"function\") {\n        const items = hiddenTabs.map((h) => {\n          return { index: h, node: node.getChildren()[h] };\n        });\n        overflowContent = icons.more(node, items);\n      } else {\n        overflowContent = /* @__PURE__ */ jsxs(Fragment, { children: [\n          icons.more,\n          /* @__PURE__ */ jsx(\"div\", { className: cm(CLASSES.FLEXLAYOUT__TAB_BUTTON_OVERFLOW_COUNT), children: hiddenTabs.length > 0 ? hiddenTabs.length : \"\" })\n        ] });\n      }\n      buttons.splice(\n        Math.min(renderState.overflowPosition, buttons.length),\n        0,\n        /* @__PURE__ */ jsx(\n          \"button\",\n          {\n            \"data-layout-path\": path + \"/button/overflow\",\n            ref: overflowbuttonRef,\n            className: cm(CLASSES.FLEXLAYOUT__TAB_TOOLBAR_BUTTON) + \" \" + cm(CLASSES.FLEXLAYOUT__TAB_BUTTON_OVERFLOW),\n            title: overflowTitle,\n            onClick: onOverflowClick,\n            onPointerDown: onInterceptPointerDown,\n            children: overflowContent\n          },\n          \"overflowbutton\"\n        )\n      );\n    }\n  }\n  if (selectedTabNode !== void 0 && layout.isSupportsPopout() && selectedTabNode.isEnablePopout() && selectedTabNode.isEnablePopoutIcon()) {\n    const popoutTitle = layout.i18nName(I18nLabel.Popout_Tab);\n    buttons.push(\n      /* @__PURE__ */ jsx(\n        \"button\",\n        {\n          \"data-layout-path\": path + \"/button/popout\",\n          title: popoutTitle,\n          className: cm(CLASSES.FLEXLAYOUT__TAB_TOOLBAR_BUTTON) + \" \" + cm(CLASSES.FLEXLAYOUT__TAB_TOOLBAR_BUTTON_FLOAT),\n          onClick: onPopoutTab,\n          onPointerDown: onInterceptPointerDown,\n          children: typeof icons.popout === \"function\" ? icons.popout(selectedTabNode) : icons.popout\n        },\n        \"popout\"\n      )\n    );\n  }\n  if (node.canMaximize()) {\n    const minTitle = layout.i18nName(I18nLabel.Restore);\n    const maxTitle = layout.i18nName(I18nLabel.Maximize);\n    buttons.push(\n      /* @__PURE__ */ jsx(\n        \"button\",\n        {\n          \"data-layout-path\": path + \"/button/max\",\n          title: node.isMaximized() ? minTitle : maxTitle,\n          className: cm(CLASSES.FLEXLAYOUT__TAB_TOOLBAR_BUTTON) + \" \" + cm(CLASSES.FLEXLAYOUT__TAB_TOOLBAR_BUTTON_ + (node.isMaximized() ? \"max\" : \"min\")),\n          onClick: onMaximizeToggle,\n          onPointerDown: onInterceptPointerDown,\n          children: node.isMaximized() ? typeof icons.restore === \"function\" ? icons.restore(node) : icons.restore : typeof icons.maximize === \"function\" ? icons.maximize(node) : icons.maximize\n        },\n        \"max\"\n      )\n    );\n  }\n  if (!node.isMaximized() && showClose) {\n    const title = isTabStretch ? layout.i18nName(I18nLabel.Close_Tab) : layout.i18nName(I18nLabel.Close_Tabset);\n    buttons.push(\n      /* @__PURE__ */ jsx(\n        \"button\",\n        {\n          \"data-layout-path\": path + \"/button/close\",\n          title,\n          className: cm(CLASSES.FLEXLAYOUT__TAB_TOOLBAR_BUTTON) + \" \" + cm(CLASSES.FLEXLAYOUT__TAB_TOOLBAR_BUTTON_CLOSE),\n          onClick: isTabStretch ? onCloseTab : onClose,\n          onPointerDown: onInterceptPointerDown,\n          children: typeof icons.closeTabset === \"function\" ? icons.closeTabset(node) : icons.closeTabset\n        },\n        \"close\"\n      )\n    );\n  }\n  if (node.isActive() && node.isEnableActiveIcon()) {\n    const title = layout.i18nName(I18nLabel.Active_Tabset);\n    buttons.push(\n      /* @__PURE__ */ jsx(\n        \"div\",\n        {\n          \"data-layout-path\": path + \"/button/active\",\n          title,\n          className: cm(CLASSES.FLEXLAYOUT__TAB_TOOLBAR_ICON),\n          children: typeof icons.activeTabset === \"function\" ? icons.activeTabset(node) : icons.activeTabset\n        },\n        \"active\"\n      )\n    );\n  }\n  const buttonbar = /* @__PURE__ */ jsx(\n    \"div\",\n    {\n      ref: buttonBarRef,\n      className: cm(CLASSES.FLEXLAYOUT__TAB_TOOLBAR),\n      onPointerDown: onInterceptPointerDown,\n      onDragStart: (e) => {\n        e.preventDefault();\n      },\n      children: buttons\n    },\n    \"buttonbar\"\n  );\n  let tabStrip;\n  let tabStripClasses = cm(CLASSES.FLEXLAYOUT__TABSET_TABBAR_OUTER);\n  if (node.getClassNameTabStrip() !== void 0) {\n    tabStripClasses += \" \" + node.getClassNameTabStrip();\n  }\n  tabStripClasses += \" \" + CLASSES.FLEXLAYOUT__TABSET_TABBAR_OUTER_ + node.getTabLocation();\n  if (node.isActive()) {\n    tabStripClasses += \" \" + cm(CLASSES.FLEXLAYOUT__TABSET_SELECTED);\n  }\n  if (node.isMaximized()) {\n    tabStripClasses += \" \" + cm(CLASSES.FLEXLAYOUT__TABSET_MAXIMIZED);\n  }\n  if (isTabStretch) {\n    const tabNode = node.getChildren()[0];\n    if (tabNode.getTabSetClassName() !== void 0) {\n      tabStripClasses += \" \" + tabNode.getTabSetClassName();\n    }\n  }\n  let leadingContainer = void 0;\n  if (leading) {\n    leadingContainer = /* @__PURE__ */ jsx(\"div\", { className: cm(CLASSES.FLEXLAYOUT__TABSET_LEADING), children: leading });\n  }\n  if (node.isEnableTabWrap()) {\n    if (node.isEnableTabStrip()) {\n      tabStrip = /* @__PURE__ */ jsxs(\n        \"div\",\n        {\n          className: tabStripClasses,\n          style: { flexWrap: \"wrap\", gap: \"1px\", marginTop: \"2px\" },\n          ref: tabStripRef,\n          \"data-layout-path\": path + \"/tabstrip\",\n          onPointerDown,\n          onDoubleClick,\n          onContextMenu,\n          onClick: onAuxMouseClick,\n          onAuxClick: onAuxMouseClick,\n          draggable: true,\n          onDragStart,\n          children: [\n            leadingContainer,\n            tabs,\n            /* @__PURE__ */ jsx(\"div\", { style: { flexGrow: 1 } }),\n            buttonbar\n          ]\n        }\n      );\n    }\n  } else {\n    if (node.isEnableTabStrip()) {\n      let miniScrollbar = void 0;\n      if (node.isEnableTabScrollbar()) {\n        miniScrollbar = /* @__PURE__ */ jsx(\n          \"div\",\n          {\n            ref: miniScrollRef,\n            className: cm(CLASSES.FLEXLAYOUT__MINI_SCROLLBAR),\n            onPointerDown: onScrollPointerDown\n          }\n        );\n      }\n      tabStrip = /* @__PURE__ */ jsxs(\n        \"div\",\n        {\n          className: tabStripClasses,\n          ref: tabStripRef,\n          \"data-layout-path\": path + \"/tabstrip\",\n          onPointerDown,\n          onDoubleClick,\n          onContextMenu,\n          onClick: onAuxMouseClick,\n          onAuxClick: onAuxMouseClick,\n          draggable: true,\n          onWheel: onMouseWheel,\n          onDragStart,\n          children: [\n            leadingContainer,\n            /* @__PURE__ */ jsxs(\"div\", { className: cm(CLASSES.FLEXLAYOUT__MINI_SCROLLBAR_CONTAINER), children: [\n              /* @__PURE__ */ jsx(\n                \"div\",\n                {\n                  ref: tabStripInnerRef,\n                  className: cm(CLASSES.FLEXLAYOUT__TABSET_TABBAR_INNER) + \" \" + cm(CLASSES.FLEXLAYOUT__TABSET_TABBAR_INNER_ + node.getTabLocation()),\n                  style: { overflowX: \"auto\", overflowY: \"hidden\" },\n                  onScroll,\n                  children: /* @__PURE__ */ jsx(\n                    \"div\",\n                    {\n                      style: { width: isTabStretch ? \"100%\" : \"none\" },\n                      className: cm(CLASSES.FLEXLAYOUT__TABSET_TABBAR_INNER_TAB_CONTAINER) + \" \" + cm(CLASSES.FLEXLAYOUT__TABSET_TABBAR_INNER_TAB_CONTAINER_ + node.getTabLocation()),\n                      children: tabs\n                    }\n                  )\n                }\n              ),\n              miniScrollbar\n            ] }),\n            buttonbar\n          ]\n        }\n      );\n    }\n  }\n  let emptyTabset;\n  if (node.getChildren().length === 0) {\n    const placeHolderCallback = layout.getTabSetPlaceHolderCallback();\n    if (placeHolderCallback) {\n      emptyTabset = placeHolderCallback(node);\n    }\n  }\n  let content = /* @__PURE__ */ jsx(\"div\", { ref: contentRef, className: cm(CLASSES.FLEXLAYOUT__TABSET_CONTENT), children: emptyTabset });\n  if (node.getTabLocation() === \"top\") {\n    content = /* @__PURE__ */ jsxs(Fragment, { children: [\n      tabStrip,\n      content\n    ] });\n  } else {\n    content = /* @__PURE__ */ jsxs(Fragment, { children: [\n      content,\n      tabStrip\n    ] });\n  }\n  const style2 = {\n    flexGrow: Math.max(1, node.getWeight() * 1e3),\n    minWidth: node.getMinWidth(),\n    minHeight: node.getMinHeight(),\n    maxWidth: node.getMaxWidth(),\n    maxHeight: node.getMaxHeight()\n  };\n  if (node.getModel().getMaximizedTabset(layout.getWindowId()) !== void 0 && !node.isMaximized()) {\n    style2.display = \"none\";\n  }\n  const tabset = /* @__PURE__ */ jsx(\n    \"div\",\n    {\n      ref: selfRef,\n      className: cm(CLASSES.FLEXLAYOUT__TABSET_CONTAINER),\n      style: style2,\n      children: /* @__PURE__ */ jsx(\n        \"div\",\n        {\n          className: cm(CLASSES.FLEXLAYOUT__TABSET),\n          \"data-layout-path\": path,\n          children: content\n        }\n      )\n    }\n  );\n  if (node.isMaximized()) {\n    if (layout.getMainElement()) {\n      return createPortal(\n        /* @__PURE__ */ jsx(\"div\", { style: {\n          position: \"absolute\",\n          display: \"flex\",\n          top: 0,\n          left: 0,\n          bottom: 0,\n          right: 0\n        }, children: tabset }),\n        layout.getMainElement()\n      );\n    } else {\n      return tabset;\n    }\n  } else {\n    return tabset;\n  }\n};\nconst Row = (props) => {\n  const { layout, node } = props;\n  const selfRef = React.useRef(null);\n  const horizontal = node.getOrientation() === Orientation.HORZ;\n  React.useLayoutEffect(() => {\n    node.setRect(layout.getBoundingClientRect(selfRef.current));\n  });\n  const items = [];\n  let i = 0;\n  for (const child of node.getChildren()) {\n    if (i > 0) {\n      items.push(/* @__PURE__ */ jsx(Splitter, { layout, node, index: i, horizontal }, \"splitter\" + i));\n    }\n    if (child instanceof RowNode) {\n      items.push(/* @__PURE__ */ jsx(Row, { layout, node: child }, child.getId()));\n    } else if (child instanceof TabSetNode) {\n      items.push(/* @__PURE__ */ jsx(TabSet, { layout, node: child }, child.getId()));\n    }\n    i++;\n  }\n  const style2 = {\n    flexGrow: Math.max(1, node.getWeight() * 1e3),\n    // NOTE:  flex-grow cannot have values < 1 otherwise will not fill parent, need to normalize \n    minWidth: node.getMinWidth(),\n    minHeight: node.getMinHeight(),\n    maxWidth: node.getMaxWidth(),\n    maxHeight: node.getMaxHeight()\n  };\n  if (horizontal) {\n    style2.flexDirection = \"row\";\n  } else {\n    style2.flexDirection = \"column\";\n  }\n  return /* @__PURE__ */ jsx(\n    \"div\",\n    {\n      ref: selfRef,\n      className: layout.getClassName(CLASSES.FLEXLAYOUT__ROW),\n      style: style2,\n      children: items\n    }\n  );\n};\nconst Tab = (props) => {\n  const { layout, selected, node, path } = props;\n  const selfRef = React.useRef(null);\n  const firstSelect = React.useRef(true);\n  const parentNode = node.getParent();\n  const rect = parentNode.getContentRect();\n  React.useLayoutEffect(() => {\n    const element = node.getMoveableElement();\n    selfRef.current.appendChild(element);\n    node.setMoveableElement(element);\n    const handleScroll = () => {\n      node.saveScrollPosition();\n    };\n    element.addEventListener(\"scroll\", handleScroll);\n    selfRef.current.addEventListener(\"pointerdown\", onPointerDown);\n    return () => {\n      element.removeEventListener(\"scroll\", handleScroll);\n      if (selfRef.current) {\n        selfRef.current.removeEventListener(\"pointerdown\", onPointerDown);\n      }\n      node.setVisible(false);\n    };\n  }, []);\n  React.useEffect(() => {\n    if (node.isSelected()) {\n      if (firstSelect.current) {\n        node.restoreScrollPosition();\n        firstSelect.current = false;\n      }\n    }\n  });\n  const onPointerDown = () => {\n    const parent = node.getParent();\n    if (parent instanceof TabSetNode) {\n      if (!parent.isActive()) {\n        layout.doAction(Actions.setActiveTabset(parent.getId(), layout.getWindowId()));\n      }\n    }\n  };\n  node.setRect(rect);\n  const cm = layout.getClassName;\n  const style2 = {};\n  rect.styleWithPosition(style2);\n  let overlay = null;\n  if (selected) {\n    node.setVisible(true);\n    if (document.hidden && node.isEnablePopoutOverlay()) {\n      const overlayStyle = {};\n      rect.styleWithPosition(overlayStyle);\n      overlay = /* @__PURE__ */ jsx(\"div\", { style: overlayStyle, className: cm(CLASSES.FLEXLAYOUT__TAB_OVERLAY) });\n    }\n  } else {\n    style2.display = \"none\";\n    node.setVisible(false);\n  }\n  if (parentNode instanceof TabSetNode) {\n    if (node.getModel().getMaximizedTabset(layout.getWindowId()) !== void 0) {\n      if (parentNode.isMaximized()) {\n        style2.zIndex = 10;\n      } else {\n        style2.display = \"none\";\n      }\n    }\n  }\n  if (parentNode instanceof BorderNode) {\n    if (!parentNode.isShowing()) {\n      style2.display = \"none\";\n    }\n  }\n  let className = cm(CLASSES.FLEXLAYOUT__TAB);\n  if (parentNode instanceof BorderNode) {\n    className += \" \" + cm(CLASSES.FLEXLAYOUT__TAB_BORDER);\n    className += \" \" + cm(CLASSES.FLEXLAYOUT__TAB_BORDER_ + parentNode.getLocation().getName());\n  }\n  if (node.getContentClassName() !== void 0) {\n    className += \" \" + node.getContentClassName();\n  }\n  return /* @__PURE__ */ jsxs(Fragment, { children: [\n    overlay,\n    /* @__PURE__ */ jsx(\n      \"div\",\n      {\n        ref: selfRef,\n        style: style2,\n        className,\n        \"data-layout-path\": path\n      }\n    )\n  ] });\n};\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    __publicField(this, \"retry\", () => {\n      this.setState({ hasError: false });\n    });\n    this.state = { hasError: false };\n  }\n  static getDerivedStateFromError(error) {\n    return { hasError: true };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.debug(error);\n    console.debug(errorInfo);\n  }\n  render() {\n    if (this.state.hasError) {\n      return /* @__PURE__ */ jsx(\"div\", { className: CLASSES.FLEXLAYOUT__ERROR_BOUNDARY_CONTAINER, children: /* @__PURE__ */ jsx(\"div\", { className: CLASSES.FLEXLAYOUT__ERROR_BOUNDARY_CONTENT, children: /* @__PURE__ */ jsxs(\"div\", { style: { display: \"flex\", flexDirection: \"column\", alignItems: \"center\" }, children: [\n        this.props.message,\n        /* @__PURE__ */ jsx(\"p\", { children: /* @__PURE__ */ jsx(\"button\", { onClick: this.retry, children: this.props.retryText }) })\n      ] }) }) });\n    }\n    return this.props.children;\n  }\n}\nconst SizeTracker = React.memo(({ layout, node }) => {\n  return /* @__PURE__ */ jsx(\n    ErrorBoundary,\n    {\n      message: layout.i18nName(I18nLabel.Error_rendering_component),\n      retryText: layout.i18nName(I18nLabel.Error_rendering_component_retry),\n      children: layout.props.factory(node)\n    }\n  );\n}, arePropsEqual);\nfunction arePropsEqual(prevProps, nextProps) {\n  const reRender = nextProps.visible && (!prevProps.rect.equalSize(nextProps.rect) || prevProps.forceRevision !== nextProps.forceRevision || prevProps.tabsRevision !== nextProps.tabsRevision);\n  return !reRender;\n}\nclass Layout extends React.Component {\n  // so LayoutInternal knows this is a parent render (used for optimization)\n  /** @internal */\n  constructor(props) {\n    super(props);\n    /** @internal */\n    __publicField(this, \"selfRef\");\n    /** @internal */\n    __publicField(this, \"revision\");\n    this.selfRef = React.createRef();\n    this.revision = 0;\n  }\n  /** re-render the layout */\n  redraw() {\n    this.selfRef.current.redraw(\"parent \" + this.revision);\n  }\n  /**\n   * Adds a new tab to the given tabset\n   * @param tabsetId the id of the tabset where the new tab will be added\n   * @param json the json for the new tab node\n   * @returns the added tab node or undefined\n   */\n  addTabToTabSet(tabsetId, json) {\n    return this.selfRef.current.addTabToTabSet(tabsetId, json);\n  }\n  /**\n   * Adds a new tab by dragging an item to the drop location, must be called from within an HTML\n   * drag start handler. You can use the setDragComponent() method to set the drag image before calling this \n   * method.\n   * @param event the drag start event\n   * @param json the json for the new tab node\n   * @param onDrop a callback to call when the drag is complete\n   */\n  addTabWithDragAndDrop(event, json, onDrop) {\n    this.selfRef.current.addTabWithDragAndDrop(event, json, onDrop);\n  }\n  /**\n   * Move a tab/tabset using drag and drop, must be called from within an HTML\n   * drag start handler\n   * @param event the drag start event\n   * @param node the tab or tabset to drag\n   */\n  moveTabWithDragAndDrop(event, node) {\n    this.selfRef.current.moveTabWithDragAndDrop(event, node);\n  }\n  /**\n   * Adds a new tab to the active tabset (if there is one)\n   * @param json the json for the new tab node\n   * @returns the added tab node or undefined\n   */\n  addTabToActiveTabSet(json) {\n    return this.selfRef.current.addTabToActiveTabSet(json);\n  }\n  /**\n   * Sets the drag image from a react component for a drag event\n   * @param event the drag event\n   * @param component the react component to be used for the drag image\n   * @param x the x position of the drag cursor on the image\n   * @param y the x position of the drag cursor on the image\n   */\n  setDragComponent(event, component, x, y) {\n    this.selfRef.current.setDragComponent(event, component, x, y);\n  }\n  /** Get the root div element of the layout */\n  getRootDiv() {\n    return this.selfRef.current.getRootDiv();\n  }\n  /** @internal */\n  render() {\n    return /* @__PURE__ */ jsx(LayoutInternal, { ref: this.selfRef, ...this.props, renderRevision: this.revision++ });\n  }\n}\nconst _LayoutInternal = class _LayoutInternal extends React.Component {\n  // private renderCount: any;\n  constructor(props) {\n    super(props);\n    __publicField(this, \"selfRef\");\n    __publicField(this, \"moveablesRef\");\n    __publicField(this, \"findBorderBarSizeRef\");\n    __publicField(this, \"mainRef\");\n    __publicField(this, \"previousModel\");\n    __publicField(this, \"orderedTabIds\");\n    __publicField(this, \"orderedTabMoveableIds\");\n    __publicField(this, \"moveableElementMap\", /* @__PURE__ */ new Map());\n    __publicField(this, \"dropInfo\");\n    __publicField(this, \"outlineDiv\");\n    __publicField(this, \"currentDocument\");\n    __publicField(this, \"currentWindow\");\n    __publicField(this, \"supportsPopout\");\n    __publicField(this, \"popoutURL\");\n    __publicField(this, \"icons\");\n    __publicField(this, \"resizeObserver\");\n    __publicField(this, \"dragEnterCount\", 0);\n    __publicField(this, \"dragging\", false);\n    __publicField(this, \"windowId\");\n    __publicField(this, \"layoutWindow\");\n    __publicField(this, \"mainLayout\");\n    __publicField(this, \"isMainWindow\");\n    __publicField(this, \"isDraggingOverWindow\");\n    __publicField(this, \"styleObserver\");\n    __publicField(this, \"popoutWindowName\");\n    __publicField(this, \"updateLayoutMetrics\", () => {\n      if (this.findBorderBarSizeRef.current) {\n        const borderBarSize = this.findBorderBarSizeRef.current.getBoundingClientRect().height;\n        if (borderBarSize !== this.state.calculatedBorderBarSize) {\n          this.setState({ calculatedBorderBarSize: borderBarSize });\n        }\n      }\n    });\n    __publicField(this, \"onModelChange\", (action) => {\n      this.redrawInternal(\"model change\");\n      if (this.props.onModelChange) {\n        this.props.onModelChange(this.props.model, action);\n      }\n    });\n    __publicField(this, \"updateRect\", () => {\n      if (this.selfRef.current) {\n        const rect = Rect.fromDomRect(this.selfRef.current.getBoundingClientRect());\n        if (!rect.equals(this.state.rect) && rect.width !== 0 && rect.height !== 0) {\n          this.setState({ rect });\n          if (this.windowId !== Model.MAIN_WINDOW_ID) {\n            this.redrawInternal(\"rect updated\");\n          }\n        }\n      }\n    });\n    __publicField(this, \"getClassName\", (defaultClassName) => {\n      if (this.props.classNameMapper === void 0) {\n        return defaultClassName;\n      } else {\n        return this.props.classNameMapper(defaultClassName);\n      }\n    });\n    __publicField(this, \"onCloseWindow\", (windowLayout) => {\n      this.doAction(Actions.closeWindow(windowLayout.windowId));\n    });\n    __publicField(this, \"onSetWindow\", (windowLayout, window2) => {\n    });\n    __publicField(this, \"showControlInPortal\", (control, element) => {\n      const portal = createPortal(control, element);\n      this.setState({ portal });\n    });\n    __publicField(this, \"hideControlInPortal\", () => {\n      this.setState({ portal: void 0 });\n    });\n    __publicField(this, \"getIcons\", () => {\n      return this.icons;\n    });\n    __publicField(this, \"setDragNode\", (event, node) => {\n      _LayoutInternal.dragState = new DragState(this.mainLayout, \"internal\", node, void 0, void 0);\n      event.dataTransfer.setData(\"text/plain\", \"--flexlayout--\");\n      event.dataTransfer.effectAllowed = \"copyMove\";\n      event.dataTransfer.dropEffect = \"move\";\n      this.dragEnterCount = 0;\n      if (node instanceof TabSetNode) {\n        let rendered = false;\n        let content = this.i18nName(I18nLabel.Move_Tabset);\n        if (node.getChildren().length > 0) {\n          content = this.i18nName(I18nLabel.Move_Tabs).replace(\"?\", String(node.getChildren().length));\n        }\n        if (this.props.onRenderDragRect) {\n          const dragComponent = this.props.onRenderDragRect(content, node, void 0);\n          if (dragComponent) {\n            this.setDragComponent(event, dragComponent, 10, 10);\n            rendered = true;\n          }\n        }\n        if (!rendered) {\n          this.setDragComponent(event, content, 10, 10);\n        }\n      } else {\n        const element = event.target;\n        const rect = element.getBoundingClientRect();\n        const offsetX = event.clientX - rect.left;\n        const offsetY = event.clientY - rect.top;\n        const parentNode = node == null ? void 0 : node.getParent();\n        const isInVerticalBorder = parentNode instanceof BorderNode && parentNode.getOrientation() === Orientation.HORZ;\n        const x = isInVerticalBorder ? 10 : offsetX;\n        const y = isInVerticalBorder ? 10 : offsetY;\n        let rendered = false;\n        if (this.props.onRenderDragRect) {\n          const content = /* @__PURE__ */ jsx(TabButtonStamp, { layout: this, node }, node.getId());\n          const dragComponent = this.props.onRenderDragRect(content, node, void 0);\n          if (dragComponent) {\n            this.setDragComponent(event, dragComponent, x, y);\n            rendered = true;\n          }\n        }\n        if (!rendered) {\n          if (isSafari()) {\n            this.setDragComponent(event, /* @__PURE__ */ jsx(TabButtonStamp, { node, layout: this }), x, y);\n          } else {\n            event.dataTransfer.setDragImage(node.getTabStamp(), x, y);\n          }\n        }\n      }\n    });\n    __publicField(this, \"onDragEnterRaw\", (event) => {\n      this.dragEnterCount++;\n      if (this.dragEnterCount === 1) {\n        this.onDragEnter(event);\n      }\n    });\n    __publicField(this, \"onDragLeaveRaw\", (event) => {\n      this.dragEnterCount--;\n      if (this.dragEnterCount === 0) {\n        this.onDragLeave(event);\n      }\n    });\n    __publicField(this, \"onDragEnter\", (event) => {\n      if (!_LayoutInternal.dragState && this.props.onExternalDrag) {\n        const externalDrag = this.props.onExternalDrag(event);\n        if (externalDrag) {\n          const tempNode = TabNode.fromJson(externalDrag.json, this.props.model, false);\n          _LayoutInternal.dragState = new DragState(this.mainLayout, \"external\", tempNode, externalDrag.json, externalDrag.onDrop);\n        }\n      }\n      if (_LayoutInternal.dragState) {\n        if (this.windowId !== Model.MAIN_WINDOW_ID && _LayoutInternal.dragState.mainLayout === this.mainLayout) {\n          _LayoutInternal.dragState.mainLayout.setDraggingOverWindow(true);\n        }\n        if (_LayoutInternal.dragState.mainLayout !== this.mainLayout) {\n          return;\n        }\n        event.preventDefault();\n        this.dropInfo = void 0;\n        const rootdiv = this.selfRef.current;\n        this.outlineDiv = this.currentDocument.createElement(\"div\");\n        this.outlineDiv.className = this.getClassName(CLASSES.FLEXLAYOUT__OUTLINE_RECT);\n        this.outlineDiv.style.visibility = \"hidden\";\n        const speed = this.props.model.getAttribute(\"tabDragSpeed\");\n        this.outlineDiv.style.transition = `top ${speed}s, left ${speed}s, width ${speed}s, height ${speed}s`;\n        rootdiv.appendChild(this.outlineDiv);\n        this.dragging = true;\n        this.showOverlay(true);\n        if (!this.isDraggingOverWindow && this.props.model.getMaximizedTabset(this.windowId) === void 0) {\n          this.setState({ showEdges: this.props.model.isEnableEdgeDock() });\n        }\n        const clientRect = this.selfRef.current.getBoundingClientRect();\n        const r = new Rect(\n          event.clientX - clientRect.left,\n          event.clientY - clientRect.top,\n          1,\n          1\n        );\n        r.positionElement(this.outlineDiv);\n      }\n    });\n    __publicField(this, \"onDragOver\", (event) => {\n      var _a;\n      if (this.dragging && !this.isDraggingOverWindow) {\n        event.preventDefault();\n        const clientRect = (_a = this.selfRef.current) == null ? void 0 : _a.getBoundingClientRect();\n        const pos = {\n          x: event.clientX - ((clientRect == null ? void 0 : clientRect.left) ?? 0),\n          y: event.clientY - ((clientRect == null ? void 0 : clientRect.top) ?? 0)\n        };\n        this.checkForBorderToShow(pos.x, pos.y);\n        const dropInfo = this.props.model.findDropTargetNode(this.windowId, _LayoutInternal.dragState.dragNode, pos.x, pos.y);\n        if (dropInfo) {\n          this.dropInfo = dropInfo;\n          if (this.outlineDiv) {\n            this.outlineDiv.className = this.getClassName(dropInfo.className);\n            dropInfo.rect.positionElement(this.outlineDiv);\n            this.outlineDiv.style.visibility = \"visible\";\n          }\n        }\n      }\n    });\n    __publicField(this, \"onDragLeave\", (event) => {\n      if (this.dragging) {\n        if (this.windowId !== Model.MAIN_WINDOW_ID) {\n          _LayoutInternal.dragState.mainLayout.setDraggingOverWindow(false);\n        }\n        this.clearDragLocal();\n      }\n    });\n    __publicField(this, \"onDrop\", (event) => {\n      if (this.dragging) {\n        event.preventDefault();\n        const dragState = _LayoutInternal.dragState;\n        if (this.dropInfo) {\n          if (dragState.dragJson !== void 0) {\n            const newNode = this.doAction(Actions.addNode(dragState.dragJson, this.dropInfo.node.getId(), this.dropInfo.location, this.dropInfo.index));\n            if (dragState.fnNewNodeDropped !== void 0) {\n              dragState.fnNewNodeDropped(newNode, event);\n            }\n          } else if (dragState.dragNode !== void 0) {\n            this.doAction(Actions.moveNode(dragState.dragNode.getId(), this.dropInfo.node.getId(), this.dropInfo.location, this.dropInfo.index));\n          }\n        }\n        this.mainLayout.clearDragMain();\n      }\n      this.dragEnterCount = 0;\n    });\n    this.orderedTabIds = [];\n    this.orderedTabMoveableIds = [];\n    this.selfRef = React.createRef();\n    this.moveablesRef = React.createRef();\n    this.mainRef = React.createRef();\n    this.findBorderBarSizeRef = React.createRef();\n    this.supportsPopout = props.supportsPopout !== void 0 ? props.supportsPopout : defaultSupportsPopout;\n    this.popoutURL = props.popoutURL ? props.popoutURL : \"popout.html\";\n    this.icons = { ...defaultIcons, ...props.icons };\n    this.windowId = props.windowId ? props.windowId : Model.MAIN_WINDOW_ID;\n    this.mainLayout = this.props.mainLayout ? this.props.mainLayout : this;\n    this.isDraggingOverWindow = false;\n    this.layoutWindow = this.props.model.getwindowsMap().get(this.windowId);\n    this.layoutWindow.layout = this;\n    this.popoutWindowName = this.props.popoutWindowName || \"Popout Window\";\n    this.state = {\n      rect: Rect.empty(),\n      editingTab: void 0,\n      showEdges: false,\n      showOverlay: false,\n      calculatedBorderBarSize: 29,\n      layoutRevision: 0,\n      forceRevision: 0,\n      showHiddenBorder: DockLocation.CENTER\n    };\n    this.isMainWindow = this.windowId === Model.MAIN_WINDOW_ID;\n  }\n  componentDidMount() {\n    this.updateRect();\n    this.currentDocument = this.selfRef.current.ownerDocument;\n    this.currentWindow = this.currentDocument.defaultView;\n    this.layoutWindow.window = this.currentWindow;\n    this.layoutWindow.toScreenRectFunction = (r) => this.getScreenRect(r);\n    this.resizeObserver = new ResizeObserver((entries) => {\n      requestAnimationFrame(() => {\n        this.updateRect();\n      });\n    });\n    if (this.selfRef.current) {\n      this.resizeObserver.observe(this.selfRef.current);\n    }\n    if (this.isMainWindow) {\n      this.props.model.addChangeListener(this.onModelChange);\n      this.updateLayoutMetrics();\n    } else {\n      this.currentWindow.addEventListener(\"resize\", () => {\n        this.updateRect();\n      });\n      const sourceElement = this.props.mainLayout.getRootDiv();\n      const targetElement = this.selfRef.current;\n      copyInlineStyles(sourceElement, targetElement);\n      this.styleObserver = new MutationObserver(() => {\n        const changed = copyInlineStyles(sourceElement, targetElement);\n        if (changed) {\n          this.redraw(\"mutation observer\");\n        }\n      });\n      this.styleObserver.observe(sourceElement, { attributeFilter: [\"style\"] });\n    }\n    document.addEventListener(\"visibilitychange\", () => {\n      for (const [_, layoutWindow] of this.props.model.getwindowsMap()) {\n        const layout = layoutWindow.layout;\n        if (layout) {\n          this.redraw(\"visibility change\");\n        }\n      }\n    });\n  }\n  componentDidUpdate() {\n    this.currentDocument = this.selfRef.current.ownerDocument;\n    this.currentWindow = this.currentDocument.defaultView;\n    if (this.isMainWindow) {\n      if (this.props.model !== this.previousModel) {\n        if (this.previousModel !== void 0) {\n          this.previousModel.removeChangeListener(this.onModelChange);\n        }\n        this.props.model.getwindowsMap().get(this.windowId).layout = this;\n        this.props.model.addChangeListener(this.onModelChange);\n        this.layoutWindow = this.props.model.getwindowsMap().get(this.windowId);\n        this.layoutWindow.layout = this;\n        this.layoutWindow.toScreenRectFunction = (r) => this.getScreenRect(r);\n        this.previousModel = this.props.model;\n        this.tidyMoveablesMap();\n      }\n      this.updateLayoutMetrics();\n    }\n  }\n  componentWillUnmount() {\n    var _a, _b;\n    if (this.selfRef.current) {\n      (_a = this.resizeObserver) == null ? void 0 : _a.unobserve(this.selfRef.current);\n    }\n    if (this.isMainWindow) {\n      this.props.model.removeChangeListener(this.onModelChange);\n    }\n    (_b = this.styleObserver) == null ? void 0 : _b.disconnect();\n  }\n  render() {\n    if (!this.selfRef.current) {\n      return /* @__PURE__ */ jsxs(\"div\", { ref: this.selfRef, className: this.getClassName(CLASSES.FLEXLAYOUT__LAYOUT), children: [\n        /* @__PURE__ */ jsx(\"div\", { ref: this.moveablesRef, className: this.getClassName(CLASSES.FLEXLAYOUT__LAYOUT_MOVEABLES) }, \"__moveables__\"),\n        this.renderMetricsElements()\n      ] });\n    }\n    const model = this.props.model;\n    model.getRoot(this.windowId).calcMinMaxSize();\n    model.getRoot(this.windowId).setPaths(\"\");\n    model.getBorderSet().setPaths();\n    const inner = this.renderLayout();\n    const outer = this.renderBorders(inner);\n    const tabs = this.renderTabs();\n    const reorderedTabs = this.reorderComponents(tabs, this.orderedTabIds);\n    let floatingWindows = null;\n    let reorderedTabMoveables = null;\n    let tabStamps = null;\n    let metricElements = null;\n    if (this.isMainWindow) {\n      floatingWindows = this.renderWindows();\n      metricElements = this.renderMetricsElements();\n      const tabMoveables = this.renderTabMoveables();\n      reorderedTabMoveables = this.reorderComponents(tabMoveables, this.orderedTabMoveableIds);\n      tabStamps = /* @__PURE__ */ jsx(\"div\", { className: this.getClassName(CLASSES.FLEXLAYOUT__LAYOUT_TAB_STAMPS), children: this.renderTabStamps() }, \"__tabStamps__\");\n    }\n    return /* @__PURE__ */ jsxs(\n      \"div\",\n      {\n        ref: this.selfRef,\n        className: this.getClassName(CLASSES.FLEXLAYOUT__LAYOUT),\n        onDragEnter: this.onDragEnterRaw,\n        onDragLeave: this.onDragLeaveRaw,\n        onDragOver: this.onDragOver,\n        onDrop: this.onDrop,\n        children: [\n          /* @__PURE__ */ jsx(\"div\", { ref: this.moveablesRef, className: this.getClassName(CLASSES.FLEXLAYOUT__LAYOUT_MOVEABLES) }, \"__moveables__\"),\n          metricElements,\n          /* @__PURE__ */ jsx(Overlay, { layout: this, show: this.state.showOverlay }, \"__overlay__\"),\n          outer,\n          reorderedTabs,\n          reorderedTabMoveables,\n          tabStamps,\n          this.state.portal,\n          floatingWindows\n        ]\n      }\n    );\n  }\n  renderBorders(inner) {\n    const classMain = this.getClassName(CLASSES.FLEXLAYOUT__LAYOUT_MAIN);\n    const borders = this.props.model.getBorderSet().getBorderMap();\n    if (this.isMainWindow && borders.size > 0) {\n      inner = /* @__PURE__ */ jsx(\"div\", { className: classMain, ref: this.mainRef, children: inner });\n      const borderSetComponents = /* @__PURE__ */ new Map();\n      const borderSetContentComponents = /* @__PURE__ */ new Map();\n      for (const [_, location] of DockLocation.values) {\n        const border = borders.get(location);\n        const showBorder = border && border.isShowing() && (!border.isAutoHide() || border.isAutoHide() && (border.getChildren().length > 0 || this.state.showHiddenBorder === location));\n        if (showBorder) {\n          borderSetComponents.set(location, /* @__PURE__ */ jsx(BorderTabSet, { layout: this, border, size: this.state.calculatedBorderBarSize }));\n          borderSetContentComponents.set(location, /* @__PURE__ */ jsx(BorderTab, { layout: this, border, show: border.getSelected() !== -1 }));\n        }\n      }\n      const classBorderOuter = this.getClassName(CLASSES.FLEXLAYOUT__LAYOUT_BORDER_CONTAINER);\n      const classBorderInner = this.getClassName(CLASSES.FLEXLAYOUT__LAYOUT_BORDER_CONTAINER_INNER);\n      if (this.props.model.getBorderSet().getLayoutHorizontal()) {\n        const innerWithBorderTabs = /* @__PURE__ */ jsxs(\"div\", { className: classBorderInner, style: { flexDirection: \"column\" }, children: [\n          borderSetContentComponents.get(DockLocation.TOP),\n          /* @__PURE__ */ jsxs(\"div\", { className: classBorderInner, style: { flexDirection: \"row\" }, children: [\n            borderSetContentComponents.get(DockLocation.LEFT),\n            inner,\n            borderSetContentComponents.get(DockLocation.RIGHT)\n          ] }),\n          borderSetContentComponents.get(DockLocation.BOTTOM)\n        ] });\n        return /* @__PURE__ */ jsxs(\"div\", { className: classBorderOuter, style: { flexDirection: \"column\" }, children: [\n          borderSetComponents.get(DockLocation.TOP),\n          /* @__PURE__ */ jsxs(\"div\", { className: classBorderInner, style: { flexDirection: \"row\" }, children: [\n            borderSetComponents.get(DockLocation.LEFT),\n            innerWithBorderTabs,\n            borderSetComponents.get(DockLocation.RIGHT)\n          ] }),\n          borderSetComponents.get(DockLocation.BOTTOM)\n        ] });\n      } else {\n        const innerWithBorderTabs = /* @__PURE__ */ jsxs(\"div\", { className: classBorderInner, style: { flexDirection: \"row\" }, children: [\n          borderSetContentComponents.get(DockLocation.LEFT),\n          /* @__PURE__ */ jsxs(\"div\", { className: classBorderInner, style: { flexDirection: \"column\" }, children: [\n            borderSetContentComponents.get(DockLocation.TOP),\n            inner,\n            borderSetContentComponents.get(DockLocation.BOTTOM)\n          ] }),\n          borderSetContentComponents.get(DockLocation.RIGHT)\n        ] });\n        return /* @__PURE__ */ jsxs(\"div\", { className: classBorderOuter, style: { flexDirection: \"row\" }, children: [\n          borderSetComponents.get(DockLocation.LEFT),\n          /* @__PURE__ */ jsxs(\"div\", { className: classBorderInner, style: { flexDirection: \"column\" }, children: [\n            borderSetComponents.get(DockLocation.TOP),\n            innerWithBorderTabs,\n            borderSetComponents.get(DockLocation.BOTTOM)\n          ] }),\n          borderSetComponents.get(DockLocation.RIGHT)\n        ] });\n      }\n    } else {\n      return /* @__PURE__ */ jsx(\"div\", { className: classMain, ref: this.mainRef, style: { position: \"absolute\", top: 0, left: 0, bottom: 0, right: 0, display: \"flex\" }, children: inner });\n    }\n  }\n  renderLayout() {\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(Row, { layout: this, node: this.props.model.getRoot(this.windowId) }, \"__row__\"),\n      this.renderEdgeIndicators()\n    ] });\n  }\n  renderEdgeIndicators() {\n    const edges = [];\n    const arrowIcon = this.icons.edgeArrow;\n    if (this.state.showEdges) {\n      const r = this.props.model.getRoot(this.windowId).getRect();\n      const length = edgeRectLength;\n      const width = edgeRectWidth;\n      const offset = edgeRectLength / 2;\n      const className = this.getClassName(CLASSES.FLEXLAYOUT__EDGE_RECT);\n      const radius = 50;\n      edges.push(/* @__PURE__ */ jsx(\"div\", { style: { top: 0, left: r.width / 2 - offset, width: length, height: width, borderBottomLeftRadius: radius, borderBottomRightRadius: radius }, className: className + \" \" + this.getClassName(CLASSES.FLEXLAYOUT__EDGE_RECT_TOP), children: /* @__PURE__ */ jsx(\"div\", { style: { transform: \"rotate(180deg)\" }, children: arrowIcon }) }, \"North\"));\n      edges.push(/* @__PURE__ */ jsx(\"div\", { style: { top: r.height / 2 - offset, left: 0, width, height: length, borderTopRightRadius: radius, borderBottomRightRadius: radius }, className: className + \" \" + this.getClassName(CLASSES.FLEXLAYOUT__EDGE_RECT_LEFT), children: /* @__PURE__ */ jsx(\"div\", { style: { transform: \"rotate(90deg)\" }, children: arrowIcon }) }, \"West\"));\n      edges.push(/* @__PURE__ */ jsx(\"div\", { style: { top: r.height - width, left: r.width / 2 - offset, width: length, height: width, borderTopLeftRadius: radius, borderTopRightRadius: radius }, className: className + \" \" + this.getClassName(CLASSES.FLEXLAYOUT__EDGE_RECT_BOTTOM), children: /* @__PURE__ */ jsx(\"div\", { children: arrowIcon }) }, \"South\"));\n      edges.push(/* @__PURE__ */ jsx(\"div\", { style: { top: r.height / 2 - offset, left: r.width - width, width, height: length, borderTopLeftRadius: radius, borderBottomLeftRadius: radius }, className: className + \" \" + this.getClassName(CLASSES.FLEXLAYOUT__EDGE_RECT_RIGHT), children: /* @__PURE__ */ jsx(\"div\", { style: { transform: \"rotate(-90deg)\" }, children: arrowIcon }) }, \"East\"));\n    }\n    return edges;\n  }\n  renderWindows() {\n    const floatingWindows = [];\n    if (this.supportsPopout) {\n      const windows = this.props.model.getwindowsMap();\n      let i = 1;\n      for (const [windowId, layoutWindow] of windows) {\n        if (windowId !== Model.MAIN_WINDOW_ID) {\n          floatingWindows.push(\n            /* @__PURE__ */ jsx(\n              PopoutWindow,\n              {\n                layout: this,\n                title: this.popoutWindowName + \" \" + i,\n                layoutWindow,\n                url: this.popoutURL + \"?id=\" + windowId,\n                onSetWindow: this.onSetWindow,\n                onCloseWindow: this.onCloseWindow,\n                children: /* @__PURE__ */ jsx(\"div\", { className: this.props.popoutClassName, children: /* @__PURE__ */ jsx(_LayoutInternal, { ...this.props, windowId, mainLayout: this }) })\n              },\n              windowId\n            )\n          );\n          i++;\n        }\n      }\n    }\n    return floatingWindows;\n  }\n  renderTabMoveables() {\n    const tabMoveables = /* @__PURE__ */ new Map();\n    this.props.model.visitNodes((node) => {\n      if (node instanceof TabNode) {\n        const child = node;\n        const element = this.getMoveableElement(child.getId());\n        child.setMoveableElement(element);\n        const selected = child.isSelected();\n        const rect = child.getParent().getContentRect();\n        const visible = selected || !child.isEnableRenderOnDemand();\n        const renderTab = child.isRendered() || visible && (rect.width > 0 && rect.height > 0);\n        if (renderTab) {\n          const key = child.getId() + (child.isEnableWindowReMount() ? child.getWindowId() : \"\");\n          tabMoveables.set(node.getId(), createPortal(\n            /* @__PURE__ */ jsx(\n              SizeTracker,\n              {\n                layout: this,\n                node: child,\n                rect,\n                visible,\n                forceRevision: this.state.forceRevision,\n                tabsRevision: this.props.renderRevision\n              },\n              key\n            ),\n            element,\n            key\n          ));\n          child.setRendered(renderTab);\n        }\n      }\n    });\n    return tabMoveables;\n  }\n  renderTabStamps() {\n    const tabStamps = [];\n    this.props.model.visitNodes((node) => {\n      if (node instanceof TabNode) {\n        const child = node;\n        tabStamps.push(/* @__PURE__ */ jsx(DragContainer, { layout: this, node: child }, child.getId()));\n      }\n    });\n    return tabStamps;\n  }\n  renderTabs() {\n    const tabs = /* @__PURE__ */ new Map();\n    this.props.model.visitWindowNodes(this.windowId, (node) => {\n      if (node instanceof TabNode) {\n        const child = node;\n        const selected = child.isSelected();\n        const path = child.getPath();\n        const renderTab = child.isRendered() || selected || !child.isEnableRenderOnDemand();\n        if (renderTab) {\n          tabs.set(child.getId(), /* @__PURE__ */ jsx(\n            Tab,\n            {\n              layout: this,\n              path,\n              node: child,\n              selected\n            },\n            child.getId()\n          ));\n        }\n      }\n    });\n    return tabs;\n  }\n  renderMetricsElements() {\n    return /* @__PURE__ */ jsx(\"div\", { ref: this.findBorderBarSizeRef, className: this.getClassName(CLASSES.FLEXLAYOUT__BORDER_SIZER), children: \"FindBorderBarSize\" }, \"findBorderBarSize\");\n  }\n  checkForBorderToShow(x, y) {\n    const r = this.getBoundingClientRect(this.mainRef.current);\n    const c = r.getCenter();\n    const margin = edgeRectWidth;\n    const offset = edgeRectLength / 2;\n    let overEdge = false;\n    if (this.props.model.isEnableEdgeDock() && this.state.showHiddenBorder === DockLocation.CENTER) {\n      if (y > c.y - offset && y < c.y + offset || x > c.x - offset && x < c.x + offset) {\n        overEdge = true;\n      }\n    }\n    let location = DockLocation.CENTER;\n    if (!overEdge) {\n      if (x <= r.x + margin) {\n        location = DockLocation.LEFT;\n      } else if (x >= r.getRight() - margin) {\n        location = DockLocation.RIGHT;\n      } else if (y <= r.y + margin) {\n        location = DockLocation.TOP;\n      } else if (y >= r.getBottom() - margin) {\n        location = DockLocation.BOTTOM;\n      }\n    }\n    if (location !== this.state.showHiddenBorder) {\n      this.setState({ showHiddenBorder: location });\n    }\n  }\n  tidyMoveablesMap() {\n    const tabs = /* @__PURE__ */ new Map();\n    this.props.model.visitNodes((node, _) => {\n      if (node instanceof TabNode) {\n        tabs.set(node.getId(), node);\n      }\n    });\n    for (const [nodeId, element] of this.moveableElementMap) {\n      if (!tabs.has(nodeId)) {\n        element.remove();\n        this.moveableElementMap.delete(nodeId);\n      }\n    }\n  }\n  reorderComponents(components, ids) {\n    const nextIds = [];\n    const nextIdsSet = /* @__PURE__ */ new Set();\n    let reordered = [];\n    for (const id of ids) {\n      if (components.get(id)) {\n        nextIds.push(id);\n        nextIdsSet.add(id);\n      }\n    }\n    ids.splice(0, ids.length, ...nextIds);\n    for (const [id, _] of components) {\n      if (!nextIdsSet.has(id)) {\n        ids.push(id);\n      }\n    }\n    reordered = ids.map((id) => {\n      return components.get(id);\n    });\n    return reordered;\n  }\n  redraw(type) {\n    this.mainLayout.setState((state, props) => {\n      return { forceRevision: state.forceRevision + 1 };\n    });\n  }\n  redrawInternal(type) {\n    this.mainLayout.setState((state, props) => {\n      return { layoutRevision: state.layoutRevision + 1 };\n    });\n  }\n  doAction(action) {\n    if (this.props.onAction !== void 0) {\n      const outcome = this.props.onAction(action);\n      if (outcome !== void 0) {\n        return this.props.model.doAction(outcome);\n      }\n      return void 0;\n    } else {\n      return this.props.model.doAction(action);\n    }\n  }\n  getBoundingClientRect(div) {\n    const layoutRect = this.getDomRect();\n    if (layoutRect) {\n      return Rect.getBoundingClientRect(div).relativeTo(layoutRect);\n    }\n    return Rect.empty();\n  }\n  getMoveableContainer() {\n    return this.moveablesRef.current;\n  }\n  getMoveableElement(id) {\n    let moveableElement = this.moveableElementMap.get(id);\n    if (moveableElement === void 0) {\n      moveableElement = document.createElement(\"div\");\n      this.moveablesRef.current.appendChild(moveableElement);\n      moveableElement.className = CLASSES.FLEXLAYOUT__TAB_MOVEABLE;\n      this.moveableElementMap.set(id, moveableElement);\n    }\n    return moveableElement;\n  }\n  getMainLayout() {\n    return this.mainLayout;\n  }\n  getCurrentDocument() {\n    return this.currentDocument;\n  }\n  getDomRect() {\n    if (this.selfRef.current) {\n      return Rect.fromDomRect(this.selfRef.current.getBoundingClientRect());\n    } else {\n      return Rect.empty();\n    }\n  }\n  getWindowId() {\n    return this.windowId;\n  }\n  getRootDiv() {\n    return this.selfRef.current;\n  }\n  getMainElement() {\n    return this.mainRef.current;\n  }\n  getFactory() {\n    return this.props.factory;\n  }\n  isSupportsPopout() {\n    return this.supportsPopout;\n  }\n  isRealtimeResize() {\n    return this.props.realtimeResize ?? false;\n  }\n  getPopoutURL() {\n    return this.popoutURL;\n  }\n  setEditingTab(tabNode) {\n    this.setState({ editingTab: tabNode });\n  }\n  getEditingTab() {\n    return this.state.editingTab;\n  }\n  getModel() {\n    return this.props.model;\n  }\n  getScreenRect(inRect) {\n    const rect = inRect.clone();\n    const layoutRect = this.getDomRect();\n    const navHeight = 60;\n    const navWidth = 2;\n    rect.x = this.currentWindow.screenX + this.currentWindow.scrollX + navWidth / 2 + layoutRect.x + rect.x;\n    rect.y = this.currentWindow.screenY + this.currentWindow.scrollY + (navHeight - navWidth / 2) + layoutRect.y + rect.y;\n    rect.height += navHeight;\n    rect.width += navWidth;\n    return rect;\n  }\n  addTabToTabSet(tabsetId, json) {\n    const tabsetNode = this.props.model.getNodeById(tabsetId);\n    if (tabsetNode !== void 0) {\n      const node = this.doAction(Actions.addNode(json, tabsetId, DockLocation.CENTER, -1));\n      return node;\n    }\n    return void 0;\n  }\n  addTabToActiveTabSet(json) {\n    const tabsetNode = this.props.model.getActiveTabset(this.windowId);\n    if (tabsetNode !== void 0) {\n      const node = this.doAction(Actions.addNode(json, tabsetNode.getId(), DockLocation.CENTER, -1));\n      return node;\n    }\n    return void 0;\n  }\n  maximize(tabsetNode) {\n    this.doAction(Actions.maximizeToggle(tabsetNode.getId(), this.getWindowId()));\n  }\n  customizeTab(tabNode, renderValues) {\n    if (this.props.onRenderTab) {\n      this.props.onRenderTab(tabNode, renderValues);\n    }\n  }\n  customizeTabSet(tabSetNode, renderValues) {\n    if (this.props.onRenderTabSet) {\n      this.props.onRenderTabSet(tabSetNode, renderValues);\n    }\n  }\n  i18nName(id, param) {\n    let message;\n    if (this.props.i18nMapper) {\n      message = this.props.i18nMapper(id, param);\n    }\n    if (message === void 0) {\n      message = id + (param === void 0 ? \"\" : param);\n    }\n    return message;\n  }\n  getShowOverflowMenu() {\n    return this.props.onShowOverflowMenu;\n  }\n  getTabSetPlaceHolderCallback() {\n    return this.props.onTabSetPlaceHolder;\n  }\n  showContextMenu(node, event) {\n    if (this.props.onContextMenu) {\n      this.props.onContextMenu(node, event);\n    }\n  }\n  auxMouseClick(node, event) {\n    if (this.props.onAuxMouseClick) {\n      this.props.onAuxMouseClick(node, event);\n    }\n  }\n  showOverlay(show) {\n    this.setState({ showOverlay: show });\n    enablePointerOnIFrames(!show, this.currentDocument);\n  }\n  // *************************** Start Drag Drop *************************************\n  addTabWithDragAndDrop(event, json, onDrop) {\n    const tempNode = TabNode.fromJson(json, this.props.model, false);\n    _LayoutInternal.dragState = new DragState(this.mainLayout, \"add\", tempNode, json, onDrop);\n  }\n  moveTabWithDragAndDrop(event, node) {\n    this.setDragNode(event, node);\n  }\n  setDragComponent(event, component, x, y) {\n    const dragElement = /* @__PURE__ */ jsx(\n      \"div\",\n      {\n        style: { position: \"unset\" },\n        className: this.getClassName(CLASSES.FLEXLAYOUT__LAYOUT) + \" \" + this.getClassName(CLASSES.FLEXLAYOUT__DRAG_RECT),\n        children: component\n      }\n    );\n    const tempDiv = this.currentDocument.createElement(\"div\");\n    tempDiv.setAttribute(\"data-layout-path\", \"/drag-rectangle\");\n    tempDiv.style.position = \"absolute\";\n    tempDiv.style.left = \"-10000px\";\n    tempDiv.style.top = \"-10000px\";\n    this.currentDocument.body.appendChild(tempDiv);\n    createRoot(tempDiv).render(dragElement);\n    event.dataTransfer.setDragImage(tempDiv, x, y);\n    setTimeout(() => {\n      this.currentDocument.body.removeChild(tempDiv);\n    }, 0);\n  }\n  setDraggingOverWindow(overWindow) {\n    if (this.isDraggingOverWindow !== overWindow) {\n      if (this.outlineDiv) {\n        this.outlineDiv.style.visibility = overWindow ? \"hidden\" : \"visible\";\n      }\n      if (overWindow) {\n        this.setState({ showEdges: false });\n      } else {\n        if (this.props.model.getMaximizedTabset(this.windowId) === void 0) {\n          this.setState({ showEdges: this.props.model.isEnableEdgeDock() });\n        }\n      }\n      this.isDraggingOverWindow = overWindow;\n    }\n  }\n  clearDragMain() {\n    _LayoutInternal.dragState = void 0;\n    if (this.windowId === Model.MAIN_WINDOW_ID) {\n      this.isDraggingOverWindow = false;\n    }\n    for (const [, layoutWindow] of this.props.model.getwindowsMap()) {\n      layoutWindow.layout.clearDragLocal();\n    }\n  }\n  clearDragLocal() {\n    this.setState({ showEdges: false });\n    this.showOverlay(false);\n    this.dragEnterCount = 0;\n    this.dragging = false;\n    if (this.outlineDiv) {\n      this.selfRef.current.removeChild(this.outlineDiv);\n      this.outlineDiv = void 0;\n    }\n  }\n  // *************************** End Drag Drop *************************************\n};\n__publicField(_LayoutInternal, \"dragState\");\nlet LayoutInternal = _LayoutInternal;\nconst FlexLayoutVersion = \"0.8.17\";\nconst defaultIcons = {\n  close: /* @__PURE__ */ jsx(CloseIcon, {}),\n  closeTabset: /* @__PURE__ */ jsx(CloseIcon, {}),\n  popout: /* @__PURE__ */ jsx(PopoutIcon, {}),\n  maximize: /* @__PURE__ */ jsx(MaximizeIcon, {}),\n  restore: /* @__PURE__ */ jsx(RestoreIcon, {}),\n  more: /* @__PURE__ */ jsx(OverflowIcon, {}),\n  edgeArrow: /* @__PURE__ */ jsx(EdgeIcon, {}),\n  activeTabset: /* @__PURE__ */ jsx(AsterickIcon, {})\n};\nconst defaultSupportsPopout = isDesktop();\nconst edgeRectLength = 100;\nconst edgeRectWidth = 10;\nclass DragState {\n  constructor(mainLayout, dragSource, dragNode, dragJson, fnNewNodeDropped) {\n    __publicField(this, \"mainLayout\");\n    __publicField(this, \"dragSource\");\n    __publicField(this, \"dragNode\");\n    __publicField(this, \"dragJson\");\n    __publicField(this, \"fnNewNodeDropped\");\n    this.mainLayout = mainLayout;\n    this.dragSource = dragSource;\n    this.dragNode = dragNode;\n    this.dragJson = dragJson;\n    this.fnNewNodeDropped = fnNewNodeDropped;\n  }\n}\nexport {\n  Action,\n  Actions,\n  AddIcon,\n  AsterickIcon,\n  BorderNode,\n  BorderSet,\n  CLASSES,\n  CloseIcon,\n  DefaultMax,\n  DefaultMin,\n  DockLocation,\n  DropInfo,\n  EdgeIcon,\n  FlexLayoutVersion,\n  I18nLabel,\n  ICloseType,\n  Layout,\n  LayoutInternal,\n  LayoutWindow,\n  MaximizeIcon,\n  MenuIcon,\n  Model,\n  Node,\n  Orientation,\n  OverflowIcon,\n  PopoutIcon,\n  Rect,\n  RestoreIcon,\n  RowNode,\n  SettingsIcon,\n  TabNode,\n  TabSetNode\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAOA,yBAAoC;AACpC,YAAuB;AACvB,mBAAkC;AAClC,uBAA6B;AAC7B,oBAA2B;AAP3B,IAAI,YAAY,OAAO;AACvB,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,IAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU,gBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AAM7G,IAAM,eAAe,MAAMA,cAAa;AAAA;AAAA,EAEtC,YAAY,MAAM;AAEhB,kBAAc,MAAM,OAAO;AAC3B,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,OAAO,KAAK,MAAM;AAChB,QAAI,SAASA,cAAa,MAAM;AAC9B,aAAOA,cAAa;AAAA,IACtB,OAAO;AACL,aAAOA,cAAa;AAAA,IACtB;AAAA,EACF;AAAA,EACA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AACF;AACA,cAAc,cAAc,QAAQ,IAAI,aAAa,MAAM,CAAC;AAC5D,cAAc,cAAc,QAAQ,IAAI,aAAa,MAAM,CAAC;AAC5D,IAAI,cAAc;AAClB,IAAM,OAAN,MAAM,MAAK;AAAA,EACT,YAAY,GAAG,GAAG,OAAO,QAAQ;AAC/B,kBAAc,MAAM,GAAG;AACvB,kBAAc,MAAM,GAAG;AACvB,kBAAc,MAAM,OAAO;AAC3B,kBAAc,MAAM,QAAQ;AAC5B,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO,QAAQ;AACb,WAAO,IAAI,MAAK,GAAG,GAAG,GAAG,CAAC;AAAA,EAC5B;AAAA,EACA,OAAO,SAAS,MAAM;AACpB,WAAO,IAAI,MAAK,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,EACzD;AAAA,EACA,SAAS;AACP,WAAO,EAAE,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,OAAO,KAAK,OAAO,QAAQ,KAAK,OAAO;AAAA,EACxE;AAAA,EACA,KAAK,OAAO;AACV,SAAK,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI;AACtC,SAAK,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI;AACtC,SAAK,QAAQ,KAAK,MAAM,KAAK,QAAQ,KAAK,IAAI;AAC9C,SAAK,SAAS,KAAK,MAAM,KAAK,SAAS,KAAK,IAAI;AAAA,EAClD;AAAA,EACA,OAAO,sBAAsB,SAAS;AACpC,UAAM,EAAE,GAAG,GAAG,OAAO,OAAO,IAAI,QAAQ,sBAAsB;AAC9D,WAAO,IAAI,MAAK,GAAG,GAAG,OAAO,MAAM;AAAA,EACrC;AAAA,EACA,OAAO,eAAe,SAAS;AAC7B,UAAM,OAAO,QAAQ,sBAAsB;AAC3C,UAAM,SAAS,OAAO,iBAAiB,OAAO;AAC9C,UAAM,cAAc,WAAW,OAAO,WAAW;AACjD,UAAM,eAAe,WAAW,OAAO,YAAY;AACnD,UAAM,aAAa,WAAW,OAAO,UAAU;AAC/C,UAAM,gBAAgB,WAAW,OAAO,aAAa;AACrD,UAAM,kBAAkB,WAAW,OAAO,eAAe;AACzD,UAAM,mBAAmB,WAAW,OAAO,gBAAgB;AAC3D,UAAM,iBAAiB,WAAW,OAAO,cAAc;AACvD,UAAM,oBAAoB,WAAW,OAAO,iBAAiB;AAC7D,UAAM,eAAe,KAAK,QAAQ,kBAAkB,cAAc,eAAe;AACjF,UAAM,gBAAgB,KAAK,SAAS,iBAAiB,aAAa,gBAAgB;AAClF,WAAO,IAAI;AAAA,MACT,KAAK,OAAO,kBAAkB;AAAA,MAC9B,KAAK,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,YAAY,SAAS;AAC1B,WAAO,IAAI,MAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,OAAO,QAAQ,MAAM;AAAA,EACrE;AAAA,EACA,WAAW,GAAG;AACZ,WAAO,IAAI,MAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,EACrE;AAAA,EACA,QAAQ;AACN,WAAO,IAAI,MAAK,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,EACzD;AAAA,EACA,OAAO,MAAM;AACX,WAAO,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK,MAAM,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK,MAAM,KAAK,WAAW,QAAQ,OAAO,SAAS,KAAK,UAAU,KAAK,YAAY,QAAQ,OAAO,SAAS,KAAK;AAAA,EAC5M;AAAA,EACA,UAAU,MAAM;AACd,WAAO,KAAK,WAAW,QAAQ,OAAO,SAAS,KAAK,UAAU,KAAK,YAAY,QAAQ,OAAO,SAAS,KAAK;AAAA,EAC9G;AAAA,EACA,YAAY;AACV,WAAO,KAAK,IAAI,KAAK;AAAA,EACvB;AAAA,EACA,WAAW;AACT,WAAO,KAAK,IAAI,KAAK;AAAA,EACvB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,IAAI,KAAK;AAAA,EACvB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,IAAI,KAAK;AAAA,EACvB;AAAA,EACA,YAAY;AACV,WAAO,EAAE,GAAG,KAAK,IAAI,KAAK,QAAQ,GAAG,GAAG,KAAK,IAAI,KAAK,SAAS,EAAE;AAAA,EACnE;AAAA,EACA,gBAAgB,SAAS,UAAU;AACjC,SAAK,kBAAkB,QAAQ,OAAO,QAAQ;AAAA,EAChD;AAAA,EACA,kBAAkB,QAAQ,WAAW,YAAY;AAC/C,WAAO,OAAO,KAAK,IAAI;AACvB,WAAO,MAAM,KAAK,IAAI;AACtB,WAAO,QAAQ,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI;AACzC,WAAO,SAAS,KAAK,IAAI,GAAG,KAAK,MAAM,IAAI;AAC3C,WAAO,WAAW;AAClB,WAAO;AAAA,EACT;AAAA,EACA,SAAS,GAAG,GAAG;AACb,QAAI,KAAK,KAAK,KAAK,KAAK,KAAK,SAAS,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,UAAU,GAAG;AAC/E,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,aAAa,QAAQ;AACnB,WAAO,IAAI,MAAK,KAAK,IAAI,OAAO,MAAM,KAAK,IAAI,OAAO,KAAK,KAAK,IAAI,GAAG,KAAK,QAAQ,OAAO,OAAO,OAAO,KAAK,GAAG,KAAK,IAAI,GAAG,KAAK,SAAS,OAAO,MAAM,OAAO,MAAM,CAAC;AAAA,EACxK;AAAA,EACA,aAAa,WAAW;AACtB,SAAK,KAAK,UAAU,QAAQ,KAAK,SAAS;AAC1C,SAAK,KAAK,UAAU,SAAS,KAAK,UAAU;AAAA,EAC9C;AAAA;AAAA,EAEA,SAAS,aAAa;AACpB,QAAI,WAAW,KAAK;AACpB,QAAI,gBAAgB,YAAY,MAAM;AACpC,iBAAW,KAAK;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,WAAO,cAAc,KAAK,IAAI,SAAS,KAAK,IAAI,aAAa,KAAK,QAAQ,cAAc,KAAK,SAAS;AAAA,EACxG;AACF;AACA,IAAM,gBAAgB,MAAMC,eAAc;AAAA;AAAA,EAExC,YAAY,OAAO,cAAc,YAAY;AAE3C,kBAAc,MAAM,MAAM;AAE1B,kBAAc,MAAM,aAAa;AAEjC,kBAAc,MAAM,WAAW;AAC/B,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,IAAAA,eAAc,OAAO,IAAI,KAAK,MAAM,IAAI;AAAA,EAC1C;AAAA;AAAA,EAEA,OAAO,UAAU,MAAM;AACrB,WAAOA,eAAc,OAAO,IAAI,IAAI;AAAA,EACtC;AAAA;AAAA,EAEA,OAAO,YAAY,MAAM,GAAG,GAAG;AAC7B,SAAK,IAAI,KAAK,KAAK,KAAK;AACxB,SAAK,IAAI,KAAK,KAAK,KAAK;AACxB,QAAI,KAAK,QAAQ,IAAI,QAAQ,KAAK,QAAQ,IAAI,MAAM;AAClD,aAAOA,eAAc;AAAA,IACvB;AACA,UAAM,KAAK,KAAK;AAChB,UAAM,KAAK,KAAK,IAAI;AACpB,QAAI,IAAI;AACN,aAAO,KAAKA,eAAc,SAASA,eAAc;AAAA,IACnD,OAAO;AACL,aAAO,KAAKA,eAAc,QAAQA,eAAc;AAAA,IAClD;AAAA,EACF;AAAA,EACA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,YAAY,GAAG;AACb,QAAI,SAASA,eAAc,KAAK;AAC9B,aAAO,IAAI,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS,CAAC;AAAA,IACjD,WAAW,SAASA,eAAc,QAAQ;AACxC,aAAO,IAAI,KAAK,EAAE,GAAG,EAAE,UAAU,IAAI,EAAE,SAAS,GAAG,EAAE,OAAO,EAAE,SAAS,CAAC;AAAA,IAC1E;AACA,QAAI,SAASA,eAAc,MAAM;AAC/B,aAAO,IAAI,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,GAAG,EAAE,MAAM;AAAA,IACjD,WAAW,SAASA,eAAc,OAAO;AACvC,aAAO,IAAI,KAAK,EAAE,SAAS,IAAI,EAAE,QAAQ,GAAG,EAAE,GAAG,EAAE,QAAQ,GAAG,EAAE,MAAM;AAAA,IACxE,OAAO;AACL,aAAO,EAAE,MAAM;AAAA,IACjB;AAAA,EACF;AAAA;AAAA,EAEA,MAAM,MAAM,MAAM;AAChB,QAAI,SAASA,eAAc,KAAK;AAC9B,YAAM,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,IAAI;AACpD,YAAM,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,MAAM,KAAK,OAAO,KAAK,SAAS,IAAI;AACzE,aAAO,EAAE,OAAO,IAAI,KAAK,GAAG;AAAA,IAC9B,WAAW,SAASA,eAAc,MAAM;AACtC,YAAM,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG,MAAM,KAAK,MAAM;AACrD,YAAM,KAAK,IAAI,KAAK,KAAK,IAAI,MAAM,KAAK,GAAG,KAAK,QAAQ,MAAM,KAAK,MAAM;AACzE,aAAO,EAAE,OAAO,IAAI,KAAK,GAAG;AAAA,IAC9B;AACA,QAAI,SAASA,eAAc,OAAO;AAChC,YAAM,KAAK,IAAI,KAAK,KAAK,SAAS,IAAI,MAAM,KAAK,GAAG,MAAM,KAAK,MAAM;AACrE,YAAM,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,QAAQ,MAAM,KAAK,MAAM;AAClE,aAAO,EAAE,OAAO,IAAI,KAAK,GAAG;AAAA,IAC9B,OAAO;AACL,YAAM,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,UAAU,IAAI,MAAM,KAAK,OAAO,IAAI;AACrE,YAAM,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,SAAS,IAAI;AAClE,aAAO,EAAE,OAAO,IAAI,KAAK,GAAG;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,SAASA,eAAc,KAAK;AAC9B,aAAOA,eAAc;AAAA,IACvB,WAAW,SAASA,eAAc,MAAM;AACtC,aAAOA,eAAc;AAAA,IACvB;AACA,QAAI,SAASA,eAAc,OAAO;AAChC,aAAOA,eAAc;AAAA,IACvB,OAAO;AACL,aAAOA,eAAc;AAAA,IACvB;AAAA,EACF;AAAA,EACA,WAAW;AACT,WAAO,yBAAyB,KAAK,OAAO,mBAAmB,KAAK,cAAc;AAAA,EACpF;AACF;AACA,cAAc,eAAe,UAA0B,oBAAI,IAAI,CAAC;AAChE,cAAc,eAAe,OAAO,IAAI,cAAc,OAAO,YAAY,MAAM,CAAC,CAAC;AACjF,cAAc,eAAe,UAAU,IAAI,cAAc,UAAU,YAAY,MAAM,CAAC,CAAC;AACvF,cAAc,eAAe,QAAQ,IAAI,cAAc,QAAQ,YAAY,MAAM,CAAC,CAAC;AACnF,cAAc,eAAe,SAAS,IAAI,cAAc,SAAS,YAAY,MAAM,CAAC,CAAC;AACrF,cAAc,eAAe,UAAU,IAAI,cAAc,UAAU,YAAY,MAAM,CAAC,CAAC;AACvF,IAAI,eAAe;AACnB,IAAI,aAA6B,CAAC,eAAe;AAC/C,aAAW,WAAW,IAAI;AAC1B,aAAW,cAAc,IAAI;AAC7B,aAAW,eAAe,IAAI;AAC9B,aAAW,aAAa,IAAI;AAC5B,aAAW,WAAW,IAAI;AAC1B,aAAW,UAAU,IAAI;AACzB,aAAW,SAAS,IAAI;AACxB,aAAW,YAAY,IAAI;AAC3B,aAAW,uBAAuB,IAAI;AACtC,aAAW,2BAA2B,IAAI;AAC1C,aAAW,iCAAiC,IAAI;AAChD,SAAO;AACT,GAAG,aAAa,CAAC,CAAC;AAClB,IAAI,WAA2B,CAAC,aAAa;AAC3C,WAAS,oBAAoB,IAAI;AACjC,WAAS,qBAAqB,IAAI;AAClC,WAAS,iCAAiC,IAAI;AAC9C,WAAS,2BAA2B,IAAI;AACxC,WAAS,4BAA4B,IAAI;AACzC,WAAS,mCAAmC,IAAI;AAChD,WAAS,mCAAmC,IAAI;AAChD,WAAS,oCAAoC,IAAI;AACjD,WAAS,qCAAqC,IAAI;AAClD,WAAS,uCAAuC,IAAI;AACpD,WAAS,4CAA4C,IAAI;AACzD,WAAS,6CAA6C,IAAI;AAC1D,WAAS,0BAA0B,IAAI;AACvC,WAAS,2BAA2B,IAAI;AACxC,WAAS,wCAAwC,IAAI;AACrD,WAAS,yCAAyC,IAAI;AACtD,WAAS,gCAAgC,IAAI;AAC7C,WAAS,4BAA4B,IAAI;AACzC,WAAS,0BAA0B,IAAI;AACvC,WAAS,4BAA4B,IAAI;AACzC,WAAS,6BAA6B,IAAI;AAC1C,WAAS,mCAAmC,IAAI;AAChD,WAAS,yCAAyC,IAAI;AACtD,WAAS,uBAAuB,IAAI;AACpC,WAAS,uBAAuB,IAAI;AACpC,WAAS,2BAA2B,IAAI;AACxC,WAAS,4BAA4B,IAAI;AACzC,WAAS,8BAA8B,IAAI;AAC3C,WAAS,6BAA6B,IAAI;AAC1C,WAAS,sCAAsC,IAAI;AACnD,WAAS,oCAAoC,IAAI;AACjD,WAAS,qCAAqC,IAAI;AAClD,WAAS,oBAAoB,IAAI;AACjC,WAAS,8BAA8B,IAAI;AAC3C,WAAS,4BAA4B,IAAI;AACzC,WAAS,+BAA+B,IAAI;AAC5C,WAAS,yBAAyB,IAAI;AACtC,WAAS,qCAAqC,IAAI;AAClD,WAAS,2CAA2C,IAAI;AACxD,WAAS,0BAA0B,IAAI;AACvC,WAAS,+BAA+B,IAAI;AAC5C,WAAS,sBAAsB,IAAI;AACnC,WAAS,4BAA4B,IAAI;AACzC,WAAS,uBAAuB,IAAI;AACpC,WAAS,6BAA6B,IAAI;AAC1C,WAAS,2BAA2B,IAAI;AACxC,WAAS,6BAA6B,IAAI;AAC1C,WAAS,kCAAkC,IAAI;AAC/C,WAAS,kCAAkC,IAAI;AAC/C,WAAS,iBAAiB,IAAI;AAC9B,WAAS,iBAAiB,IAAI;AAC9B,WAAS,0BAA0B,IAAI;AACvC,WAAS,0BAA0B,IAAI;AACvC,WAAS,yBAAyB,IAAI;AACtC,WAAS,oBAAoB,IAAI;AACjC,WAAS,8BAA8B,IAAI;AAC3C,WAAS,2BAA2B,IAAI;AACxC,WAAS,mCAAmC,IAAI;AAChD,WAAS,8BAA8B,IAAI;AAC3C,WAAS,6BAA6B,IAAI;AAC1C,WAAS,gCAAgC,IAAI;AAC7C,WAAS,4BAA4B,IAAI;AACzC,WAAS,iCAAiC,IAAI;AAC9C,WAAS,kCAAkC,IAAI;AAC/C,WAAS,4BAA4B,IAAI;AACzC,WAAS,+CAA+C,IAAI;AAC5D,WAAS,gDAAgD,IAAI;AAC7D,WAAS,iCAAiC,IAAI;AAC9C,WAAS,kCAAkC,IAAI;AAC/C,WAAS,wBAAwB,IAAI;AACrC,WAAS,yBAAyB,IAAI;AACtC,WAAS,wBAAwB,IAAI;AACrC,WAAS,gCAAgC,IAAI;AAC7C,WAAS,gCAAgC,IAAI;AAC7C,WAAS,gCAAgC,IAAI;AAC7C,WAAS,iCAAiC,IAAI;AAC9C,WAAS,uCAAuC,IAAI;AACpD,WAAS,gCAAgC,IAAI;AAC7C,WAAS,iCAAiC,IAAI;AAC9C,WAAS,8BAA8B,IAAI;AAC3C,WAAS,yBAAyB,IAAI;AACtC,WAAS,gCAAgC,IAAI;AAC7C,WAAS,8BAA8B,IAAI;AAC3C,WAAS,iCAAiC,IAAI;AAC9C,WAAS,sCAAsC,IAAI;AACnD,WAAS,kDAAkD,IAAI;AAC/D,WAAS,sCAAsC,IAAI;AACnD,WAAS,kCAAkC,IAAI;AAC/C,WAAS,6BAA6B,IAAI;AAC1C,WAAS,uCAAuC,IAAI;AACpD,WAAS,wBAAwB,IAAI;AACrC,WAAS,4BAA4B,IAAI;AACzC,WAAS,sCAAsC,IAAI;AACnD,SAAO;AACT,GAAG,WAAW,CAAC,CAAC;AAChB,IAAM,SAAN,MAAa;AAAA,EACX,YAAY,MAAM,MAAM;AACtB,kBAAc,MAAM,MAAM;AAC1B,kBAAc,MAAM,MAAM;AAC1B,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,WAAW,MAAMC,UAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU9B,OAAO,QAAQ,MAAM,UAAU,UAAU,OAAO,QAAQ;AACtD,WAAO,IAAI,OAAOA,UAAS,UAAU;AAAA,MACnC;AAAA,MACA,QAAQ;AAAA,MACR,UAAU,SAAS,QAAQ;AAAA,MAC3B;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,SAAS,YAAY,UAAU,UAAU,OAAO,QAAQ;AAC7D,WAAO,IAAI,OAAOA,UAAS,WAAW;AAAA,MACpC,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,UAAU,SAAS,QAAQ;AAAA,MAC3B;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,UAAU,WAAW;AAC1B,WAAO,IAAI,OAAOA,UAAS,YAAY,EAAE,MAAM,UAAU,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,aAAa,cAAc;AAChC,WAAO,IAAI,OAAOA,UAAS,eAAe,EAAE,MAAM,aAAa,CAAC;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,UAAU,WAAW,MAAM;AAChC,WAAO,IAAI,OAAOA,UAAS,YAAY,EAAE,MAAM,WAAW,KAAK,CAAC;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,UAAU,WAAW;AAC1B,WAAO,IAAI,OAAOA,UAAS,YAAY,EAAE,SAAS,UAAU,CAAC;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,gBAAgB,cAAc,UAAU;AAC7C,WAAO,IAAI,OAAOA,UAAS,mBAAmB,EAAE,YAAY,cAAc,SAAS,CAAC;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,cAAc,QAAQ,SAAS;AACpC,WAAO,IAAI,OAAOA,UAAS,gBAAgB,EAAE,QAAQ,QAAQ,CAAC;AAAA,EAChE;AAAA,EACA,OAAO,kBAAkB,QAAQ,KAAK;AACpC,WAAO,IAAI,OAAOA,UAAS,qBAAqB,EAAE,MAAM,QAAQ,IAAI,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,eAAe,cAAc,UAAU;AAC5C,WAAO,IAAI,OAAOA,UAAS,iBAAiB,EAAE,MAAM,cAAc,SAAS,CAAC;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,sBAAsB,YAAY;AACvC,WAAO,IAAI,OAAOA,UAAS,yBAAyB,EAAE,MAAM,WAAW,CAAC;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,qBAAqB,QAAQ,YAAY;AAC9C,WAAO,IAAI,OAAOA,UAAS,wBAAwB,EAAE,MAAM,QAAQ,MAAM,WAAW,CAAC;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,UAAU,QAAQ;AACvB,WAAO,IAAI,OAAOA,UAAS,YAAY,EAAE,MAAM,OAAO,CAAC;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,aAAa,QAAQ;AAC1B,WAAO,IAAI,OAAOA,UAAS,eAAe,EAAE,MAAM,OAAO,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,YAAY,UAAU;AAC3B,WAAO,IAAI,OAAOA,UAAS,cAAc,EAAE,SAAS,CAAC;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,aAAa,QAAQ,MAAM;AAChC,WAAO,IAAI,OAAOA,UAAS,eAAe,EAAE,QAAQ,KAAK,CAAC;AAAA,EAC5D;AACF;AACA,cAAc,UAAU,YAAY,oBAAoB;AACxD,cAAc,UAAU,aAAa,qBAAqB;AAC1D,cAAc,UAAU,cAAc,sBAAsB;AAC5D,cAAc,UAAU,iBAAiB,yBAAyB;AAClE,cAAc,UAAU,cAAc,sBAAsB;AAC5D,cAAc,UAAU,cAAc,sBAAsB;AAC5D,cAAc,UAAU,qBAAqB,4BAA4B;AACzE,cAAc,UAAU,kBAAkB,0BAA0B;AACpE,cAAc,UAAU,uBAAuB,8BAA8B;AAC7E,cAAc,UAAU,mBAAmB,2BAA2B;AACtE,cAAc,UAAU,2BAA2B,kCAAkC;AACrF,cAAc,UAAU,0BAA0B,iCAAiC;AACnF,cAAc,UAAU,cAAc,sBAAsB;AAC5D,cAAc,UAAU,iBAAiB,yBAAyB;AAClE,cAAc,UAAU,gBAAgB,wBAAwB;AAChE,cAAc,UAAU,iBAAiB,yBAAyB;AAClE,IAAI,UAAU;AACd,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,MAAM,WAAW,cAAc,iBAAiB;AAC1D,kBAAc,MAAM,MAAM;AAC1B,kBAAc,MAAM,OAAO;AAC3B,kBAAc,MAAM,WAAW;AAC/B,kBAAc,MAAM,YAAY;AAChC,kBAAc,MAAM,YAAY;AAChC,kBAAc,MAAM,cAAc;AAClC,kBAAc,MAAM,iBAAiB;AACrC,kBAAc,MAAM,MAAM;AAC1B,kBAAc,MAAM,UAAU;AAC9B,kBAAc,MAAM,OAAO;AAC3B,kBAAc,MAAM,aAAa;AACjC,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EACd;AAAA,EACA,QAAQ,OAAO;AACb,SAAK,OAAO;AACZ,WAAO;AAAA,EACT;AAAA,EACA,SAAS,OAAO;AACd,SAAK,QAAQ;AACb,WAAO;AAAA,EACT;AAAA,EACA,eAAe,OAAO;AACpB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,cAAc;AACZ,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,SAAK,QAAQ;AACb,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,cAAc,OAAO;AACnB,SAAK,aAAa;AAAA,EACpB;AACF;AACA,cAAc,WAAW,UAAU,QAAQ;AAC3C,cAAc,WAAW,UAAU,QAAQ;AAC3C,cAAc,WAAW,WAAW,SAAS;AAC7C,IAAM,uBAAN,MAA2B;AAAA,EACzB,cAAc;AACZ,kBAAc,MAAM,YAAY;AAChC,kBAAc,MAAM,iBAAiB;AACrC,SAAK,aAAa,CAAC;AACnB,SAAK,kBAAkC,oBAAI,IAAI;AAAA,EACjD;AAAA,EACA,WAAW,MAAM,WAAW,cAAc,iBAAiB;AACzD,UAAM,OAAO,IAAI,UAAU,MAAM,WAAW,cAAc,eAAe;AACzE,SAAK,WAAW,KAAK,IAAI;AACzB,SAAK,gBAAgB,IAAI,MAAM,IAAI;AACnC,WAAO;AAAA,EACT;AAAA,EACA,aAAa,MAAM,WAAW;AAC5B,WAAO,KAAK,WAAW,MAAM,WAAW,QAAQ,KAAK;AAAA,EACvD;AAAA,EACA,IAAI,MAAM,cAAc,iBAAiB;AACvC,WAAO,KAAK,WAAW,MAAM,QAAQ,cAAc,eAAe;AAAA,EACpE;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa,MAAM;AACjB,UAAM,aAAa,KAAK,gBAAgB,IAAI,IAAI;AAChD,QAAI,eAAe,QAAQ;AACzB,aAAO,WAAW;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAAS,KAAK;AACnB,eAAW,QAAQ,KAAK,YAAY;AAClC,YAAM,YAAY,IAAI,KAAK,IAAI;AAC/B,UAAI,KAAK,mBAAmB,cAAc,KAAK,cAAc;AAC3D,gBAAQ,KAAK,IAAI,IAAI;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,SAAS,KAAK;AACrB,eAAW,QAAQ,KAAK,YAAY;AAClC,UAAI,YAAY,QAAQ,KAAK,IAAI;AACjC,UAAI,cAAc,UAAU,KAAK,OAAO;AACtC,oBAAY,QAAQ,KAAK,KAAK;AAAA,MAChC;AACA,UAAI,cAAc,QAAQ;AACxB,YAAI,KAAK,IAAI,IAAI,KAAK;AAAA,MACxB,OAAO;AACL,YAAI,KAAK,IAAI,IAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,SAAS,KAAK;AACnB,eAAW,QAAQ,KAAK,YAAY;AAClC,UAAI,OAAO,UAAU,eAAe,KAAK,SAAS,KAAK,IAAI,GAAG;AAC5D,cAAM,YAAY,QAAQ,KAAK,IAAI;AACnC,YAAI,cAAc,QAAQ;AACxB,iBAAO,IAAI,KAAK,IAAI;AAAA,QACtB,OAAO;AACL,cAAI,KAAK,IAAI,IAAI;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,KAAK;AACf,eAAW,QAAQ,KAAK,YAAY;AAClC,UAAI,KAAK,IAAI,IAAI,KAAK;AAAA,IACxB;AAAA,EACF;AAAA,EACA,eAAe,MAAM,iBAAiB;AACpC,eAAW,QAAQ,gBAAgB,YAAY;AAC7C,UAAI,KAAK,aAAa,KAAK,gBAAgB,IAAI,KAAK,SAAS,GAAG;AAC9D,cAAM,aAAa,KAAK,gBAAgB,IAAI,KAAK,SAAS;AAC1D,mBAAW,cAAc,IAAI;AAC7B,aAAK,cAAc,UAAU;AAC7B,mBAAW,cAAc,IAAI;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA,EACA,sBAAsB,MAAM,kBAAkB;AAC5C,QAAI,IAAI;AACR,UAAM,QAAQ,CAAC;AACf,UAAM,SAAS,KAAK,WAAW,KAAK,CAAC,GAAG,MAAM,EAAE,KAAK,cAAc,EAAE,IAAI,CAAC;AAC1E,UAAM,KAAK,uBAAuB,OAAO,cAAc;AACvD,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,IAAI,OAAO,CAAC;AAClB,UAAI,OAAO,EAAE;AACb,UAAI,eAAe;AACnB,UAAI,OAAO;AACX,UAAI,YAAY;AAChB,UAAI,KAAK,iBAAiB,QAAQ;AAChC,uBAAe,KAAK;AAAA,MACtB,WAAW,KAAK,cAAc,UAAU,qBAAqB,UAAU,iBAAiB,gBAAgB,IAAI,KAAK,SAAS,MAAM,QAAQ;AACtI,oBAAY,KAAK;AACjB,eAAO,iBAAiB,gBAAgB,IAAI,SAAS;AACrD,uBAAe,KAAK;AACpB,eAAO,KAAK;AAAA,MACd;AACA,YAAM,WAAW,KAAK,UAAU,YAAY;AAC5C,YAAM,WAAW,KAAK,WAAW,KAAK;AACtC,UAAI,KAAK;AACT,UAAI,EAAE,aAAa;AACjB,cAAM,EAAE;AAAA,MACV,WAAW,EAAE,gBAAgB,KAAK,EAAE,eAAe,OAAO,SAAS,GAAG,cAAc;AAClF,cAAM,aAAa,EAAE,UAAU,cAAc,EAAE,WAAW,IAAI;AAC9D,cAAM;AACN,eAAO,KAAK,EAAE,eAAe,OAAO,SAAS,GAAG;AAAA,MAClD;AACA,YAAM;AACN,UAAI,EAAE,OAAO;AACX,cAAM,gBAAgB,QAAQ;AAAA,MAChC,WAAW,WAAW;AACpB,cAAM,4CAA4C,EAAE,SAAS,aAAa,QAAQ;AAAA,MACpF,OAAO;AACL,cAAM,YAAY,QAAQ;AAAA,MAC5B;AACA,YAAM;AACN,YAAM,KAAK,EAAE;AACb,YAAM,KAAK,MAAM,EAAE,OAAO,WAAW,OAAO,OAAO,KAAK;AAAA,IAC1D;AACA,UAAM,KAAK,GAAG;AACd,WAAO,MAAM,KAAK,IAAI;AAAA,EACxB;AACF;AACA,IAAM,WAAN,MAAe;AAAA,EACb,YAAY,MAAM,MAAM,UAAU,OAAO,WAAW;AAClD,kBAAc,MAAM,MAAM;AAC1B,kBAAc,MAAM,MAAM;AAC1B,kBAAc,MAAM,UAAU;AAC9B,kBAAc,MAAM,OAAO;AAC3B,kBAAc,MAAM,WAAW;AAC/B,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,YAAY;AAAA,EACnB;AACF;AACA,IAAM,YAAN,MAAM,WAAU;AAAA;AAAA,EAEd,YAAY,QAAQ;AAElB,kBAAc,MAAM,SAAS;AAE7B,kBAAc,MAAM,WAAW;AAE/B,kBAAc,MAAM,kBAAkB;AACtC,SAAK,UAAU,CAAC;AAChB,SAAK,YAA4B,oBAAI,IAAI;AACzC,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA,EAEA,OAAO,SAAS,MAAM,OAAO;AAC3B,UAAM,YAAY,IAAI,WAAU,KAAK;AACrC,cAAU,UAAU,KAAK,IAAI,CAAC,eAAe,WAAW,SAAS,YAAY,KAAK,CAAC;AACnF,eAAW,UAAU,UAAU,SAAS;AACtC,gBAAU,UAAU,IAAI,OAAO,YAAY,GAAG,MAAM;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,WAAO,KAAK,QAAQ,IAAI,CAAC,eAAe,WAAW,OAAO,CAAC;AAAA,EAC7D;AAAA;AAAA,EAEA,sBAAsB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,YAAY,IAAI;AACd,eAAW,cAAc,KAAK,SAAS;AACrC,SAAG,YAAY,CAAC;AAChB,iBAAW,QAAQ,WAAW,YAAY,GAAG;AAC3C,aAAK,YAAY,IAAI,CAAC;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,eAAW,cAAc,KAAK,SAAS;AACrC,YAAM,OAAO,aAAa,WAAW,YAAY,EAAE,QAAQ;AAC3D,iBAAW,QAAQ,IAAI;AACvB,UAAI,IAAI;AACR,iBAAW,QAAQ,WAAW,YAAY,GAAG;AAC3C,aAAK,QAAQ,OAAO,OAAO,CAAC;AAC5B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB,UAAU,GAAG,GAAG;AACjC,eAAW,UAAU,KAAK,SAAS;AACjC,UAAI,OAAO,UAAU,GAAG;AACtB,cAAM,WAAW,OAAO,QAAQ,UAAU,GAAG,CAAC;AAC9C,YAAI,aAAa,QAAQ;AACvB,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,OAAN,MAAW;AAAA;AAAA,EAET,YAAY,QAAQ;AAElB,kBAAc,MAAM,OAAO;AAE3B,kBAAc,MAAM,YAAY;AAEhC,kBAAc,MAAM,QAAQ;AAE5B,kBAAc,MAAM,UAAU;AAE9B,kBAAc,MAAM,MAAM;AAE1B,kBAAc,MAAM,MAAM;AAE1B,kBAAc,MAAM,WAAW;AAC/B,SAAK,QAAQ;AACb,SAAK,aAAa,CAAC;AACnB,SAAK,WAAW,CAAC;AACjB,SAAK,OAAO,KAAK,MAAM;AACvB,SAAK,YAA4B,oBAAI,IAAI;AACzC,SAAK,OAAO;AAAA,EACd;AAAA,EACA,QAAQ;AACN,QAAI,KAAK,KAAK,WAAW;AACzB,QAAI,OAAO,QAAQ;AACjB,aAAO;AAAA,IACT;AACA,SAAK,KAAK,MAAM,aAAa;AAC7B,SAAK,MAAM,EAAE;AACb,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,UAAU;AACR,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,WAAW,QAAQ;AAC1B,aAAO,KAAK,MAAM,0BAA0B,IAAI,YAAY,OAAO,YAAY;AAAA,IACjF,OAAO;AACL,aAAO,YAAY,KAAK,KAAK,OAAO,eAAe,CAAC;AAAA,IACtD;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,OAAO,UAAU;AAChC,SAAK,UAAU,IAAI,OAAO,QAAQ;AAAA,EACpC;AAAA,EACA,oBAAoB,OAAO;AACzB,SAAK,UAAU,OAAO,KAAK;AAAA,EAC7B;AAAA;AAAA,EAEA,MAAM,IAAI;AACR,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA;AAAA,EAEA,UAAU,OAAO,QAAQ;AACvB,QAAI,KAAK,UAAU,IAAI,KAAK,GAAG;AAC7B,WAAK,UAAU,IAAI,KAAK,EAAE,MAAM;AAAA,IAClC;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ,MAAM;AACZ,QAAI,MAAM,KAAK,WAAW,IAAI;AAC9B,QAAI,QAAQ,QAAQ;AAClB,YAAM,YAAY,KAAK,wBAAwB,EAAE,aAAa,IAAI;AAClE,UAAI,cAAc,QAAQ;AACxB,cAAM,KAAK,MAAM,aAAa,SAAS;AAAA,MACzC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,YAAY,IAAI,OAAO;AACrB,OAAG,MAAM,KAAK;AACd;AACA,eAAW,QAAQ,KAAK,UAAU;AAChC,WAAK,YAAY,IAAI,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,SAAS,MAAM;AACb,QAAI,IAAI;AACR,eAAW,QAAQ,KAAK,UAAU;AAChC,UAAI,UAAU;AACd,UAAI,KAAK,QAAQ,MAAM,OAAO;AAC5B,mBAAW,OAAO;AAAA,MACpB,WAAW,KAAK,QAAQ,MAAM,UAAU;AACtC,mBAAW,QAAQ;AAAA,MACrB,WAAW,KAAK,QAAQ,MAAM,OAAO;AACnC,mBAAW,OAAO;AAAA,MACpB;AACA,WAAK,OAAO;AACZ,WAAK,SAAS,OAAO;AACrB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,UAAU,QAAQ;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA,EAEA,QAAQ,MAAM;AACZ,SAAK,OAAO;AAAA,EACd;AAAA;AAAA,EAEA,QAAQ,MAAM;AACZ,SAAK,OAAO;AAAA,EACd;AAAA;AAAA,EAEA,UAAU,QAAQ;AAChB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA,EAEA,YAAY,OAAO;AACjB,SAAK,WAAW,WAAW;AAAA,EAC7B;AAAA;AAAA,EAEA,mBAAmB,UAAU,UAAU,GAAG,GAAG;AAC3C,QAAI;AACJ,QAAI,KAAK,KAAK,SAAS,GAAG,CAAC,GAAG;AAC5B,UAAI,KAAK,MAAM,mBAAmB,QAAQ,MAAM,QAAQ;AACtD,cAAM,KAAK,MAAM,mBAAmB,QAAQ,EAAE,QAAQ,UAAU,GAAG,CAAC;AAAA,MACtE,OAAO;AACL,cAAM,KAAK,QAAQ,UAAU,GAAG,CAAC;AACjC,YAAI,QAAQ,QAAQ;AAClB,cAAI,KAAK,SAAS,WAAW,GAAG;AAC9B,uBAAW,SAAS,KAAK,UAAU;AACjC,oBAAM,MAAM,mBAAmB,UAAU,UAAU,GAAG,CAAC;AACvD,kBAAI,QAAQ,QAAQ;AAClB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,QAAQ,UAAU,GAAG,GAAG;AACtB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,YAAY,UAAU,UAAU;AAC9B,QAAI,YAAY,MAAM;AACpB,UAAI,SAAS,aAAa,aAAa,UAAU,SAAS,KAAK,aAAa,MAAM,OAAO;AACvF,eAAO;AAAA,MACT;AACA,UAAI,SAAS,aAAa,aAAa,UAAU,SAAS,QAAQ,MAAM,YAAY,SAAS,QAAQ,MAAM,QAAQ;AACjH,eAAO;AAAA,MACT;AACA,UAAI,SAAS,aAAa,aAAa,UAAU,SAAS,KAAK,eAAe,MAAM,OAAO;AACzF,eAAO;AAAA,MACT;AACA,UAAI,KAAK,MAAM,eAAe,GAAG;AAC/B,eAAO,KAAK,MAAM,eAAe,EAAE,UAAU,QAAQ;AAAA,MACvD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,YAAY,WAAW;AACrB,UAAM,MAAM,KAAK,SAAS,QAAQ,SAAS;AAC3C,QAAI,QAAQ,IAAI;AACd,WAAK,SAAS,OAAO,KAAK,CAAC;AAAA,IAC7B;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,SAAS,WAAW,KAAK;AACvB,QAAI,OAAO,MAAM;AACf,WAAK,SAAS,OAAO,KAAK,GAAG,SAAS;AAAA,IACxC,OAAO;AACL,WAAK,SAAS,KAAK,SAAS;AAC5B,YAAM,KAAK,SAAS,SAAS;AAAA,IAC/B;AACA,cAAU,SAAS;AACnB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,YAAY;AACV,SAAK,WAAW,CAAC;AAAA,EACnB;AAAA;AAAA,EAEA,kBAAkB,QAAQ;AACxB,QAAI,UAAU,MAAM;AAClB,eAAS,CAAC;AAAA,IACZ;AACA,WAAO,KAAK,KAAK,kBAAkB,MAAM;AAAA,EAC3C;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,KAAK,UAAU,KAAK,YAAY,QAAQ,GAAG;AAAA,EACpD;AACF;AACA,IAAM,WAAW,MAAMC,kBAAiB,KAAK;AAAA;AAAA,EAE3C,YAAY,OAAO,MAAM,aAAa,MAAM;AAC1C,UAAM,KAAK;AAEX,kBAAc,MAAM,WAAW,KAAK,MAAM,CAAC;AAE3C,kBAAc,MAAM,iBAAiB;AAErC,kBAAc,MAAM,UAAU;AAE9B,kBAAc,MAAM,cAAc;AAElC,kBAAc,MAAM,OAAO;AAE3B,kBAAc,MAAM,SAAS;AAE7B,kBAAc,MAAM,UAAU;AAE9B,kBAAc,MAAM,WAAW;AAE/B,kBAAc,MAAM,YAAY;AAChC,SAAK,QAAQ,CAAC;AACd,SAAK,kBAAkB;AACvB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,IAAAA,UAAS,qBAAqB,SAAS,MAAM,KAAK,UAAU;AAC5D,QAAI,eAAe,MAAM;AACvB,YAAM,QAAQ,IAAI;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,SAAS,MAAM,OAAO,aAAa,MAAM;AAC9C,UAAM,gBAAgB,IAAIA,UAAS,OAAO,MAAM,UAAU;AAC1D,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,WAAO,KAAK,QAAQ,MAAM;AAAA,EAC5B;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,QAAQ,UAAU;AAAA,EAChC;AAAA,EACA,eAAe;AACb,WAAO,KAAK,QAAQ,WAAW;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,kBAAkB,YAAY;AACrC,aAAO,KAAK,OAAO,YAAY;AAAA,IACjC;AACA,WAAO,MAAM;AAAA,EACf;AAAA,EACA,YAAY;AACV,UAAM,eAAe,KAAK,MAAM,cAAc,EAAE,IAAI,KAAK,YAAY,CAAC;AACtE,QAAI,cAAc;AAChB,aAAO,aAAa;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,YAAY,MAAM,MAAM;AAAA,EACtC;AAAA,EACA,aAAa;AACX,WAAO,KAAK,UAAU,EAAE,gBAAgB,MAAM;AAAA,EAChD;AAAA,EACA,UAAU;AACR,WAAO,KAAK,QAAQ,MAAM;AAAA,EAC5B;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,QAAQ,aAAa;AAAA,EACnC;AAAA,EACA,eAAe;AACb,WAAO,KAAK,QAAQ,WAAW;AAAA,EACjC;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,QAAQ,cAAc;AAAA,EACpC;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,QAAQ,kBAAkB;AAAA,EACxC;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,QAAQ,qBAAqB;AAAA,EAC3C;AAAA,EACA,eAAe;AACb,WAAO,KAAK,QAAQ,YAAY;AAAA,EAClC;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,QAAQ,cAAc;AAAA,EACpC;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,QAAQ,qBAAqB;AAAA,EAC3C;AAAA,EACA,eAAe;AACb,WAAO,KAAK,QAAQ,WAAW;AAAA,EACjC;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,QAAQ,kBAAkB;AAAA,EACxC;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,QAAQ,iBAAiB;AAAA,EACvC;AAAA,EACA,yBAAyB;AACvB,WAAO,KAAK,QAAQ,sBAAsB;AAAA,EAC5C;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,QAAQ,UAAU;AAAA,EAChC;AAAA,EACA,eAAe;AACb,WAAO,KAAK,QAAQ,WAAW;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,QAAQ,UAAU;AAAA,EAChC;AAAA,EACA,eAAe;AACb,WAAO,KAAK,QAAQ,WAAW;AAAA,EACjC;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,SAAS;AACP,UAAM,OAAO,CAAC;AACd,IAAAA,UAAS,qBAAqB,OAAO,MAAM,KAAK,UAAU;AAC1D,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,KAAK,iBAAiB;AACxB,WAAK,aAAa,KAAK,gBAAgB;AACvC,WAAK,YAAY,KAAK,gBAAgB;AAAA,IACxC;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB;AACtB,QAAI,KAAK,WAAW;AAClB,4BAAsB,MAAM;AAC1B,YAAI,KAAK,iBAAiB;AACxB,cAAI,KAAK,WAAW;AAClB,iBAAK,gBAAgB,YAAY,KAAK;AACtC,iBAAK,gBAAgB,aAAa,KAAK;AAAA,UACzC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ,MAAM;AACZ,QAAI,CAAC,KAAK,OAAO,KAAK,IAAI,GAAG;AAC3B,WAAK,UAAU,UAAU,EAAE,KAAK,CAAC;AACjC,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA;AAAA,EAEA,WAAW,SAAS;AAClB,QAAI,YAAY,KAAK,SAAS;AAC5B,WAAK,UAAU;AACf,WAAK,UAAU,cAAc,EAAE,QAAQ,CAAC;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,aAAa,WAAW;AACtB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,cAAc,YAAY;AACxB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,WAAW,MAAM;AACf,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,YAAY,OAAO;AACjB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,mBAAmB,SAAS;AAC1B,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA,EAEA,gBAAgB,MAAM;AACpB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA,EAEA,yBAAyB;AACvB,UAAM,UAAU,KAAK,QAAQ,SAAS;AACtC,QAAI,YAAY,QAAQ;AACtB,aAAO;AAAA,IACT;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,QAAQ,MAAM;AACZ,SAAK,WAAW,OAAO;AAAA,EACzB;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,OAAO,OAAO,IAAI;AACvB,SAAK,UAAU,SAAS,CAAC,CAAC;AAAA,EAC5B;AAAA;AAAA,EAEA,YAAY,MAAM;AAChB,IAAAA,UAAS,qBAAqB,OAAO,MAAM,KAAK,UAAU;AAAA,EAC5D;AAAA;AAAA,EAEA,0BAA0B;AACxB,WAAOA,UAAS;AAAA,EAClB;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,SAAK,WAAW,cAAc;AAAA,EAChC;AAAA;AAAA,EAEA,gBAAgB,QAAQ;AACtB,SAAK,WAAW,eAAe;AAAA,EACjC;AAAA;AAAA,EAEA,OAAO,0BAA0B;AAC/B,WAAOA,UAAS;AAAA,EAClB;AAAA;AAAA,EAEA,OAAO,6BAA6B;AAClC,UAAM,uBAAuB,IAAI,qBAAqB;AACtD,yBAAqB,IAAI,QAAQA,UAAS,MAAM,IAAI,EAAE,QAAQ,UAAU,MAAM,EAAE,SAAS;AACzF,yBAAqB,IAAI,MAAM,MAAM,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MAC/D;AAAA,IACF;AACA,yBAAqB,IAAI,QAAQ,eAAe,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MAC1E;AAAA,IACF;AACA,yBAAqB,IAAI,WAAW,MAAM,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MACpE;AAAA,IACF;AACA,yBAAqB,IAAI,YAAY,MAAM,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MACrE;AAAA,IACF;AACA,yBAAqB,IAAI,aAAa,MAAM,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MACtE;AAAA,IACF;AACA,yBAAqB,IAAI,UAAU,MAAM,EAAE,QAAQ,KAAK,EAAE;AAAA,MACxD;AAAA,IACF;AACA,yBAAqB,IAAI,mBAAmB,MAAM,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MAC5E;AAAA,IACF;AACA,yBAAqB,IAAI,uBAAuB,KAAK,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MAChF;AAAA,IACF;AACA,yBAAqB,aAAa,eAAe,gBAAgB,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MAC5F;AAAA,IACF;AACA,yBAAqB,aAAa,aAAa,cAAc,EAAE,QAAQ,YAAY,EAAE;AAAA,MACnF;AAAA,IACF;AACA,yBAAqB,aAAa,cAAc,eAAe,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MAC1F;AAAA,IACF;AACA,yBAAqB,aAAa,gBAAgB,iBAAiB,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MAC9F;AAAA,IACF;AACA,yBAAqB,aAAa,aAAa,cAAc,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MACvF;AAAA,IACF;AACA,yBAAqB,aAAa,oBAAoB,qBAAqB,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MACrG;AAAA,IACF;AACA,yBAAqB,aAAa,QAAQ,SAAS,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MAC7E;AAAA,IACF;AACA,yBAAqB,aAAa,wBAAwB,yBAAyB,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MAC9G;AAAA,IACF;AACA,yBAAqB,aAAa,gBAAgB,iBAAiB,EAAE,QAAQ,UAAU,OAAO,EAAE,SAAS,aAAa,EAAE;AAAA,MACtH;AAAA,IACF;AACA,yBAAqB,aAAa,oBAAoB,qBAAqB,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MACtG;AAAA,IACF;AACA,yBAAqB,aAAa,uBAAuB,wBAAwB,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MAC5G;AAAA;AAAA,IAEF;AACA,yBAAqB,aAAa,eAAe,gBAAgB,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MAC3F;AAAA,IACF;AACA,yBAAqB,aAAa,gBAAgB,iBAAiB,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MAC7F;AAAA,IACF;AACA,yBAAqB,aAAa,YAAY,aAAa,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MACrF;AAAA,IACF;AACA,yBAAqB,aAAa,aAAa,cAAc,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MACvF;AAAA,IACF;AACA,yBAAqB,aAAa,YAAY,aAAa,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MACrF;AAAA,IACF;AACA,yBAAqB,aAAa,aAAa,cAAc,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MACvF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,cAAc,UAAU,QAAQ,KAAK;AAErC,cAAc,UAAU,wBAAwB,SAAS,2BAA2B,CAAC;AACrF,IAAI,UAAU;AACd,SAAS,YAAY;AACnB,QAAM,UAAU,OAAO,WAAW,eAAe,OAAO,cAAc,OAAO,WAAW,oCAAoC,EAAE;AAC9H,SAAO;AACT;AACA,SAAS,iBAAiB,QAAQ,MAAM,WAAW;AACjD,MAAI,iBAAiB;AACrB,QAAM,eAAe,KAAK,QAAQ;AAClC,QAAM,OAAO,KAAK,QAAQ;AAC1B,MAAI,cAAc,QAAQ;AACxB,gBAAY;AAAA,EACd;AACA,MAAI,mBAAmB,UAAU,KAAK,QAAQ,MAAM,QAAQ;AAC1D,QAAI,cAAc,GAAG;AACnB,2BAAiC,wBAAI,OAAO,EAAE,OAAO,EAAE,OAAO,OAAO,QAAQ,OAAO,WAAW,YAAY,YAAY,OAAO,GAAG,KAAK,KAAK,QAAQ,GAAG,KAAK,iBAAiB,CAAC;AAAA,IAC/K,OAAO;AACL,2BAAiC,wBAAI,OAAO,EAAE,OAAO,EAAE,OAAO,OAAO,QAAQ,MAAM,GAAG,KAAK,KAAK,QAAQ,GAAG,KAAK,iBAAiB,CAAC;AAAA,IACpI;AAAA,EACF;AACA,QAAM,UAAU,CAAC;AACjB,QAAM,cAAc,EAAE,SAAS,gBAAgB,SAAS,cAAc,MAAM,QAAQ;AACpF,SAAO,aAAa,MAAM,WAAW;AACrC,OAAK,gBAAgB,YAAY,IAAI;AACrC,SAAO;AACT;AACA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,WAAW;AACf,MAAI,MAAM,uBAAuB,YAAY;AAC3C,QAAI,MAAM,YAAY,WAAW,KAAK,MAAM,WAAW,MAAM,UAAU,MAAM,WAAW,MAAM,UAAU;AACtG,iBAAW;AAAA,IACb;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,uBAAuB,QAAQ,iBAAiB;AACvD,QAAM,UAAU;AAAA,IACd,GAAG,qBAAqB,UAAU,eAAe;AAAA,IACjD,GAAG,qBAAqB,WAAW,eAAe;AAAA,EACpD;AACA,aAAW,UAAU,SAAS;AAC5B,WAAO,MAAM,gBAAgB,SAAS,SAAS;AAAA,EACjD;AACF;AACA,SAAS,qBAAqB,KAAK,iBAAiB;AAClD,SAAO,CAAC,GAAG,gBAAgB,qBAAqB,GAAG,CAAC;AACtD;AACA,SAAS,UAAU,KAAK,OAAO,MAAM,SAAS,YAAY;AACxD,QAAM,eAAe;AACrB,QAAM,cAAc,CAAC,OAAO;AAC1B,OAAG,eAAe;AAClB,SAAK,GAAG,SAAS,GAAG,OAAO;AAAA,EAC7B;AACA,QAAM,gBAAgB,CAAC,OAAO;AAC5B,OAAG,eAAe;AAClB,eAAW;AAAA,EACb;AACA,QAAM,YAAY,MAAM;AACtB,QAAI,oBAAoB,eAAe,WAAW;AAClD,QAAI,oBAAoB,aAAa,SAAS;AAC9C,QAAI,oBAAoB,iBAAiB,aAAa;AACtD,YAAQ;AAAA,EACV;AACA,MAAI,iBAAiB,eAAe,WAAW;AAC/C,MAAI,iBAAiB,aAAa,SAAS;AAC3C,MAAI,iBAAiB,iBAAiB,aAAa;AACrD;AACA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,gBAAgB,SAAS;AAC3B,WAAO,KAAK,eAAe;AAAA,EAC7B,WAAW,gBAAgB,YAAY;AACrC,eAAW,SAAS,KAAK,YAAY,GAAG;AACtC,UAAI,MAAM,eAAe,MAAM,OAAO;AACpC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,QAAQ,QAAQ;AACxC,QAAM,cAAc,OAAO,aAAa,OAAO;AAC/C,QAAM,cAAc,OAAO,aAAa,OAAO;AAC/C,MAAI,gBAAgB,YAAa,QAAO;AACxC,MAAI,aAAa;AACf,WAAO,aAAa,SAAS,WAAW;AAAA,EAC1C,OAAO;AACL,WAAO,gBAAgB,OAAO;AAAA,EAChC;AACA,SAAO;AACT;AACA,SAAS,WAAW;AAClB,QAAM,YAAY,UAAU;AAC5B,SAAO,UAAU,SAAS,QAAQ,KAAK,CAAC,UAAU,SAAS,QAAQ,KAAK,CAAC,UAAU,SAAS,UAAU;AACxG;AACA,SAAS,oBAAoB,QAAQ,cAAc;AACjD,MAAI,WAAW,WAAW,kBAAkB,cAAc,kBAAkB,aAAa;AACvF,UAAM,gBAAgB,OAAO,YAAY;AACzC,QAAI,kBAAkB,IAAI;AACxB,UAAI,iBAAiB,iBAAiB,OAAO,YAAY,EAAE,SAAS,GAAG;AACrE,YAAI,gBAAgB,OAAO,YAAY,EAAE,QAAQ;AAC/C,iBAAO,YAAY,OAAO,YAAY,EAAE,SAAS,CAAC;AAAA,QACpD;AAAA,MACF,WAAW,eAAe,eAAe;AACvC,eAAO,YAAY,gBAAgB,CAAC;AAAA,MACtC,WAAW,eAAe,cAAe;AAAA,WACpC;AACH,eAAO,YAAY,EAAE;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,aAAa;AACpB,UAAQ,4BAA4B,OAAO;AAAA,IACzC;AAAA,IACA,CAAC,OAAO,IAAI,OAAO,gBAAgB,IAAI,WAAW,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,IAAI,GAAG,SAAS,EAAE;AAAA,EACrF;AACF;AACA,IAAM,cAAc,MAAMC,qBAAoB,KAAK;AAAA;AAAA,EAEjD,YAAY,OAAO,MAAM;AACvB,UAAM,KAAK;AAEX,kBAAc,MAAM,gBAAgB,KAAK,MAAM,CAAC;AAEhD,kBAAc,MAAM,eAAe,KAAK,MAAM,CAAC;AAE/C,kBAAc,MAAM,qBAAqB;AAEzC,kBAAc,MAAM,oBAAoB;AAExC,kBAAc,MAAM,qBAAqB;AAEzC,kBAAc,MAAM,oBAAoB;AACxC,SAAK,sBAAsB;AAC3B,SAAK,qBAAqB;AAC1B,SAAK,sBAAsB;AAC3B,SAAK,qBAAqB;AAC1B,IAAAA,aAAY,qBAAqB,SAAS,MAAM,KAAK,UAAU;AAC/D,UAAM,QAAQ,IAAI;AAAA,EACpB;AAAA;AAAA,EAEA,OAAO,SAAS,MAAM,OAAO,cAAc;AACzC,UAAM,gBAAgB,IAAIA,aAAY,OAAO,IAAI;AACjD,QAAI,KAAK,YAAY,MAAM;AACzB,iBAAW,aAAa,KAAK,UAAU;AACrC,cAAM,QAAQ,QAAQ,SAAS,WAAW,KAAK;AAC/C,sBAAc,SAAS,KAAK;AAAA,MAC9B;AAAA,IACF;AACA,QAAI,cAAc,SAAS,WAAW,GAAG;AACvC,oBAAc,YAAY,EAAE;AAAA,IAC9B;AACA,QAAI,KAAK,aAAa,KAAK,cAAc,MAAM;AAC7C,mBAAa,kBAAkB;AAAA,IACjC;AACA,QAAI,KAAK,UAAU,KAAK,WAAW,MAAM;AACvC,mBAAa,eAAe;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,WAAO,KAAK,QAAQ,MAAM;AAAA,EAC5B;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,QAAQ,kBAAkB;AAAA,EACxC;AAAA,EACA,cAAc;AACZ,UAAM,WAAW,KAAK,WAAW;AACjC,QAAI,aAAa,QAAQ;AACvB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAChB,UAAM,WAAW,KAAK,YAAY;AAClC,QAAI,aAAa,IAAI;AACnB,aAAO,KAAK,SAAS,QAAQ;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY;AACV,WAAO,KAAK,QAAQ,QAAQ;AAAA,EAC9B;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,QAAQ,UAAU;AAAA,EAChC;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,QAAQ,WAAW;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,WAAW,aAAa;AACtB,QAAI,gBAAgB,YAAY,MAAM;AACpC,aAAO,KAAK,YAAY;AAAA,IAC1B,OAAO;AACL,aAAO,KAAK,aAAa;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,QAAQ,UAAU;AAAA,EAChC;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,QAAQ,WAAW;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,WAAW,aAAa;AACtB,QAAI,gBAAgB,YAAY,MAAM;AACpC,aAAO,KAAK,YAAY;AAAA,IAC1B,OAAO;AACL,aAAO,KAAK,aAAa;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,MAAM,mBAAmB,KAAK,YAAY,CAAC,MAAM;AAAA,EAC/D;AAAA,EACA,WAAW;AACT,WAAO,KAAK,MAAM,gBAAgB,KAAK,YAAY,CAAC,MAAM;AAAA,EAC5D;AAAA,EACA,0BAA0B;AACxB,WAAO,KAAK,QAAQ,uBAAuB;AAAA,EAC7C;AAAA,EACA,eAAe;AACb,WAAO,KAAK,QAAQ,YAAY;AAAA,EAClC;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,QAAQ,eAAe;AAAA,EACrC;AAAA,EACA,eAAe;AACb,WAAO,KAAK,QAAQ,YAAY;AAAA,EAClC;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,QAAQ,cAAc;AAAA,EACpC;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,QAAQ,gBAAgB;AAAA,EACtC;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,QAAQ,aAAa;AAAA,EACnC;AAAA,EACA,2BAA2B;AACzB,WAAO,KAAK,QAAQ,wBAAwB;AAAA,EAC9C;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,QAAQ,gBAAgB;AAAA,EACtC;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,QAAQ,eAAe;AAAA,EACrC;AAAA,EACA,uBAAuB;AACrB,WAAO,KAAK,QAAQ,oBAAoB;AAAA,EAC1C;AAAA,EACA,uBAAuB;AACrB,WAAO,KAAK,QAAQ,mBAAmB;AAAA,EACzC;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,QAAQ,aAAa;AAAA,EACnC;AAAA,EACA,SAAS;AACP,UAAM,OAAO,CAAC;AACd,IAAAA,aAAY,qBAAqB,OAAO,MAAM,KAAK,UAAU;AAC7D,SAAK,WAAW,KAAK,SAAS,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC;AAC3D,QAAI,KAAK,SAAS,GAAG;AACnB,WAAK,SAAS;AAAA,IAChB;AACA,QAAI,KAAK,YAAY,GAAG;AACtB,WAAK,YAAY;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,sBAAsB,KAAK,iBAAiB;AACjD,SAAK,qBAAqB,KAAK,gBAAgB;AAC/C,SAAK,sBAAsB,KAAK,iBAAiB;AACjD,SAAK,qBAAqB,KAAK,gBAAgB;AAC/C,eAAW,SAAS,KAAK,UAAU;AACjC,YAAM,IAAI;AACV,WAAK,qBAAqB,KAAK,IAAI,KAAK,oBAAoB,EAAE,YAAY,CAAC;AAC3E,WAAK,sBAAsB,KAAK,IAAI,KAAK,qBAAqB,EAAE,aAAa,CAAC;AAC9E,WAAK,qBAAqB,KAAK,IAAI,KAAK,oBAAoB,EAAE,YAAY,CAAC;AAC3E,WAAK,sBAAsB,KAAK,IAAI,KAAK,qBAAqB,EAAE,aAAa,CAAC;AAAA,IAChF;AACA,SAAK,uBAAuB,KAAK,aAAa;AAC9C,SAAK,uBAAuB,KAAK,aAAa;AAAA,EAChD;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,iBAAiB,GAAG;AAC3B,UAAI,KAAK,SAAS,EAAE,mBAAmB,KAAK,YAAY,CAAC,MAAM,MAAM;AACnE,eAAO;AAAA,MACT;AACA,UAAI,KAAK,UAAU,MAAM,KAAK,SAAS,EAAE,QAAQ,KAAK,YAAY,CAAC,KAAK,KAAK,SAAS,EAAE,QAAQ,KAAK,YAAY,CAAC,EAAE,YAAY,EAAE,WAAW,GAAG;AAC9I,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,eAAe,MAAM;AACnB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,gBAAgB,MAAM;AACpB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA,EAEA,UAAU,QAAQ;AAChB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA,EAEA,YAAY,OAAO;AACjB,SAAK,WAAW,WAAW;AAAA,EAC7B;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,OAAO,YAAY;AAAA,EACjC;AAAA;AAAA,EAEA,QAAQ,UAAU,GAAG,GAAG;AACtB,QAAI;AACJ,QAAI,aAAa,MAAM;AACrB,YAAM,eAAe,aAAa;AAClC,YAAM,cAAc,KAAK;AACzB,iBAAW,IAAI,SAAS,MAAM,aAAa,cAAc,IAAI,QAAQ,wBAAwB;AAAA,IAC/F,WAAW,KAAK,YAAY,MAAM,MAAM,kBAAkB,CAAC,gBAAgB,QAAQ,GAAG;AACpF,aAAO;AAAA,IACT,WAAW,KAAK,YAAY,SAAS,GAAG,CAAC,GAAG;AAC1C,UAAI,eAAe,aAAa;AAChC,UAAI,KAAK,MAAM,mBAAmB,KAAK,OAAO,YAAY,CAAC,MAAM,QAAQ;AACvE,uBAAe,aAAa,YAAY,KAAK,aAAa,GAAG,CAAC;AAAA,MAChE;AACA,YAAM,cAAc,aAAa,YAAY,KAAK,IAAI;AACtD,iBAAW,IAAI,SAAS,MAAM,aAAa,cAAc,IAAI,QAAQ,wBAAwB;AAAA,IAC/F,WAAW,KAAK,gBAAgB,QAAQ,KAAK,aAAa,SAAS,GAAG,CAAC,GAAG;AACxE,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,SAAS,WAAW,GAAG;AAC9B,YAAI,KAAK,aAAa,MAAM;AAC5B,aAAK,EAAE,IAAI;AACX,YAAI,EAAE,SAAS;AACf,UAAE,QAAQ;AAAA,MACZ,OAAO;AACL,YAAI,QAAQ,KAAK,SAAS,CAAC;AAC3B,YAAI,MAAM,WAAW;AACrB,aAAK,EAAE;AACP,YAAI,EAAE;AACN,YAAI,IAAI,KAAK,aAAa;AAC1B,YAAI,cAAc;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,kBAAQ,KAAK,SAAS,CAAC;AACvB,cAAI,MAAM,WAAW;AACrB,cAAI,EAAE,MAAM,IAAI;AACd,iBAAK,EAAE;AACP,gBAAI,KAAK,aAAa;AAAA,UACxB;AACA,wBAAc,EAAE,IAAI,EAAE,QAAQ;AAC9B,cAAI,KAAK,KAAK,IAAI,eAAe,EAAE,IAAI,KAAK,IAAI,EAAE,UAAU,GAAG;AAC7D,kBAAM,eAAe,aAAa;AAClC,kBAAM,cAAc,IAAI,KAAK,EAAE,IAAI,GAAG,EAAE,GAAG,GAAG,EAAE,MAAM;AACtD,gBAAI,KAAK,KAAK,IAAI,EAAE,KAAK,EAAE,IAAI,KAAK,KAAK,SAAS,GAAG;AACnD,yBAAW,IAAI,SAAS,MAAM,aAAa,cAAc,GAAG,QAAQ,wBAAwB;AAC5F;AAAA,YACF,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AACA,cAAI;AAAA,QACN;AAAA,MACF;AACA,UAAI,YAAY,QAAQ,EAAE,SAAS,IAAI,KAAK,KAAK,SAAS,GAAG;AAC3D,cAAM,eAAe,aAAa;AAClC,cAAM,cAAc,IAAI,KAAK,EAAE,SAAS,IAAI,GAAG,IAAI,GAAG,CAAC;AACvD,mBAAW,IAAI,SAAS,MAAM,aAAa,cAAc,KAAK,SAAS,QAAQ,QAAQ,wBAAwB;AAAA,MACjH;AAAA,IACF;AACA,QAAI,CAAC,SAAS,YAAY,UAAU,QAAQ,GAAG;AAC7C,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,OAAO,YAAY,IAAI;AAAA,EAC9B;AAAA;AAAA,EAEA,OAAO,MAAM;AACX,UAAM,eAAe,KAAK,YAAY,IAAI;AAC1C,SAAK,MAAM,KAAK;AAChB,wBAAoB,MAAM,YAAY;AAAA,EACxC;AAAA;AAAA,EAEA,KAAK,UAAU,UAAU,OAAO,QAAQ;AACtC,UAAM,eAAe;AACrB,QAAI,SAAS,UAAU;AACrB;AAAA,IACF;AACA,QAAI,aAAa,SAAS,UAAU;AACpC,QAAI,YAAY;AAChB,QAAI,eAAe,QAAQ;AACzB,kBAAY,WAAW,YAAY,QAAQ;AAC3C,UAAI,sBAAsB,cAAc,WAAW,YAAY,MAAM,WAAW;AAC9E,mBAAW,YAAY,EAAE;AAAA,MAC3B,OAAO;AACL,4BAAoB,YAAY,SAAS;AAAA,MAC3C;AAAA,IACF;AACA,QAAI,oBAAoB,WAAW,eAAe,QAAQ,YAAY,SAAS,QAAQ,GAAG;AACxF;AAAA,IACF;AACA,QAAI,iBAAiB,aAAa,QAAQ;AACxC,UAAI,YAAY;AAChB,UAAI,cAAc,IAAI;AACpB,oBAAY,KAAK,SAAS;AAAA,MAC5B;AACA,UAAI,oBAAoB,SAAS;AAC/B,aAAK,SAAS,UAAU,SAAS;AACjC,YAAI,UAAU,WAAW,SAAS,KAAK,gBAAgB,GAAG;AACxD,eAAK,YAAY,SAAS;AAAA,QAC5B;AAAA,MACF,WAAW,oBAAoB,SAAS;AACtC,iBAAS,YAAY,CAAC,OAAO,UAAU;AACrC,cAAI,iBAAiB,SAAS;AAC5B,iBAAK,SAAS,OAAO,SAAS;AAC9B;AAAA,UACF;AAAA,QACF,GAAG,CAAC;AAAA,MACN,OAAO;AACL,iBAAS,IAAI,GAAG,IAAI,SAAS,YAAY,EAAE,QAAQ,KAAK;AACtD,gBAAM,QAAQ,SAAS,YAAY,EAAE,CAAC;AACtC,eAAK,SAAS,OAAO,SAAS;AAC9B;AAAA,QACF;AACA,YAAI,KAAK,YAAY,MAAM,MAAM,KAAK,SAAS,SAAS,GAAG;AACzD,eAAK,YAAY,CAAC;AAAA,QACpB;AAAA,MACF;AACA,WAAK,MAAM,gBAAgB,MAAM,KAAK,OAAO,YAAY,CAAC;AAAA,IAC5D,OAAO;AACL,UAAI,WAAW;AACf,UAAI,oBAAoB,SAAS;AAC/B,cAAM,WAAW,KAAK,MAAM,kBAAkB;AAC9C,mBAAW,IAAIA,aAAY,KAAK,OAAO,WAAW,SAAS,QAAQ,IAAI,CAAC,CAAC;AACzE,iBAAS,SAAS,QAAQ;AAC1B,qBAAa;AAAA,MACf,WAAW,oBAAoB,SAAS;AACtC,cAAM,SAAS,KAAK,UAAU;AAC9B,YAAI,SAAS,eAAe,MAAM,OAAO,eAAe,MAAM,SAAS,eAAe,MAAM,OAAO,eAAe,KAAK,aAAa,aAAa,SAAS;AACxJ,gBAAM,OAAO,IAAI,QAAQ,KAAK,OAAO,KAAK,YAAY,GAAG,CAAC,CAAC;AAC3D,eAAK,SAAS,QAAQ;AACtB,qBAAW;AAAA,QACb;AAAA,MACF,OAAO;AACL,mBAAW;AAAA,MACb;AACA,YAAM,YAAY,KAAK;AACvB,YAAM,MAAM,UAAU,YAAY,EAAE,QAAQ,IAAI;AAChD,UAAI,UAAU,eAAe,MAAM,aAAa,aAAa;AAC3D,iBAAS,UAAU,KAAK,UAAU,IAAI,CAAC;AACvC,aAAK,UAAU,KAAK,UAAU,IAAI,CAAC;AACnC,kBAAU,SAAS,UAAU,MAAM,aAAa,SAAS;AAAA,MAC3D,OAAO;AACL,cAAM,SAAS,IAAI,QAAQ,KAAK,OAAO,KAAK,YAAY,GAAG,CAAC,CAAC;AAC7D,eAAO,UAAU,KAAK,UAAU,CAAC;AACjC,eAAO,SAAS,IAAI;AACpB,aAAK,UAAU,EAAE;AACjB,iBAAS,UAAU,EAAE;AACrB,eAAO,SAAS,UAAU,aAAa,SAAS;AAChD,kBAAU,YAAY,IAAI;AAC1B,kBAAU,SAAS,QAAQ,GAAG;AAAA,MAChC;AACA,UAAI,oBAAoBA,cAAa;AACnC,aAAK,MAAM,gBAAgB,UAAU,KAAK,YAAY,CAAC;AAAA,MACzD;AAAA,IACF;AACA,SAAK,MAAM,KAAK;AAAA,EAClB;AAAA;AAAA,EAEA,YAAY,MAAM;AAChB,IAAAA,aAAY,qBAAqB,OAAO,MAAM,KAAK,UAAU;AAAA,EAC/D;AAAA;AAAA,EAEA,0BAA0B;AACxB,WAAOA,aAAY;AAAA,EACrB;AAAA;AAAA,EAEA,OAAO,0BAA0B;AAC/B,WAAOA,aAAY;AAAA,EACrB;AAAA;AAAA,EAEA,OAAO,6BAA6B;AAClC,UAAM,uBAAuB,IAAI,qBAAqB;AACtD,yBAAqB,IAAI,QAAQA,aAAY,MAAM,IAAI,EAAE,QAAQ,UAAU,MAAM,EAAE,SAAS;AAC5F,yBAAqB,IAAI,MAAM,MAAM,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MAC/D;AAAA,IACF;AACA,yBAAqB,IAAI,UAAU,GAAG,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MAChE;AAAA,IACF;AACA,yBAAqB,IAAI,YAAY,CAAC,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MAChE;AAAA,IACF;AACA,yBAAqB,IAAI,QAAQ,MAAM,EAAE,QAAQ,UAAU,MAAM;AACjE,yBAAqB,IAAI,UAAU,MAAM,EAAE,QAAQ,KAAK,EAAE;AAAA,MACxD;AAAA,IACF;AACA,yBAAqB,aAAa,yBAAyB,6BAA6B,EAAE;AAAA,MACxF;AAAA,IACF;AACA,yBAAqB,aAAa,cAAc,kBAAkB,EAAE;AAAA,MAClE;AAAA,IACF;AACA,yBAAqB,aAAa,cAAc,kBAAkB,EAAE;AAAA,MAClE;AAAA,IACF;AACA,yBAAqB,aAAa,gBAAgB,oBAAoB,EAAE;AAAA,MACtE;AAAA,IACF;AACA,yBAAqB,aAAa,kBAAkB,sBAAsB,EAAE;AAAA,MAC1E;AAAA,IACF;AACA,yBAAqB,aAAa,eAAe,mBAAmB,EAAE;AAAA,MACpE;AAAA,IACF;AACA,yBAAqB,aAAa,0BAA0B,8BAA8B,EAAE;AAAA,MAC1F;AAAA,IACF;AACA,yBAAqB,aAAa,qBAAqB,yBAAyB,EAAE;AAAA,MAChF;AAAA,IACF;AACA,yBAAqB,aAAa,kBAAkB,sBAAsB,EAAE;AAAA,MAC1E;AAAA,IACF;AACA,yBAAqB,aAAa,YAAY,gBAAgB,EAAE;AAAA,MAC9D;AAAA,IACF;AACA,yBAAqB,aAAa,aAAa,iBAAiB,EAAE;AAAA,MAChE;AAAA,IACF;AACA,yBAAqB,aAAa,YAAY,gBAAgB,EAAE;AAAA,MAC9D;AAAA,IACF;AACA,yBAAqB,aAAa,aAAa,iBAAiB,EAAE;AAAA,MAChE;AAAA,IACF;AACA,yBAAqB,aAAa,iBAAiB,qBAAqB,EAAE;AAAA,MACxE;AAAA,IACF;AACA,yBAAqB,aAAa,eAAe,mBAAmB,EAAE;AAAA,MACpE;AAAA,IACF;AACA,yBAAqB,aAAa,iBAAiB,qBAAqB,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MACnG;AAAA,IACF;AACA,yBAAqB,aAAa,oBAAoB,wBAAwB,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MACzG;AAAA,IACF;AACA,yBAAqB,aAAa,sBAAsB,0BAA0B,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MAC7G;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,cAAc,aAAa,QAAQ,QAAQ;AAE3C,cAAc,aAAa,wBAAwB,YAAY,2BAA2B,CAAC;AAC3F,IAAI,aAAa;AACjB,IAAM,WAAW,MAAMC,kBAAiB,KAAK;AAAA;AAAA,EAE3C,YAAY,OAAO,UAAU,MAAM;AACjC,UAAM,KAAK;AAEX,kBAAc,MAAM,UAAU;AAE9B,kBAAc,MAAM,WAAW;AAE/B,kBAAc,MAAM,UAAU;AAE9B,kBAAc,MAAM,WAAW;AAE/B,kBAAc,MAAM,UAAU;AAC9B,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,IAAAA,UAAS,qBAAqB,SAAS,MAAM,KAAK,UAAU;AAC5D,SAAK,iBAAiB;AACtB,UAAM,QAAQ,IAAI;AAAA,EACpB;AAAA;AAAA,EAEA,OAAO,SAAS,MAAM,OAAO,cAAc;AACzC,UAAM,gBAAgB,IAAIA,UAAS,OAAO,aAAa,UAAU,IAAI;AACrE,QAAI,KAAK,YAAY,MAAM;AACzB,iBAAW,aAAa,KAAK,UAAU;AACrC,YAAI,UAAU,SAAS,WAAW,MAAM;AACtC,gBAAM,QAAQ,WAAW,SAAS,WAAW,OAAO,YAAY;AAChE,wBAAc,SAAS,KAAK;AAAA,QAC9B,OAAO;AACL,gBAAM,QAAQA,UAAS,SAAS,WAAW,OAAO,YAAY;AAC9D,wBAAc,SAAS,KAAK;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY;AACV,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,SAAS;AACP,UAAM,OAAO,CAAC;AACd,IAAAA,UAAS,qBAAqB,OAAO,MAAM,KAAK,UAAU;AAC1D,SAAK,WAAW,CAAC;AACjB,eAAW,SAAS,KAAK,UAAU;AACjC,WAAK,SAAS,KAAK,MAAM,OAAO,CAAC;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,UAAU,QAAQ;AAChB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA,EAEA,kBAAkB,OAAO;AACvB,UAAM,IAAI,KAAK,eAAe,MAAM,YAAY;AAChD,UAAM,IAAI,KAAK,YAAY;AAC3B,UAAM,KAAK,KAAK,MAAM,gBAAgB;AACtC,UAAM,KAAK,EAAE,CAAC,EAAE,QAAQ;AACxB,UAAM,KAAK,EAAE,EAAE,SAAS,CAAC,EAAE,QAAQ;AACnC,QAAI,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC;AACzD,UAAM,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC;AAC3D,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,YAAM,IAAI,EAAE,CAAC;AACb,QAAE,CAAC,KAAK,IAAI,EAAE,YAAY,IAAI,EAAE,aAAa;AAC7C,QAAE,CAAC,KAAK,IAAI,EAAE,YAAY,IAAI,EAAE,aAAa;AAC7C,UAAI,IAAI,GAAG;AACT,UAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAK;AAAA,MACV;AAAA,IACF;AACA,aAAS,IAAI,EAAE,SAAS,GAAG,KAAK,OAAO,KAAK;AAC1C,YAAM,IAAI,EAAE,CAAC;AACb,QAAE,CAAC,MAAM,IAAI,EAAE,YAAY,IAAI,EAAE,aAAa,KAAK;AACnD,QAAE,CAAC,MAAM,IAAI,EAAE,YAAY,IAAI,EAAE,aAAa,KAAK;AAAA,IACrD;AACA,QAAI,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC/C,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,oBAAoB,OAAO;AACzB,UAAM,IAAI,KAAK,eAAe,MAAM,YAAY;AAChD,UAAM,IAAI,KAAK,YAAY;AAC3B,UAAM,KAAK,KAAK,MAAM,gBAAgB;AACtC,UAAM,eAAe,CAAC;AACtB,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAI,EAAE,QAAQ;AACpB,YAAM,IAAI,IAAI,EAAE,QAAQ,EAAE;AAC1B,mBAAa,KAAK,CAAC;AACnB,aAAO;AAAA,IACT;AACA,UAAM,YAAY,EAAE,KAAK,EAAE,QAAQ;AACnC,UAAM,iBAAiB,IAAI,UAAU,IAAI,UAAU,KAAK;AACxD,WAAO,EAAE,cAAc,KAAK,cAAc;AAAA,EAC5C;AAAA;AAAA,EAEA,eAAe,OAAO,aAAa,cAAc,KAAK,eAAe;AACnE,UAAM,IAAI,KAAK,eAAe,MAAM,YAAY;AAChD,UAAM,IAAI,KAAK,YAAY;AAC3B,UAAM,KAAK,EAAE,KAAK;AAClB,UAAM,OAAO,IAAI,GAAG,YAAY,IAAI,GAAG,aAAa;AACpD,UAAM,QAAQ,CAAC,GAAG,YAAY;AAC9B,QAAI,cAAc,eAAe;AAC/B,UAAI,QAAQ,gBAAgB;AAC5B,UAAI,WAAW;AACf,UAAI,MAAM,KAAK,IAAI,QAAQ,MAAM;AAC/B,mBAAW,MAAM,KAAK,IAAI,QAAQ;AAClC,cAAM,KAAK,IAAI;AAAA,MACjB,OAAO;AACL,cAAM,KAAK,KAAK;AAAA,MAClB;AACA,eAAS,IAAI,QAAQ,GAAG,KAAK,GAAG,KAAK;AACnC,cAAM,IAAI,EAAE,CAAC;AACb,cAAM,IAAI,IAAI,EAAE,YAAY,IAAI,EAAE,aAAa;AAC/C,YAAI,MAAM,CAAC,IAAI,QAAQ,GAAG;AACxB,gBAAM,CAAC,KAAK;AACZ;AAAA,QACF,OAAO;AACL,mBAAS,MAAM,CAAC,IAAI;AACpB,gBAAM,CAAC,IAAI;AAAA,QACb;AAAA,MACF;AACA,eAAS,IAAI,QAAQ,GAAG,IAAI,EAAE,QAAQ,KAAK;AACzC,cAAM,IAAI,EAAE,CAAC;AACb,cAAM,IAAI,IAAI,EAAE,YAAY,IAAI,EAAE,aAAa;AAC/C,YAAI,MAAM,CAAC,IAAI,WAAW,GAAG;AAC3B,gBAAM,CAAC,KAAK;AACZ;AAAA,QACF,OAAO;AACL,sBAAY,IAAI,MAAM,CAAC;AACvB,gBAAM,CAAC,IAAI;AAAA,QACb;AAAA,MACF;AAAA,IACF,OAAO;AACL,UAAI,QAAQ,cAAc;AAC1B,UAAI,WAAW;AACf,UAAI,MAAM,QAAQ,CAAC,IAAI,QAAQ,MAAM;AACnC,mBAAW,MAAM,QAAQ,CAAC,IAAI,QAAQ;AACtC,cAAM,QAAQ,CAAC,IAAI;AAAA,MACrB,OAAO;AACL,cAAM,QAAQ,CAAC,KAAK;AAAA,MACtB;AACA,eAAS,IAAI,OAAO,IAAI,EAAE,QAAQ,KAAK;AACrC,cAAM,IAAI,EAAE,CAAC;AACb,cAAM,IAAI,IAAI,EAAE,YAAY,IAAI,EAAE,aAAa;AAC/C,YAAI,MAAM,CAAC,IAAI,QAAQ,GAAG;AACxB,gBAAM,CAAC,KAAK;AACZ;AAAA,QACF,OAAO;AACL,mBAAS,MAAM,CAAC,IAAI;AACpB,gBAAM,CAAC,IAAI;AAAA,QACb;AAAA,MACF;AACA,eAAS,IAAI,QAAQ,GAAG,KAAK,GAAG,KAAK;AACnC,cAAM,IAAI,EAAE,CAAC;AACb,cAAM,IAAI,IAAI,EAAE,YAAY,IAAI,EAAE,aAAa;AAC/C,YAAI,MAAM,CAAC,IAAI,WAAW,GAAG;AAC3B,gBAAM,CAAC,KAAK;AACZ;AAAA,QACF,OAAO;AACL,sBAAY,IAAI,MAAM,CAAC;AACvB,gBAAM,CAAC,IAAI;AAAA,QACb;AAAA,MACF;AAAA,IACF;AACA,UAAM,UAAU,MAAM,IAAI,CAAC,MAAM,KAAK,IAAI,KAAK,CAAC,IAAI,MAAM,GAAG;AAC7D,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,WAAW,aAAa;AACtB,QAAI,gBAAgB,YAAY,MAAM;AACpC,aAAO,KAAK,YAAY;AAAA,IAC1B,OAAO;AACL,aAAO,KAAK,aAAa;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,WAAW,aAAa;AACtB,QAAI,gBAAgB,YAAY,MAAM;AACpC,aAAO,KAAK,YAAY;AAAA,IAC1B,OAAO;AACL,aAAO,KAAK,aAAa;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,QAAI,QAAQ;AACZ,eAAW,SAAS,KAAK,UAAU;AACjC,YAAM,IAAI;AACV,QAAE,eAAe;AACjB,UAAI,KAAK,eAAe,MAAM,YAAY,MAAM;AAC9C,aAAK,aAAa,EAAE,aAAa;AACjC,aAAK,aAAa,EAAE,aAAa;AACjC,YAAI,CAAC,OAAO;AACV,eAAK,aAAa,KAAK,MAAM,gBAAgB;AAC7C,eAAK,aAAa,KAAK,MAAM,gBAAgB;AAAA,QAC/C;AACA,aAAK,WAAW,KAAK,IAAI,KAAK,UAAU,EAAE,YAAY,CAAC;AACvD,aAAK,WAAW,KAAK,IAAI,KAAK,UAAU,EAAE,YAAY,CAAC;AAAA,MACzD,OAAO;AACL,aAAK,YAAY,EAAE,YAAY;AAC/B,aAAK,YAAY,EAAE,YAAY;AAC/B,YAAI,CAAC,OAAO;AACV,eAAK,YAAY,KAAK,MAAM,gBAAgB;AAC5C,eAAK,YAAY,KAAK,MAAM,gBAAgB;AAAA,QAC9C;AACA,aAAK,YAAY,KAAK,IAAI,KAAK,WAAW,EAAE,aAAa,CAAC;AAC1D,aAAK,YAAY,KAAK,IAAI,KAAK,WAAW,EAAE,aAAa,CAAC;AAAA,MAC5D;AACA,cAAQ;AAAA,IACV;AAAA,EACF;AAAA;AAAA,EAEA,OAAO;AACL,QAAI,IAAI;AACR,WAAO,IAAI,KAAK,SAAS,QAAQ;AAC/B,YAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,UAAI,iBAAiBA,WAAU;AAC7B,cAAM,KAAK;AACX,cAAM,gBAAgB,MAAM,YAAY;AACxC,YAAI,cAAc,WAAW,GAAG;AAC9B,eAAK,YAAY,KAAK;AAAA,QACxB,WAAW,cAAc,WAAW,GAAG;AACrC,gBAAM,WAAW,cAAc,CAAC;AAChC,eAAK,YAAY,KAAK;AACtB,cAAI,oBAAoBA,WAAU;AAChC,gBAAI,mBAAmB;AACvB,kBAAM,mBAAmB,SAAS,YAAY;AAC9C,uBAAW,OAAO,kBAAkB;AAClC,oBAAM,cAAc;AACpB,kCAAoB,YAAY,UAAU;AAAA,YAC5C;AACA,qBAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAChD,oBAAM,cAAc,iBAAiB,CAAC;AACtC,0BAAY,UAAU,MAAM,UAAU,IAAI,YAAY,UAAU,IAAI,gBAAgB;AACpF,mBAAK,SAAS,aAAa,IAAI,CAAC;AAAA,YAClC;AAAA,UACF,OAAO;AACL,qBAAS,UAAU,MAAM,UAAU,CAAC;AACpC,iBAAK,SAAS,UAAU,CAAC;AAAA,UAC3B;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,WAAW,iBAAiB,cAAc,MAAM,YAAY,EAAE,WAAW,GAAG;AAC1E,YAAI,MAAM,wBAAwB,GAAG;AACnC,eAAK,YAAY,KAAK;AACtB,cAAI,UAAU,KAAK,MAAM,mBAAmB,KAAK,QAAQ,GAAG;AAC1D,iBAAK,MAAM,mBAAmB,QAAQ,KAAK,QAAQ;AAAA,UACrD;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,OAAO;AACL;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS,KAAK,MAAM,QAAQ,KAAK,QAAQ,KAAK,KAAK,SAAS,WAAW,GAAG;AAC5E,YAAM,WAAW,KAAK,MAAM,kBAAkB;AAC9C,UAAI,QAAQ,WAAW,SAAS,IAAI,CAAC;AACrC,cAAQ,EAAE,GAAG,OAAO,UAAU,GAAG;AACjC,YAAM,QAAQ,IAAI,WAAW,KAAK,OAAO,KAAK;AAC9C,WAAK,MAAM,gBAAgB,OAAO,KAAK,QAAQ;AAC/C,WAAK,SAAS,KAAK;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ,UAAU,GAAG,GAAG;AACtB,UAAM,KAAK,IAAI,KAAK,KAAK;AACzB,UAAM,KAAK,IAAI,KAAK,KAAK;AACzB,UAAM,IAAI,KAAK,KAAK;AACpB,UAAM,IAAI,KAAK,KAAK;AACpB,UAAM,SAAS;AACf,UAAM,OAAO;AACb,QAAI;AACJ,QAAI,KAAK,YAAY,MAAM,MAAM,kBAAkB,CAAC,gBAAgB,QAAQ,GAAG;AAC7E,aAAO;AAAA,IACT;AACA,QAAI,KAAK,MAAM,iBAAiB,KAAK,KAAK,WAAW,QAAQ;AAC3D,UAAI,IAAI,KAAK,KAAK,IAAI,UAAU,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,IAAI,MAAM;AACtE,cAAM,eAAe,aAAa;AAClC,cAAM,cAAc,aAAa,YAAY,KAAK,IAAI;AACtD,oBAAY,QAAQ,YAAY,QAAQ;AACxC,mBAAW,IAAI,SAAS,MAAM,aAAa,cAAc,IAAI,QAAQ,6BAA6B;AAAA,MACpG,WAAW,IAAI,KAAK,KAAK,SAAS,IAAI,UAAU,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,IAAI,MAAM;AACtF,cAAM,eAAe,aAAa;AAClC,cAAM,cAAc,aAAa,YAAY,KAAK,IAAI;AACtD,oBAAY,QAAQ,YAAY,QAAQ;AACxC,oBAAY,KAAK,YAAY;AAC7B,mBAAW,IAAI,SAAS,MAAM,aAAa,cAAc,IAAI,QAAQ,6BAA6B;AAAA,MACpG,WAAW,IAAI,KAAK,KAAK,IAAI,UAAU,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,IAAI,MAAM;AAC7E,cAAM,eAAe,aAAa;AAClC,cAAM,cAAc,aAAa,YAAY,KAAK,IAAI;AACtD,oBAAY,SAAS,YAAY,SAAS;AAC1C,mBAAW,IAAI,SAAS,MAAM,aAAa,cAAc,IAAI,QAAQ,6BAA6B;AAAA,MACpG,WAAW,IAAI,KAAK,KAAK,UAAU,IAAI,UAAU,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,IAAI,MAAM;AACvF,cAAM,eAAe,aAAa;AAClC,cAAM,cAAc,aAAa,YAAY,KAAK,IAAI;AACtD,oBAAY,SAAS,YAAY,SAAS;AAC1C,oBAAY,KAAK,YAAY;AAC7B,mBAAW,IAAI,SAAS,MAAM,aAAa,cAAc,IAAI,QAAQ,6BAA6B;AAAA,MACpG;AACA,UAAI,aAAa,QAAQ;AACvB,YAAI,CAAC,SAAS,YAAY,UAAU,QAAQ,GAAG;AAC7C,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,KAAK,UAAU,UAAU,OAAO;AAC9B,UAAM,eAAe;AACrB,UAAM,SAAS,SAAS,UAAU;AAClC,QAAI,QAAQ;AACV,aAAO,YAAY,QAAQ;AAAA,IAC7B;AACA,QAAI,WAAW,UAAU,kBAAkB,YAAY;AACrD,aAAO,YAAY,CAAC;AAAA,IACtB;AACA,QAAI,WAAW,UAAU,kBAAkB,YAAY;AACrD,aAAO,YAAY,EAAE;AAAA,IACvB;AACA,QAAI;AACJ,QAAI,oBAAoB,cAAc,oBAAoBA,WAAU;AAClE,aAAO;AACP,UAAI,gBAAgBA,aAAY,KAAK,eAAe,MAAM,KAAK,eAAe,MAAM,SAAS,eAAe,MAAM,KAAK,eAAe,KAAK,aAAa,aAAa,SAAS;AAC5K,eAAO,IAAIA,UAAS,KAAK,OAAO,KAAK,UAAU,CAAC,CAAC;AACjD,aAAK,SAAS,QAAQ;AAAA,MACxB;AAAA,IACF,OAAO;AACL,YAAM,WAAW,KAAK,MAAM,kBAAkB;AAC9C,aAAO,IAAI,WAAW,KAAK,OAAO,WAAW,SAAS,QAAQ,IAAI,CAAC,CAAC;AACpE,WAAK,SAAS,QAAQ;AAAA,IACxB;AACA,QAAI,OAAO,KAAK,SAAS,OAAO,CAAC,KAAK,UAAU;AAC9C,aAAO,MAAM,MAAM,UAAU;AAAA,IAC/B,GAAG,CAAC;AACJ,QAAI,SAAS,GAAG;AACd,aAAO;AAAA,IACT;AACA,SAAK,UAAU,OAAO,CAAC;AACvB,UAAM,OAAO,CAAC,KAAK,MAAM,0BAA0B;AACnD,QAAI,iBAAiB,aAAa,QAAQ;AACxC,UAAI,UAAU,IAAI;AAChB,aAAK,SAAS,MAAM,KAAK,SAAS,MAAM;AAAA,MAC1C,OAAO;AACL,aAAK,SAAS,MAAM,KAAK;AAAA,MAC3B;AAAA,IACF,WAAW,QAAQ,iBAAiB,aAAa,QAAQ,CAAC,QAAQ,iBAAiB,aAAa,KAAK;AACnG,WAAK,SAAS,MAAM,CAAC;AAAA,IACvB,WAAW,QAAQ,iBAAiB,aAAa,SAAS,CAAC,QAAQ,iBAAiB,aAAa,QAAQ;AACvG,WAAK,SAAS,IAAI;AAAA,IACpB,WAAW,QAAQ,iBAAiB,aAAa,OAAO,CAAC,QAAQ,iBAAiB,aAAa,MAAM;AACnG,YAAM,OAAO,IAAIA,UAAS,KAAK,OAAO,KAAK,UAAU,CAAC,CAAC;AACvD,YAAM,OAAO,IAAIA,UAAS,KAAK,OAAO,KAAK,UAAU,CAAC,CAAC;AACvD,WAAK,UAAU,EAAE;AACjB,WAAK,UAAU,EAAE;AACjB,iBAAW,SAAS,KAAK,UAAU;AACjC,aAAK,SAAS,KAAK;AAAA,MACrB;AACA,WAAK,UAAU;AACf,WAAK,SAAS,IAAI;AAClB,WAAK,SAAS,IAAI;AAClB,WAAK,SAAS,IAAI;AAAA,IACpB,WAAW,QAAQ,iBAAiB,aAAa,UAAU,CAAC,QAAQ,iBAAiB,aAAa,OAAO;AACvG,YAAM,OAAO,IAAIA,UAAS,KAAK,OAAO,KAAK,UAAU,CAAC,CAAC;AACvD,YAAM,OAAO,IAAIA,UAAS,KAAK,OAAO,KAAK,UAAU,CAAC,CAAC;AACvD,WAAK,UAAU,EAAE;AACjB,WAAK,UAAU,EAAE;AACjB,iBAAW,SAAS,KAAK,UAAU;AACjC,aAAK,SAAS,KAAK;AAAA,MACrB;AACA,WAAK,UAAU;AACf,WAAK,SAAS,IAAI;AAClB,WAAK,SAAS,IAAI;AAClB,WAAK,SAAS,IAAI;AAAA,IACpB;AACA,QAAI,gBAAgB,YAAY;AAC9B,WAAK,MAAM,gBAAgB,MAAM,KAAK,QAAQ;AAAA,IAChD;AACA,SAAK,MAAM,KAAK;AAAA,EAClB;AAAA;AAAA,EAEA,eAAe;AACb,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,0BAA0B;AACxB,WAAOA,UAAS;AAAA,EAClB;AAAA;AAAA,EAEA,YAAY,MAAM;AAChB,IAAAA,UAAS,qBAAqB,OAAO,MAAM,KAAK,UAAU;AAAA,EAC5D;AAAA;AAAA,EAEA,OAAO,0BAA0B;AAC/B,WAAOA,UAAS;AAAA,EAClB;AAAA;AAAA,EAEA,mBAAmB;AACjB,QAAI,MAAM;AACV,eAAW,KAAK,KAAK,UAAU;AAC7B,YAAM,OAAO;AACb,aAAO,KAAK,UAAU;AAAA,IACxB;AACA,QAAI,QAAQ,GAAG;AACb,YAAM;AAAA,IACR;AACA,eAAW,KAAK,KAAK,UAAU;AAC7B,YAAM,OAAO;AACb,WAAK,UAAU,KAAK,IAAI,MAAM,MAAM,KAAK,UAAU,IAAI,GAAG,CAAC;AAAA,IAC7D;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,6BAA6B;AAClC,UAAM,uBAAuB,IAAI,qBAAqB;AACtD,yBAAqB,IAAI,QAAQA,UAAS,MAAM,IAAI,EAAE,QAAQ,UAAU,MAAM,EAAE,SAAS;AACzF,yBAAqB,IAAI,MAAM,MAAM,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MAC/D;AAAA,IACF;AACA,yBAAqB,IAAI,UAAU,GAAG,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MAChE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,cAAc,UAAU,QAAQ,KAAK;AAErC,cAAc,UAAU,wBAAwB,SAAS,2BAA2B,CAAC;AACrF,IAAI,UAAU;AACd,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY,UAAU,MAAM;AAC1B,kBAAc,MAAM,WAAW;AAC/B,kBAAc,MAAM,SAAS;AAC7B,kBAAc,MAAM,OAAO;AAC3B,kBAAc,MAAM,SAAS;AAC7B,kBAAc,MAAM,OAAO;AAC3B,kBAAc,MAAM,kBAAkB;AACtC,kBAAc,MAAM,eAAe;AACnC,kBAAc,MAAM,uBAAuB;AAC3C,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,wBAAwB,CAAC,MAAM;AAAA,EACtC;AAAA,EACA,WAAW,IAAI;AACb,SAAK,KAAK,YAAY,IAAI,CAAC;AAAA,EAC7B;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,KAAK,OAAO;AACd,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA,EAEA,IAAI,OAAO,OAAO;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA,EAEA,IAAI,OAAO,OAAO;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA,EAEA,IAAI,KAAK,OAAO;AACd,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA,EAEA,IAAI,gBAAgB,OAAO;AACzB,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA,EAEA,IAAI,aAAa,OAAO;AACtB,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA,EAEA,IAAI,uBAAuB;AACzB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,qBAAqB,OAAO;AAC9B,SAAK,wBAAwB;AAAA,EAC/B;AAAA,EACA,SAAS;AACP,QAAI,KAAK,WAAW,KAAK,QAAQ,YAAY,MAAM;AACjD,WAAK,OAAO,IAAI;AAAA,QACd,KAAK,QAAQ;AAAA,QACb,KAAK,QAAQ;AAAA,QACb,KAAK,QAAQ;AAAA,QACb,KAAK,QAAQ;AAAA,MACf;AAAA,IACF;AACA,WAAO,EAAE,QAAQ,KAAK,KAAK,OAAO,GAAG,MAAM,KAAK,KAAK,OAAO,EAAE;AAAA,EAChE;AAAA,EACA,OAAO,SAAS,YAAY,OAAO,UAAU;AAC3C,UAAM,QAAQ,MAAM,cAAc,EAAE;AACpC,UAAM,OAAO,WAAW,OAAO,KAAK,SAAS,WAAW,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK,GAAG;AACnH,SAAK,KAAK,EAAE;AACZ,UAAM,eAAe,IAAI,cAAa,UAAU,IAAI;AACpD,iBAAa,OAAO,QAAQ,SAAS,WAAW,QAAQ,OAAO,YAAY;AAC3E,WAAO;AAAA,EACT;AACF;AACA,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,SAAS,MAAMC,QAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,cAAc;AAEZ,kBAAc,MAAM,YAAY;AAEhC,kBAAc,MAAM,OAAO;AAE3B,kBAAc,MAAM,iBAAiB;AAErC,kBAAc,MAAM,SAAS;AAE7B,kBAAc,MAAM,aAAa;AAEjC,kBAAc,MAAM,gBAAgB;AAEpC,kBAAc,MAAM,SAAS;AAE7B,kBAAc,MAAM,YAAY;AAChC,SAAK,aAAa,CAAC;AACnB,SAAK,QAAwB,oBAAI,IAAI;AACrC,SAAK,UAAU,IAAI,UAAU,IAAI;AACjC,SAAK,UAA0B,oBAAI,IAAI;AACvC,SAAK,aAAa,IAAI,aAAaA,QAAO,gBAAgB,KAAK,MAAM,CAAC;AACtE,SAAK,QAAQ,IAAIA,QAAO,gBAAgB,KAAK,UAAU;AACvD,SAAK,kBAAkB,CAAC;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,QAAQ;AACf,QAAI;AACJ,QAAI,YAAY;AAChB,YAAQ,OAAO,MAAM;AAAA,MACnB,KAAK,QAAQ,UAAU;AACrB,cAAM,UAAU,IAAI,QAAQ,MAAM,OAAO,KAAK,MAAM,IAAI;AACxD,cAAM,SAAS,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM;AAChD,YAAI,kBAAkB,cAAc,kBAAkB,cAAc,kBAAkB,SAAS;AAC7F,iBAAO,KAAK,SAAS,aAAa,UAAU,OAAO,KAAK,QAAQ,GAAG,OAAO,KAAK,OAAO,OAAO,KAAK,MAAM;AACxG,sBAAY;AAAA,QACd;AACA;AAAA,MACF;AAAA,MACA,KAAK,QAAQ,WAAW;AACtB,cAAM,WAAW,KAAK,MAAM,IAAI,OAAO,KAAK,QAAQ;AACpD,YAAI,oBAAoB,WAAW,oBAAoB,cAAc,oBAAoB,SAAS;AAChG,cAAI,aAAa,KAAK,mBAAmB,SAAS,YAAY,CAAC,GAAG;AAChE,kBAAM,aAAa,KAAK,QAAQ,IAAI,SAAS,YAAY,CAAC;AAC1D,uBAAW,kBAAkB;AAAA,UAC/B;AACA,gBAAM,SAAS,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM;AAChD,cAAI,kBAAkB,cAAc,kBAAkB,cAAc,kBAAkB,SAAS;AAC7F,mBAAO,KAAK,UAAU,aAAa,UAAU,OAAO,KAAK,QAAQ,GAAG,OAAO,KAAK,OAAO,OAAO,KAAK,MAAM;AAAA,UAC3G;AAAA,QACF;AACA,aAAK,mBAAmB;AACxB;AAAA,MACF;AAAA,MACA,KAAK,QAAQ,YAAY;AACvB,cAAM,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,IAAI;AAC5C,YAAI,gBAAgB,SAAS;AAC3B,eAAK,OAAO;AAAA,QACd;AACA,aAAK,mBAAmB;AACxB;AAAA,MACF;AAAA,MACA,KAAK,QAAQ,eAAe;AAC1B,cAAM,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,IAAI;AAC5C,YAAI,gBAAgB,YAAY;AAC9B,gBAAM,WAAW,CAAC,GAAG,KAAK,YAAY,CAAC;AACvC,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,kBAAM,QAAQ,SAAS,CAAC;AACxB,gBAAI,MAAM,cAAc,GAAG;AACzB,oBAAM,OAAO;AAAA,YACf;AAAA,UACF;AACA,cAAI,KAAK,YAAY,EAAE,WAAW,GAAG;AACnC,iBAAK,OAAO;AAAA,UACd;AACA,eAAK,KAAK;AAAA,QACZ;AACA,aAAK,mBAAmB;AACxB;AAAA,MACF;AAAA,MACA,KAAK,QAAQ,eAAe;AAC1B,cAAM,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,IAAI;AAC5C,YAAI,gBAAgB,YAAY;AAC9B,gBAAM,cAAc,KAAK,YAAY;AACrC,gBAAM,kBAAkB,KAAK,QAAQ,IAAI,KAAK,YAAY,CAAC;AAC3D,gBAAM,WAAW,WAAW;AAC5B,gBAAM,eAAe,IAAI,aAAa,UAAU,gBAAgB,qBAAqB,KAAK,QAAQ,CAAC,CAAC;AACpG,gBAAM,OAAO;AAAA,YACX,MAAM;AAAA,YACN,UAAU,CAAC;AAAA,UACb;AACA,gBAAM,MAAM,QAAQ,SAAS,MAAM,MAAM,YAAY;AACrD,uBAAa,OAAO;AACpB,eAAK,QAAQ,IAAI,UAAU,YAAY;AACvC,cAAI,KAAK,MAAM,aAAa,QAAQ,CAAC;AACrC,cAAI,aAAa;AACf,iBAAK,WAAW,kBAAkB;AAAA,UACpC;AAAA,QACF;AACA,aAAK,mBAAmB;AACxB;AAAA,MACF;AAAA,MACA,KAAK,QAAQ,YAAY;AACvB,cAAM,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,IAAI;AAC5C,YAAI,gBAAgB,SAAS;AAC3B,gBAAM,WAAW,WAAW;AAC5B,cAAI,IAAI,KAAK,MAAM;AACnB,cAAI,KAAK,UAAU,aAAa,YAAY;AAC1C,gBAAI,KAAK,UAAU,EAAE,QAAQ;AAAA,UAC/B,OAAO;AACL,gBAAI,KAAK,UAAU,EAAE,eAAe;AAAA,UACtC;AACA,gBAAM,kBAAkB,KAAK,QAAQ,IAAI,KAAK,YAAY,CAAC;AAC3D,gBAAM,eAAe,IAAI,aAAa,UAAU,gBAAgB,qBAAqB,CAAC,CAAC;AACvF,gBAAM,WAAW,WAAW;AAC5B,gBAAM,OAAO;AAAA,YACX,MAAM;AAAA,YACN,UAAU;AAAA,cACR,EAAE,MAAM,UAAU,IAAI,SAAS;AAAA,YACjC;AAAA,UACF;AACA,gBAAM,MAAM,QAAQ,SAAS,MAAM,MAAM,YAAY;AACrD,uBAAa,OAAO;AACpB,eAAK,QAAQ,IAAI,UAAU,YAAY;AACvC,gBAAM,SAAS,KAAK,MAAM,IAAI,QAAQ;AACtC,iBAAO,KAAK,MAAM,aAAa,QAAQ,GAAG,IAAI;AAAA,QAChD;AACA,aAAK,mBAAmB;AACxB;AAAA,MACF;AAAA,MACA,KAAK,QAAQ,cAAc;AACzB,cAAM,UAAU,KAAK,QAAQ,IAAI,OAAO,KAAK,QAAQ;AACrD,YAAI,SAAS;AACX,WAAC,KAAK,KAAK,WAAW,SAAS,OAAO,SAAS,GAAG,KAAK,QAAQ,MAAM,aAAa,QAAQ,EAAE;AAC5F,eAAK,WAAW,WAAW,CAAC,MAAM,UAAU;AAC1C,gBAAI,gBAAgB,SAAS;AAC3B,mBAAK,YAAYA,QAAO,cAAc;AAAA,YACxC;AAAA,UACF,CAAC;AACD,eAAK,QAAQ,OAAO,OAAO,KAAK,QAAQ;AAAA,QAC1C;AACA;AAAA,MACF;AAAA,MACA,KAAK,QAAQ,eAAe;AAC1B,cAAM,WAAW,WAAW;AAC5B,cAAM,eAAe,IAAI,aAAa,UAAU,KAAK,SAAS,OAAO,KAAK,IAAI,CAAC;AAC/E,cAAM,MAAM,QAAQ,SAAS,OAAO,KAAK,QAAQ,MAAM,YAAY;AACnE,qBAAa,OAAO;AACpB,aAAK,QAAQ,IAAI,UAAU,YAAY;AACvC,oBAAY;AACZ;AAAA,MACF;AAAA,MACA,KAAK,QAAQ,YAAY;AACvB,cAAM,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,IAAI;AAC5C,YAAI,gBAAgB,SAAS;AAC3B,eAAK,QAAQ,OAAO,KAAK,IAAI;AAAA,QAC/B;AACA;AAAA,MACF;AAAA,MACA,KAAK,QAAQ,YAAY;AACvB,cAAM,UAAU,KAAK,MAAM,IAAI,OAAO,KAAK,OAAO;AAClD,cAAM,WAAW,OAAO,KAAK,WAAW,OAAO,KAAK,WAAWA,QAAO;AACtE,cAAM,UAAU,KAAK,QAAQ,IAAI,QAAQ;AACzC,YAAI,mBAAmB,SAAS;AAC9B,gBAAM,SAAS,QAAQ,UAAU;AACjC,gBAAM,MAAM,OAAO,YAAY,EAAE,QAAQ,OAAO;AAChD,cAAI,kBAAkB,YAAY;AAChC,gBAAI,OAAO,YAAY,MAAM,KAAK;AAChC,qBAAO,YAAY,EAAE;AAAA,YACvB,OAAO;AACL,qBAAO,YAAY,GAAG;AAAA,YACxB;AAAA,UACF,WAAW,kBAAkB,YAAY;AACvC,gBAAI,OAAO,YAAY,MAAM,KAAK;AAChC,qBAAO,YAAY,GAAG;AAAA,YACxB;AACA,oBAAQ,eAAe;AAAA,UACzB;AAAA,QACF;AACA;AAAA,MACF;AAAA,MACA,KAAK,QAAQ,mBAAmB;AAC9B,cAAM,WAAW,OAAO,KAAK,WAAW,OAAO,KAAK,WAAWA,QAAO;AACtE,cAAM,UAAU,KAAK,QAAQ,IAAI,QAAQ;AACzC,YAAI,OAAO,KAAK,eAAe,QAAQ;AACrC,kBAAQ,eAAe;AAAA,QACzB,OAAO;AACL,gBAAM,aAAa,KAAK,MAAM,IAAI,OAAO,KAAK,UAAU;AACxD,cAAI,sBAAsB,YAAY;AACpC,oBAAQ,eAAe;AAAA,UACzB;AAAA,QACF;AACA;AAAA,MACF;AAAA,MACA,KAAK,QAAQ,gBAAgB;AAC3B,cAAM,MAAM,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM;AAC7C,cAAM,IAAI,IAAI,YAAY;AAC1B,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,gBAAM,IAAI,EAAE,CAAC;AACb,YAAE,UAAU,OAAO,KAAK,QAAQ,CAAC,CAAC;AAAA,QACpC;AACA;AAAA,MACF;AAAA,MACA,KAAK,QAAQ,qBAAqB;AAChC,cAAM,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,IAAI;AAC5C,YAAI,gBAAgB,YAAY;AAC9B,eAAK,QAAQ,OAAO,KAAK,GAAG;AAAA,QAC9B;AACA;AAAA,MACF;AAAA,MACA,KAAK,QAAQ,iBAAiB;AAC5B,cAAM,WAAW,OAAO,KAAK,WAAW,OAAO,KAAK,WAAWA,QAAO;AACtE,cAAM,UAAU,KAAK,QAAQ,IAAI,QAAQ;AACzC,cAAM,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,IAAI;AAC5C,YAAI,gBAAgB,YAAY;AAC9B,cAAI,SAAS,QAAQ,iBAAiB;AACpC,oBAAQ,kBAAkB;AAAA,UAC5B,OAAO;AACL,oBAAQ,kBAAkB;AAC1B,oBAAQ,eAAe;AAAA,UACzB;AAAA,QACF;AACA;AAAA,MACF;AAAA,MACA,KAAK,QAAQ,yBAAyB;AACpC,aAAK,YAAY,OAAO,KAAK,IAAI;AACjC;AAAA,MACF;AAAA,MACA,KAAK,QAAQ,wBAAwB;AACnC,cAAM,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,IAAI;AAC5C,aAAK,YAAY,OAAO,KAAK,IAAI;AACjC;AAAA,MACF;AAAA,IACF;AACA,SAAK,YAAY;AACjB,eAAW,YAAY,KAAK,iBAAiB;AAC3C,eAAS,MAAM;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,WAAWA,QAAO,gBAAgB;AAChD,UAAM,UAAU,KAAK,QAAQ,IAAI,QAAQ;AACzC,QAAI,WAAW,QAAQ,gBAAgB,KAAK,YAAY,QAAQ,aAAa,MAAM,CAAC,GAAG;AACrF,aAAO,QAAQ;AAAA,IACjB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB,WAAWA,QAAO,gBAAgB;AACnD,WAAO,KAAK,QAAQ,IAAI,QAAQ,EAAE;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,WAAWA,QAAO,gBAAgB;AACxC,WAAO,KAAK,QAAQ,IAAI,QAAQ,EAAE;AAAA,EACpC;AAAA,EACA,4BAA4B;AAC1B,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,4BAA4B;AAC1B,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,IAAI;AACb,SAAK,QAAQ,YAAY,EAAE;AAC3B,eAAW,CAAC,GAAG,CAAC,KAAK,KAAK,SAAS;AACjC,QAAE,KAAK,YAAY,IAAI,CAAC;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,iBAAiB,UAAU,IAAI;AAC7B,QAAI,KAAK,QAAQ,IAAI,QAAQ,GAAG;AAC9B,UAAI,aAAaA,QAAO,gBAAgB;AACtC,aAAK,QAAQ,YAAY,EAAE;AAAA,MAC7B;AACA,WAAK,QAAQ,IAAI,QAAQ,EAAE,WAAW,EAAE;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,IAAI;AACd,WAAO,KAAK,MAAM,IAAI,EAAE;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,OAAO,KAAK,QAAQ,IAAIA,QAAO,cAAc,EAAE,MAAM;AAClE,UAAM,QAAQ,KAAK,YAAY,EAAE,CAAC;AAClC,QAAI,iBAAiB,YAAY;AAC/B,aAAO;AAAA,IACT,OAAO;AACL,aAAO,KAAK,eAAe,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,SAAS,MAAM;AACpB,UAAM,QAAQ,IAAIA,QAAO;AACzB,IAAAA,QAAO,qBAAqB,SAAS,KAAK,QAAQ,MAAM,UAAU;AAClE,QAAI,KAAK,SAAS;AAChB,YAAM,UAAU,UAAU,SAAS,KAAK,SAAS,KAAK;AAAA,IACxD;AACA,QAAI,KAAK,SAAS;AAChB,iBAAW,YAAY,KAAK,SAAS;AACnC,cAAM,aAAa,KAAK,QAAQ,QAAQ;AACxC,cAAM,eAAe,aAAa,SAAS,YAAY,OAAO,QAAQ;AACtE,cAAM,QAAQ,IAAI,UAAU,YAAY;AAAA,MAC1C;AAAA,IACF;AACA,UAAM,WAAW,OAAO,QAAQ,SAAS,KAAK,QAAQ,OAAO,MAAM,cAAc,EAAE,IAAIA,QAAO,cAAc,CAAC;AAC7G,UAAM,KAAK;AACX,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,UAAM,SAAS,CAAC;AAChB,IAAAA,QAAO,qBAAqB,OAAO,QAAQ,KAAK,UAAU;AAC1D,SAAK,WAAW,CAAC,SAAS;AACxB,WAAK,UAAU,QAAQ,CAAC,CAAC;AAAA,IAC3B,CAAC;AACD,UAAM,UAAU,CAAC;AACjB,eAAW,CAAC,IAAI,OAAO,KAAK,KAAK,SAAS;AACxC,UAAI,OAAOA,QAAO,gBAAgB;AAChC,gBAAQ,EAAE,IAAI,QAAQ,OAAO;AAAA,MAC/B;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA,SAAS,KAAK,QAAQ,OAAO;AAAA,MAC7B,QAAQ,KAAK,WAAW,KAAK,OAAO;AAAA,MACpC,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,yBAAyB;AACvB,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,aAAa;AAC1B,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,gBAAgB;AAChC,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,kBAAkB,UAAU;AAC1B,SAAK,gBAAgB,KAAK,QAAQ;AAAA,EACpC;AAAA,EACA,qBAAqB,UAAU;AAC7B,UAAM,MAAM,KAAK,gBAAgB,UAAU,CAAC,MAAM,MAAM,QAAQ;AAChE,QAAI,QAAQ,IAAI;AACd,WAAK,gBAAgB,OAAO,KAAK,CAAC;AAAA,IACpC;AAAA,EACF;AAAA,EACA,WAAW;AACT,WAAO,KAAK,UAAU,KAAK,OAAO,CAAC;AAAA,EACrC;AAAA;AAAA;AAAA,EAGA,qBAAqB;AACnB,UAAM,eAA+B,oBAAI,IAAI;AAC7C,eAAW,CAAC,QAAQ,KAAK,KAAK,SAAS;AACrC,UAAI,aAAaA,QAAO,gBAAgB;AACtC,YAAI,QAAQ;AACZ,aAAK,iBAAiB,UAAU,CAAC,SAAS;AACxC,cAAI,gBAAgB,SAAS;AAC3B;AAAA,UACF;AAAA,QACF,CAAC;AACD,YAAI,UAAU,GAAG;AACf,uBAAa,IAAI,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,eAAW,YAAY,cAAc;AACnC,WAAK,QAAQ,OAAO,QAAQ;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB,YAAY,UAAU;AACpC,UAAM,UAAU,KAAK,QAAQ,IAAI,QAAQ;AACzC,QAAI,SAAS;AACX,UAAI,YAAY;AACd,gBAAQ,eAAe;AAAA,MACzB,OAAO;AACL,gBAAQ,eAAe;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB,YAAY,UAAU;AACvC,UAAM,UAAU,KAAK,QAAQ,IAAI,QAAQ;AACzC,QAAI,SAAS;AACX,UAAI,YAAY;AACd,gBAAQ,kBAAkB;AAAA,MAC5B,OAAO;AACL,gBAAQ,kBAAkB;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,MAAM,MAAM;AACjB,SAAK,WAAW,CAAC,SAAS;AACxB,WAAK,MAAM,IAAI,KAAK,MAAM,GAAG,IAAI;AAAA,IACnC,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,QAAQ,MAAM;AACZ,UAAM,KAAK,KAAK,MAAM;AACtB,QAAI,KAAK,MAAM,IAAI,EAAE,GAAG;AACtB,YAAM,IAAI,MAAM,wDAAwD,KAAK,MAAM,CAAC,EAAE;AAAA,IACxF;AACA,SAAK,MAAM,IAAI,IAAI,IAAI;AAAA,EACzB;AAAA;AAAA,EAEA,mBAAmB,UAAU,UAAU,GAAG,GAAG;AAC3C,QAAI,OAAO,KAAK,QAAQ,IAAI,QAAQ,EAAE,KAAK,mBAAmB,UAAU,UAAU,GAAG,CAAC;AACtF,QAAI,SAAS,UAAU,aAAaA,QAAO,gBAAgB;AACzD,aAAO,KAAK,QAAQ,mBAAmB,UAAU,GAAG,CAAC;AAAA,IACvD;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,OAAO;AACL,eAAW,CAAC,GAAG,OAAO,KAAK,KAAK,SAAS;AACvC,cAAQ,KAAK,KAAK;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,YAAY,MAAM;AAChB,IAAAA,QAAO,qBAAqB,OAAO,MAAM,KAAK,UAAU;AAAA,EAC1D;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,MAAM,WAAW;AAAA,EAC1B;AAAA;AAAA,EAEA,aAAa,MAAM;AACjB,WAAO,KAAK,WAAW,IAAI;AAAA,EAC7B;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO,yBAAyB;AAC9B,IAAAA,QAAO,qBAAqB,eAAe,WAAW,QAAQ,wBAAwB,CAAC;AACvF,IAAAA,QAAO,qBAAqB,eAAe,cAAc,WAAW,wBAAwB,CAAC;AAC7F,IAAAA,QAAO,qBAAqB,eAAe,WAAW,QAAQ,wBAAwB,CAAC;AACvF,IAAAA,QAAO,qBAAqB,eAAe,cAAc,WAAW,wBAAwB,CAAC;AAC7F,UAAM,KAAK,CAAC;AACZ,OAAG,KAAKA,QAAO,qBAAqB,sBAAsB,UAAU,MAAM,CAAC;AAC3E,OAAG,KAAK,QAAQ,wBAAwB,EAAE,sBAAsB,OAAOA,QAAO,oBAAoB,CAAC;AACnG,OAAG,KAAK,WAAW,wBAAwB,EAAE,sBAAsB,UAAUA,QAAO,oBAAoB,CAAC;AACzG,OAAG,KAAK,QAAQ,wBAAwB,EAAE,sBAAsB,OAAOA,QAAO,oBAAoB,CAAC;AACnG,OAAG,KAAK,WAAW,wBAAwB,EAAE,sBAAsB,UAAUA,QAAO,oBAAoB,CAAC;AACzG,YAAQ,IAAI,GAAG,KAAK,IAAI,CAAC;AAAA,EAC3B;AAAA;AAAA,EAEA,OAAO,6BAA6B;AAClC,UAAM,uBAAuB,IAAI,qBAAqB;AACtD,yBAAqB,IAAI,kBAAkB,IAAI,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MAC1E;AAAA,IACF;AACA,yBAAqB,IAAI,2BAA2B,KAAK,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MACpF;AAAA,IACF;AACA,yBAAqB,IAAI,2BAA2B,IAAI,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MACnF;AAAA,IACF;AACA,yBAAqB,IAAI,gBAAgB,CAAC,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MACpE;AAAA,IACF;AACA,yBAAqB,IAAI,iBAAiB,CAAC,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MACrE;AAAA,IACF;AACA,yBAAqB,IAAI,wBAAwB,KAAK,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MACjF;AAAA,IACF;AACA,yBAAqB,IAAI,kBAAkB,IAAI,EAAE,QAAQ,UAAU,OAAO;AAC1E,yBAAqB,IAAI,gBAAgB,CAAC,EAAE,QAAQ,YAAY;AAChE,yBAAqB,IAAI,mBAAmB,KAAK,EAAE,QAAQ,UAAU,OAAO,EAAE,SAAS,gBAAgB;AACvG,yBAAqB,IAAI,uBAAuB,IAAI,EAAE,QAAQ,UAAU,OAAO;AAC/E,yBAAqB,IAAI,0BAA0B,KAAK,EAAE,QAAQ,UAAU,OAAO;AACnF,yBAAqB,IAAI,iBAAiB,IAAI,EAAE,QAAQ,UAAU,OAAO;AACzE,yBAAqB,IAAI,mBAAmB,IAAI,EAAE,QAAQ,UAAU,OAAO;AAC3E,yBAAqB,IAAI,uBAAuB,MAAM,EAAE,QAAQ,UAAU,MAAM;AAChF,yBAAqB,IAAI,gBAAgB,MAAM,EAAE,QAAQ,UAAU,MAAM;AACzE,yBAAqB,IAAI,WAAW,MAAM,EAAE,QAAQ,UAAU,MAAM;AACpE,yBAAqB,IAAI,2BAA2B,IAAI,EAAE,QAAQ,UAAU,OAAO;AACnF,yBAAqB,IAAI,gBAAgB,GAAG,EAAE,QAAQ,UAAU,MAAM;AACtE,yBAAqB,IAAI,kBAAkB,EAAE,EAAE,QAAQ,UAAU,MAAM;AACvE,yBAAqB,IAAI,mBAAmB,EAAE,EAAE,QAAQ,UAAU,MAAM;AACxE,yBAAqB,IAAI,+BAA+B,IAAI,EAAE,QAAQ,UAAU,OAAO;AACvF,yBAAqB,IAAI,oBAAoB,IAAI,EAAE,QAAQ,UAAU,OAAO;AAC5E,yBAAqB,IAAI,oBAAoB,IAAI,EAAE,QAAQ,UAAU,OAAO;AAC5E,yBAAqB,IAAI,sBAAsB,IAAI,EAAE,QAAQ,UAAU,OAAO;AAC9E,yBAAqB,IAAI,wBAAwB,IAAI,EAAE,QAAQ,UAAU,OAAO;AAChF,yBAAqB,IAAI,qBAAqB,KAAK,EAAE,QAAQ,UAAU,OAAO;AAC9E,yBAAqB,IAAI,gCAAgC,KAAK,EAAE,QAAQ,UAAU,OAAO;AACzF,yBAAqB,IAAI,uBAAuB,IAAI,EAAE,QAAQ,UAAU,OAAO;AAC/E,yBAAqB,IAAI,0BAA0B,KAAK,EAAE,QAAQ,UAAU,OAAO;AACnF,yBAAqB,IAAI,2BAA2B,MAAM,EAAE,QAAQ,UAAU,MAAM;AACpF,yBAAqB,IAAI,wBAAwB,IAAI,EAAE,QAAQ,UAAU,OAAO;AAChF,yBAAqB,IAAI,uBAAuB,KAAK,EAAE,QAAQ,UAAU,OAAO;AAChF,yBAAqB,IAAI,qBAAqB,KAAK,EAAE,QAAQ,cAAc;AAC3E,yBAAqB,IAAI,eAAe,UAAU,EAAE,QAAQ,UAAU,MAAM;AAC5E,yBAAqB,IAAI,gBAAgB,UAAU,EAAE,QAAQ,UAAU,MAAM;AAC7E,yBAAqB,IAAI,kBAAkB,UAAU,EAAE,QAAQ,UAAU,MAAM;AAC/E,yBAAqB,IAAI,mBAAmB,UAAU,EAAE,QAAQ,UAAU,MAAM;AAChF,yBAAqB,IAAI,eAAe,UAAU,EAAE,QAAQ,UAAU,MAAM;AAC5E,yBAAqB,IAAI,gBAAgB,UAAU,EAAE,QAAQ,UAAU,MAAM;AAC7E,yBAAqB,IAAI,kBAAkB,UAAU,EAAE,QAAQ,UAAU,MAAM;AAC/E,yBAAqB,IAAI,mBAAmB,UAAU,EAAE,QAAQ,UAAU,MAAM;AAChF,yBAAqB,IAAI,4BAA4B,KAAK,EAAE,QAAQ,UAAU,OAAO;AACrF,yBAAqB,IAAI,cAAc,GAAG,EAAE,QAAQ,UAAU,MAAM;AACpE,yBAAqB,IAAI,iBAAiB,UAAU,EAAE,QAAQ,UAAU,MAAM;AAC9E,yBAAqB,IAAI,iBAAiB,UAAU,EAAE,QAAQ,UAAU,MAAM;AAC9E,yBAAqB,IAAI,oBAAoB,IAAI,EAAE,QAAQ,UAAU,OAAO;AAC5E,yBAAqB,IAAI,+BAA+B,IAAI,EAAE,QAAQ,UAAU,OAAO;AACvF,yBAAqB,IAAI,iCAAiC,KAAK,EAAE,QAAQ,UAAU,OAAO;AAC1F,yBAAqB,IAAI,mBAAmB,MAAM,EAAE,QAAQ,UAAU,MAAM;AAC5E,yBAAqB,IAAI,wBAAwB,KAAK,EAAE,QAAQ,UAAU,OAAO;AACjF,yBAAqB,IAAI,4BAA4B,KAAK,EAAE,QAAQ,UAAU,OAAO;AACrF,WAAO;AAAA,EACT;AACF;AACA,cAAc,QAAQ,kBAAkB,oBAAoB;AAE5D,cAAc,QAAQ,wBAAwB,OAAO,2BAA2B,CAAC;AACjF,IAAI,QAAQ;AACZ,IAAM,cAAc,MAAMC,qBAAoB,KAAK;AAAA;AAAA,EAEjD,YAAY,UAAU,MAAM,OAAO;AACjC,UAAM,KAAK;AAEX,kBAAc,MAAM,eAAe,KAAK,MAAM,CAAC;AAE/C,kBAAc,MAAM,iBAAiB,KAAK,MAAM,CAAC;AAEjD,kBAAc,MAAM,UAAU;AAC9B,SAAK,WAAW;AAChB,SAAK,WAAW,KAAK,UAAU,SAAS,QAAQ,CAAC;AACjD,IAAAA,aAAY,qBAAqB,SAAS,MAAM,KAAK,UAAU;AAC/D,UAAM,QAAQ,IAAI;AAAA,EACpB;AAAA;AAAA,EAEA,OAAO,SAAS,MAAM,OAAO;AAC3B,UAAM,WAAW,aAAa,UAAU,KAAK,QAAQ;AACrD,UAAM,SAAS,IAAIA,aAAY,UAAU,MAAM,KAAK;AACpD,QAAI,KAAK,UAAU;AACjB,aAAO,WAAW,KAAK,SAAS,IAAI,CAAC,cAAc;AACjD,cAAM,QAAQ,QAAQ,SAAS,WAAW,KAAK;AAC/C,cAAM,UAAU,MAAM;AACtB,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe;AACb,WAAO,KAAK,QAAQ,WAAW;AAAA,EACjC;AAAA,EACA,eAAe;AACb,WAAO,KAAK,SAAS,gBAAgB,YAAY;AAAA,EACnD;AAAA,EACA,UAAU;AACR,UAAM,cAAc,KAAK,QAAQ,MAAM;AACvC,UAAM,WAAW,KAAK,YAAY;AAClC,QAAI,aAAa,IAAI;AACnB,aAAO;AAAA,IACT,OAAO;AACL,YAAM,UAAU,KAAK,SAAS,QAAQ;AACtC,YAAM,gBAAgB,KAAK,aAAa,IAAI,QAAQ,QAAQ,aAAa,IAAI,QAAQ,QAAQ,cAAc;AAC3G,UAAI,kBAAkB,IAAI;AACxB,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa;AACX,UAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAI,MAAM,KAAK,QAAQ,SAAS;AAChC,QAAI,cAAc;AAChB,YAAM,UAAU,KAAK,aAAa,IAAI,aAAa,YAAY,IAAI,aAAa,aAAa;AAC7F,YAAM,KAAK,IAAI,KAAK,OAAO;AAAA,IAC7B;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AACX,UAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAI,MAAM,KAAK,QAAQ,SAAS;AAChC,QAAI,cAAc;AAChB,YAAM,UAAU,KAAK,aAAa,IAAI,aAAa,YAAY,IAAI,aAAa,aAAa;AAC7F,YAAM,KAAK,IAAI,KAAK,OAAO;AAAA,IAC7B;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,aAAa;AACX,WAAO,KAAK,QAAQ,gBAAgB;AAAA,EACtC;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,YAAY,MAAM,IAAI;AAC7B,aAAO,KAAK,SAAS,KAAK,YAAY,CAAC;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,SAAS,eAAe;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,cAAc;AACZ,WAAO;AAAA,EACT;AAAA,EACA,YAAY;AACV,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,SAAS;AACP,UAAM,OAAO,CAAC;AACd,IAAAA,aAAY,qBAAqB,OAAO,MAAM,KAAK,UAAU;AAC7D,SAAK,WAAW,KAAK,SAAS,QAAQ;AACtC,SAAK,WAAW,KAAK,SAAS,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC;AAC3D,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,gBAAgB,UAAU;AACxB,QAAI,YAAY,MAAM;AACpB,iBAAW,KAAK,YAAY,MAAM;AAAA,IACpC;AACA,QAAI,UAAU;AACZ,aAAO,KAAK,QAAQ,uBAAuB;AAAA,IAC7C,OAAO;AACL,aAAO,KAAK,QAAQ,yBAAyB;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,WAAO,KAAK,QAAQ,oBAAoB;AAAA,EAC1C;AAAA;AAAA,EAEA,YAAY,OAAO;AACjB,SAAK,WAAW,WAAW;AAAA,EAC7B;AAAA;AAAA,EAEA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,iBAAiB,GAAG;AAClB,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA,EAEA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,eAAe,GAAG;AAChB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,KAAK,QAAQ,YAAY;AAAA,EAClC;AAAA;AAAA,EAEA,QAAQ,KAAK;AACX,UAAM,WAAW,KAAK,YAAY;AAClC,QAAI,aAAa,IAAI;AACnB,WAAK,WAAW,OAAO;AAAA,IACzB,OAAO;AACL,YAAM,UAAU,KAAK,SAAS,QAAQ;AACtC,YAAM,gBAAgB,KAAK,aAAa,IAAI,QAAQ,QAAQ,aAAa,IAAI,QAAQ,QAAQ,cAAc;AAC3G,UAAI,kBAAkB,IAAI;AACxB,aAAK,WAAW,OAAO;AAAA,MACzB,OAAO;AACL,YAAI,KAAK,aAAa,GAAG;AACvB,kBAAQ,eAAe,GAAG;AAAA,QAC5B,OAAO;AACL,kBAAQ,gBAAgB,GAAG;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,YAAY,MAAM;AAChB,IAAAA,aAAY,qBAAqB,OAAO,MAAM,KAAK,UAAU;AAAA,EAC/D;AAAA;AAAA,EAEA,OAAO,MAAM;AACX,UAAM,eAAe,KAAK,YAAY,IAAI;AAC1C,QAAI,KAAK,YAAY,MAAM,IAAI;AAC7B,0BAAoB,MAAM,YAAY;AAAA,IACxC;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ,UAAU,GAAG,GAAG;AACtB,QAAI,EAAE,oBAAoB,UAAU;AAClC,aAAO;AAAA,IACT;AACA,QAAI;AACJ,UAAM,eAAe,aAAa;AAClC,QAAI,KAAK,cAAc,SAAS,GAAG,CAAC,GAAG;AACrC,UAAI,KAAK,SAAS,gBAAgB,YAAY,MAAM;AAClD,YAAI,KAAK,SAAS,SAAS,GAAG;AAC5B,cAAI,QAAQ,KAAK,SAAS,CAAC;AAC3B,cAAI,YAAY,MAAM,WAAW;AACjC,gBAAM,SAAS,UAAU;AACzB,gBAAM,cAAc,UAAU;AAC9B,cAAI,MAAM,KAAK,cAAc;AAC7B,cAAI,cAAc;AAClB,mBAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,oBAAQ,KAAK,SAAS,CAAC;AACvB,wBAAY,MAAM,WAAW;AAC7B,0BAAc,UAAU,IAAI,UAAU,QAAQ;AAC9C,gBAAI,KAAK,OAAO,IAAI,aAAa;AAC/B,oBAAM,cAAc,IAAI,KAAK,UAAU,IAAI,GAAG,QAAQ,GAAG,WAAW;AACpE,yBAAW,IAAI,SAAS,MAAM,aAAa,cAAc,GAAG,QAAQ,wBAAwB;AAC5F;AAAA,YACF;AACA,kBAAM;AAAA,UACR;AACA,cAAI,YAAY,MAAM;AACpB,kBAAM,cAAc,IAAI,KAAK,UAAU,SAAS,IAAI,GAAG,QAAQ,GAAG,WAAW;AAC7E,uBAAW,IAAI,SAAS,MAAM,aAAa,cAAc,KAAK,SAAS,QAAQ,QAAQ,wBAAwB;AAAA,UACjH;AAAA,QACF,OAAO;AACL,gBAAM,cAAc,IAAI,KAAK,KAAK,cAAc,IAAI,GAAG,KAAK,cAAc,IAAI,GAAG,GAAG,EAAE;AACtF,qBAAW,IAAI,SAAS,MAAM,aAAa,cAAc,GAAG,QAAQ,wBAAwB;AAAA,QAC9F;AAAA,MACF,OAAO;AACL,YAAI,KAAK,SAAS,SAAS,GAAG;AAC5B,cAAI,QAAQ,KAAK,SAAS,CAAC;AAC3B,cAAI,YAAY,MAAM,WAAW;AACjC,gBAAM,SAAS,UAAU;AACzB,gBAAM,aAAa,UAAU;AAC7B,cAAI,MAAM,KAAK,cAAc;AAC7B,cAAI,cAAc;AAClB,mBAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,oBAAQ,KAAK,SAAS,CAAC;AACvB,wBAAY,MAAM,WAAW;AAC7B,0BAAc,UAAU,IAAI,UAAU,SAAS;AAC/C,gBAAI,KAAK,OAAO,IAAI,aAAa;AAC/B,oBAAM,cAAc,IAAI,KAAK,QAAQ,UAAU,IAAI,GAAG,YAAY,CAAC;AACnE,yBAAW,IAAI,SAAS,MAAM,aAAa,cAAc,GAAG,QAAQ,wBAAwB;AAC5F;AAAA,YACF;AACA,kBAAM;AAAA,UACR;AACA,cAAI,YAAY,MAAM;AACpB,kBAAM,cAAc,IAAI,KAAK,QAAQ,UAAU,UAAU,IAAI,GAAG,YAAY,CAAC;AAC7E,uBAAW,IAAI,SAAS,MAAM,aAAa,cAAc,KAAK,SAAS,QAAQ,QAAQ,wBAAwB;AAAA,UACjH;AAAA,QACF,OAAO;AACL,gBAAM,cAAc,IAAI,KAAK,KAAK,cAAc,IAAI,GAAG,KAAK,cAAc,IAAI,GAAG,IAAI,CAAC;AACtF,qBAAW,IAAI,SAAS,MAAM,aAAa,cAAc,GAAG,QAAQ,wBAAwB;AAAA,QAC9F;AAAA,MACF;AACA,UAAI,CAAC,SAAS,YAAY,UAAU,QAAQ,GAAG;AAC7C,eAAO;AAAA,MACT;AAAA,IACF,WAAW,KAAK,YAAY,MAAM,MAAM,KAAK,YAAY,SAAS,GAAG,CAAC,GAAG;AACvE,YAAM,cAAc,KAAK;AACzB,iBAAW,IAAI,SAAS,MAAM,aAAa,cAAc,IAAI,QAAQ,wBAAwB;AAC7F,UAAI,CAAC,SAAS,YAAY,UAAU,QAAQ,GAAG;AAC7C,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,KAAK,UAAU,UAAU,OAAO,QAAQ;AACtC,QAAI,YAAY;AAChB,UAAM,aAAa,SAAS,UAAU;AACtC,QAAI,eAAe,QAAQ;AACzB,kBAAY,WAAW,YAAY,QAAQ;AAC3C,UAAI,eAAe,QAAQ,sBAAsBA,gBAAe,WAAW,YAAY,MAAM,WAAW;AACtG,mBAAW,YAAY,EAAE;AAAA,MAC3B,OAAO;AACL,4BAAoB,YAAY,SAAS;AAAA,MAC3C;AAAA,IACF;AACA,QAAI,oBAAoB,WAAW,eAAe,QAAQ,YAAY,SAAS,QAAQ,GAAG;AACxF;AAAA,IACF;AACA,QAAI,YAAY;AAChB,QAAI,cAAc,IAAI;AACpB,kBAAY,KAAK,SAAS;AAAA,IAC5B;AACA,QAAI,oBAAoB,SAAS;AAC/B,WAAK,SAAS,UAAU,SAAS;AAAA,IACnC;AACA,QAAI,UAAU,WAAW,SAAS,KAAK,gBAAgB,GAAG;AACxD,WAAK,YAAY,SAAS;AAAA,IAC5B;AACA,SAAK,MAAM,KAAK;AAAA,EAClB;AAAA;AAAA,EAEA,kBAAkB,OAAO,aAAa,OAAO;AAC3C,UAAM,UAAU,CAAC,GAAG,CAAC;AACrB,UAAM,UAAU,aAAa,KAAK,WAAW,IAAI;AACjD,UAAM,UAAU,aAAa,KAAK,WAAW,IAAI;AACjD,UAAM,UAAU,KAAK,MAAM,QAAQ,MAAM,cAAc;AACvD,UAAM,YAAY,QAAQ,QAAQ;AAClC,UAAM,eAAe,KAAK,MAAM,gBAAgB;AAChD,QAAI,KAAK,aAAa,aAAa,KAAK;AACtC,cAAQ,CAAC,IAAI,KAAK,cAAc,UAAU,IAAI;AAC9C,YAAM,SAAS,KAAK,cAAc,UAAU,IAAI;AAChD,cAAQ,CAAC,IAAI,KAAK,IAAI,QAAQ,CAAC,GAAG,UAAU,UAAU,IAAI,QAAQ,aAAa,IAAI,YAAY;AAC/F,cAAQ,CAAC,IAAI,KAAK,IAAI,QAAQ,CAAC,GAAG,MAAM;AAAA,IAC1C,WAAW,KAAK,aAAa,aAAa,MAAM;AAC9C,cAAQ,CAAC,IAAI,KAAK,cAAc,SAAS,IAAI;AAC7C,YAAM,SAAS,KAAK,cAAc,SAAS,IAAI;AAC/C,cAAQ,CAAC,IAAI,KAAK,IAAI,QAAQ,CAAC,GAAG,UAAU,SAAS,IAAI,QAAQ,YAAY,IAAI,YAAY;AAC7F,cAAQ,CAAC,IAAI,KAAK,IAAI,QAAQ,CAAC,GAAG,MAAM;AAAA,IAC1C,WAAW,KAAK,aAAa,aAAa,QAAQ;AAChD,cAAQ,CAAC,IAAI,KAAK,cAAc,IAAI,UAAU;AAC9C,YAAM,SAAS,KAAK,cAAc,IAAI,UAAU;AAChD,cAAQ,CAAC,IAAI,KAAK,IAAI,QAAQ,CAAC,GAAG,UAAU,IAAI,QAAQ,aAAa,CAAC;AACtE,cAAQ,CAAC,IAAI,KAAK,IAAI,QAAQ,CAAC,GAAG,MAAM;AAAA,IAC1C,WAAW,KAAK,aAAa,aAAa,OAAO;AAC/C,cAAQ,CAAC,IAAI,KAAK,cAAc,IAAI,UAAU;AAC9C,YAAM,SAAS,KAAK,cAAc,IAAI,UAAU;AAChD,cAAQ,CAAC,IAAI,KAAK,IAAI,QAAQ,CAAC,GAAG,UAAU,IAAI,QAAQ,YAAY,CAAC;AACrE,cAAQ,CAAC,IAAI,KAAK,IAAI,QAAQ,CAAC,GAAG,MAAM;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,eAAe,UAAU,aAAa;AACpC,UAAM,UAAU,KAAK,kBAAkB,WAAW;AAClD,QAAI,KAAK,aAAa,aAAa,UAAU,KAAK,aAAa,aAAa,OAAO;AACjF,aAAO,KAAK,IAAI,GAAG,QAAQ,CAAC,IAAI,WAAW;AAAA,IAC7C,OAAO;AACL,aAAO,KAAK,IAAI,GAAG,cAAc,QAAQ,CAAC,CAAC;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA,EAEA,0BAA0B;AACxB,WAAOA,aAAY;AAAA,EACrB;AAAA;AAAA,EAEA,OAAO,0BAA0B;AAC/B,WAAOA,aAAY;AAAA,EACrB;AAAA;AAAA,EAEA,OAAO,6BAA6B;AAClC,UAAM,uBAAuB,IAAI,qBAAqB;AACtD,yBAAqB,IAAI,QAAQA,aAAY,MAAM,IAAI,EAAE,QAAQ,UAAU,MAAM,EAAE,SAAS;AAC5F,yBAAqB,IAAI,YAAY,EAAE,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MACjE;AAAA,IACF;AACA,yBAAqB,IAAI,QAAQ,IAAI,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MAChE;AAAA,IACF;AACA,yBAAqB,IAAI,UAAU,MAAM,EAAE,QAAQ,KAAK,EAAE;AAAA,MACxD;AAAA,IACF;AACA,yBAAqB,aAAa,cAAc,kBAAkB,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MAC7F;AAAA,IACF;AACA,yBAAqB,aAAa,aAAa,iBAAiB,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MAC1F;AAAA,IACF;AACA,yBAAqB,aAAa,yBAAyB,6BAA6B,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MACnH;AAAA,IACF;AACA,yBAAqB,aAAa,2BAA2B,+BAA+B,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MACvH;AAAA,IACF;AACA,yBAAqB,aAAa,QAAQ,YAAY,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MAChF;AAAA,IACF;AACA,yBAAqB,aAAa,WAAW,eAAe,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MACtF;AAAA,IACF;AACA,yBAAqB,aAAa,WAAW,eAAe,EAAE,QAAQ,UAAU,MAAM,EAAE;AAAA,MACtF;AAAA,IACF;AACA,yBAAqB,aAAa,kBAAkB,sBAAsB,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MACrG;AAAA,IACF;AACA,yBAAqB,aAAa,sBAAsB,0BAA0B,EAAE,QAAQ,UAAU,OAAO,EAAE;AAAA,MAC7G;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,cAAc,aAAa,QAAQ,QAAQ;AAE3C,cAAc,aAAa,wBAAwB,YAAY,2BAA2B,CAAC;AAC3F,IAAI,aAAa;AACjB,IAAI,mBAAmB;AACvB,IAAM,WAAW,CAAC,UAAU;AAC1B,QAAM,EAAE,QAAQ,MAAM,OAAO,WAAW,IAAI;AAC5C,QAAM,CAAC,UAAU,WAAW,IAAU,eAAS,KAAK;AACpD,QAAM,UAAgB,aAAO,IAAI;AACjC,QAAM,cAAoB,aAAO,IAAI;AACrC,QAAM,UAAgB,aAAO,CAAC,CAAC;AAC/B,QAAM,aAAmB,aAAO,MAAM;AACtC,QAAM,YAAkB,aAAO,MAAM;AACrC,QAAM,aAAmB,aAAO,CAAC;AACjC,QAAM,aAAmB,aAAO,CAAC;AACjC,QAAM,cAAoB,aAAO,EAAE,cAAc,CAAC,GAAG,KAAK,GAAG,eAAe,EAAE,CAAC;AAC/E,QAAM,OAAO,KAAK,SAAS,EAAE,gBAAgB;AAC7C,MAAI,QAAQ,KAAK,SAAS,EAAE,iBAAiB;AAC7C,MAAI,CAAC,UAAU,GAAG;AAChB,YAAQ,KAAK,IAAI,IAAI,QAAQ,IAAI,IAAI;AAAA,EACvC;AACA,EAAM,gBAAU,MAAM;AACpB,QAAI,IAAI;AACR,KAAC,KAAK,QAAQ,YAAY,OAAO,SAAS,GAAG,iBAAiB,cAAc,cAAc,EAAE,SAAS,MAAM,CAAC;AAC5G,KAAC,KAAK,YAAY,YAAY,OAAO,SAAS,GAAG,iBAAiB,cAAc,cAAc,EAAE,SAAS,MAAM,CAAC;AAChH,WAAO,MAAM;AACX,UAAI,KAAK;AACT,OAAC,MAAM,QAAQ,YAAY,OAAO,SAAS,IAAI,oBAAoB,cAAc,YAAY;AAC7F,OAAC,MAAM,YAAY,YAAY,OAAO,SAAS,IAAI,oBAAoB,cAAc,YAAY;AAAA,IACnG;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,eAAe,CAAC,UAAU;AAC9B,UAAM,eAAe;AACrB,UAAM,yBAAyB;AAAA,EACjC;AACA,QAAM,gBAAgB,CAAC,UAAU;AAC/B,QAAI;AACJ,UAAM,gBAAgB;AACtB,QAAI,gBAAgB,SAAS;AAC3B,kBAAY,UAAU,KAAK,oBAAoB,KAAK;AAAA,IACtD;AACA,2BAAuB,OAAO,OAAO,mBAAmB,CAAC;AACzD,cAAU,MAAM,cAAc,eAAe,OAAO,YAAY,WAAW,YAAY;AACvF,YAAQ,UAAU,KAAK,kBAAkB,OAAO,IAAI;AACpD,UAAM,UAAU,OAAO,WAAW;AAClC,eAAW,UAAU,OAAO,mBAAmB,EAAE,cAAc,KAAK;AACpE,eAAW,QAAQ,MAAM,gBAAgB,aAAa,QAAQ;AAC9D,eAAW,QAAQ,YAAY,OAAO,aAAa,QAAQ,yBAAyB;AACpF,eAAW,QAAQ,MAAM,SAAS,KAAK,eAAe,MAAM,YAAY,OAAO,cAAc;AAC7F,QAAI,KAAK,SAAS,EAAE,uBAAuB,GAAG;AAC5C,gBAAU,UAAU,OAAO,mBAAmB,EAAE,cAAc,KAAK;AACnE,gBAAU,QAAQ,YAAY,GAAG,QAAQ,2BAA2B,IAAI,OAAO,aAAa,GAAG,QAAQ,gCAAgC,IAAI,GAAG,QAAQ,gCAAgC;AACtL,iBAAW,QAAQ,YAAY,UAAU,OAAO;AAAA,IAClD;AACA,UAAM,KAAK,KAAK,QAAQ,YAAY,OAAO,SAAS,GAAG,sBAAsB;AAC7E,UAAM,OAAO,IAAI;AAAA,MACf,EAAE,IAAI,OAAO,WAAW,EAAE;AAAA,MAC1B,EAAE,IAAI,OAAO,WAAW,EAAE;AAAA,MAC1B,EAAE;AAAA,MACF,EAAE;AAAA,IACJ;AACA,eAAW,UAAU,MAAM,UAAU,EAAE;AACvC,eAAW,UAAU,MAAM,UAAU,EAAE;AACvC,SAAK,gBAAgB,WAAW,OAAO;AACvC,QAAI,SAAS;AACX,cAAQ,YAAY,WAAW,OAAO;AAAA,IACxC;AACA,gBAAY,IAAI;AAChB,uBAAmB;AAAA,EACrB;AACA,QAAM,eAAe,MAAM;AACzB,UAAM,UAAU,OAAO,WAAW;AAClC,QAAI,WAAW,WAAW,SAAS;AACjC,cAAQ,YAAY,WAAW,OAAO;AAAA,IACxC;AACA,eAAW,UAAU;AACrB,gBAAY,KAAK;AACjB,uBAAmB;AAAA,EACrB;AACA,QAAM,aAAa,CAAC,GAAG,MAAM;AAC3B,QAAI,WAAW,SAAS;AACtB,YAAM,aAAa,OAAO,WAAW;AACrC,UAAI,CAAC,YAAY;AACf;AAAA,MACF;AACA,UAAI,KAAK,eAAe,MAAM,YAAY,MAAM;AAC9C,mBAAW,QAAQ,MAAM,MAAM,iBAAiB,IAAI,WAAW,IAAI,WAAW,OAAO,IAAI;AAAA,MAC3F,OAAO;AACL,mBAAW,QAAQ,MAAM,OAAO,iBAAiB,IAAI,WAAW,IAAI,WAAW,OAAO,IAAI;AAAA,MAC5F;AACA,UAAI,OAAO,iBAAiB,GAAG;AAC7B,qBAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACA,QAAM,YAAY,MAAM;AACtB,QAAI,WAAW,SAAS;AACtB,mBAAa;AACb,YAAM,UAAU,OAAO,WAAW;AAClC,UAAI,WAAW,WAAW,SAAS;AACjC,gBAAQ,YAAY,WAAW,OAAO;AAAA,MACxC;AACA,iBAAW,UAAU;AAAA,IACvB;AACA,2BAAuB,MAAM,OAAO,mBAAmB,CAAC;AACxD,gBAAY,KAAK;AACjB,uBAAmB;AAAA,EACrB;AACA,QAAM,eAAe,CAAC,aAAa;AACjC,UAAM,SAAS,MAAM;AACnB,UAAI,WAAW,SAAS;AACtB,YAAI,QAAQ;AACZ,YAAI,KAAK,eAAe,MAAM,YAAY,MAAM;AAC9C,kBAAQ,WAAW,QAAQ;AAAA,QAC7B,OAAO;AACL,kBAAQ,WAAW,QAAQ;AAAA,QAC7B;AACA,YAAI,gBAAgB,YAAY;AAC9B,gBAAM,MAAM,KAAK,eAAe,MAAM,KAAK;AAC3C,iBAAO,SAAS,QAAQ,kBAAkB,KAAK,MAAM,GAAG,GAAG,CAAC;AAAA,QAC9D,OAAO;AACL,gBAAM,OAAO,YAAY;AACzB,gBAAM,UAAU,KAAK,eAAe,OAAO,OAAO,KAAK,cAAc,KAAK,KAAK,KAAK,aAAa;AACjG,iBAAO,SAAS,QAAQ,cAAc,KAAK,MAAM,GAAG,OAAO,CAAC;AAAA,QAC9D;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,mBAAmB,CAAC,MAAM;AAC9B,UAAM,SAAS,QAAQ;AACvB,QAAI,MAAM;AACV,QAAI,IAAI,OAAO,CAAC,GAAG;AACjB,YAAM,OAAO,CAAC;AAAA,IAChB;AACA,QAAI,IAAI,OAAO,CAAC,GAAG;AACjB,YAAM,OAAO,CAAC;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,QAAM,KAAK,OAAO;AAClB,QAAM,SAAS;AAAA,IACb,QAAQ,aAAa,cAAc;AAAA,IACnC,eAAe,aAAa,WAAW;AAAA,EACzC;AACA,MAAI,YAAY,GAAG,QAAQ,oBAAoB,IAAI,MAAM,GAAG,QAAQ,wBAAwB,KAAK,eAAe,EAAE,QAAQ,CAAC;AAC3H,MAAI,gBAAgB,YAAY;AAC9B,iBAAa,MAAM,GAAG,QAAQ,2BAA2B;AAAA,EAC3D,OAAO;AACL,QAAI,KAAK,SAAS,EAAE,mBAAmB,OAAO,YAAY,CAAC,MAAM,QAAQ;AACvE,aAAO,UAAU;AAAA,IACnB;AAAA,EACF;AACA,MAAI,YAAY;AACd,WAAO,QAAQ,OAAO;AACtB,WAAO,WAAW,OAAO;AAAA,EAC3B,OAAO;AACL,WAAO,SAAS,OAAO;AACvB,WAAO,YAAY,OAAO;AAAA,EAC5B;AACA,MAAI;AACJ,MAAI,CAAC,YAAY,KAAK,SAAS,EAAE,uBAAuB,GAAG;AACzD,iBAAyB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,WAAW,GAAG,QAAQ,2BAA2B,IAAI,OAAO,aAAa,GAAG,QAAQ,gCAAgC,IAAI,GAAG,QAAQ,gCAAgC;AAAA,MACrK;AAAA,IACF;AAAA,EACF;AACA,MAAI,UAAU,GAAG;AACf,eAAuB;AAAA,MACrB;AAAA,MACA;AAAA,QACE;AAAA,QACA,OAAO;AAAA,QACP,KAAK;AAAA,QACL,oBAAoB,KAAK,QAAQ,IAAI,QAAQ,QAAQ;AAAA,QACrD;AAAA,QACA,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF,OAAO;AACL,UAAM,UAAU,CAAC;AACjB,QAAI,KAAK,eAAe,MAAM,YAAY,MAAM;AAC9C,cAAQ,SAAS;AACjB,cAAQ,QAAQ,OAAO,QAAQ;AAC/B,cAAQ,SAAS;AAAA,IACnB,OAAO;AACL,cAAQ,SAAS,OAAO,QAAQ;AAChC,cAAQ,QAAQ;AAChB,cAAQ,SAAS;AAAA,IACnB;AACA,UAAM,aAAa,GAAG,QAAQ,0BAA0B;AACxD,eAAuB;AAAA,MACrB;AAAA,MACA;AAAA,QACE;AAAA,QACA,OAAO;AAAA,QACP,KAAK;AAAA,QACL,oBAAoB,KAAK,QAAQ,IAAI,QAAQ,QAAQ;AAAA,QACrD;AAAA,QACA,cAA0B;AAAA,UACxB;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,UAAU,OAAO;AACxB,QAAM,EAAE,QAAQ,QAAQ,KAAK,IAAI;AACjC,QAAM,UAAgB,aAAO,IAAI;AACjC,QAAM,QAAc,aAAO,MAAM;AACjC,EAAM,sBAAgB,MAAM;AAC1B,UAAM,cAAc,OAAO,sBAAsB,QAAQ,OAAO;AAChE,QAAI,CAAC,MAAM,YAAY,CAAC,KAAK,YAAY,QAAQ,GAAG;AAClD,UAAI,CAAC,OAAO,eAAe,EAAE,OAAO,WAAW,GAAG;AAChD,eAAO,eAAe,WAAW;AACjC,YAAI,kBAAkB;AACpB,cAAI,MAAM,SAAS;AACjB,yBAAa,MAAM,OAAO;AAAA,UAC5B;AACA,gBAAM,UAAU,WAAW,MAAM;AAC/B,mBAAO,eAAe,yBAAyB,WAAW;AAC1D,kBAAM,UAAU;AAAA,UAClB,GAAG,EAAE;AAAA,QACP,OAAO;AACL,iBAAO,eAAe,yBAAyB,WAAW;AAAA,QAC5D;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,aAAa;AACjB,QAAM,SAAS,CAAC;AAChB,MAAI,OAAO,eAAe,MAAM,YAAY,MAAM;AAChD,WAAO,QAAQ,OAAO,QAAQ;AAC9B,WAAO,WAAW,OAAO,WAAW;AACpC,WAAO,WAAW,OAAO,WAAW;AAAA,EACtC,OAAO;AACL,WAAO,SAAS,OAAO,QAAQ;AAC/B,WAAO,YAAY,OAAO,WAAW;AACrC,WAAO,YAAY,OAAO,WAAW;AACrC,iBAAa;AAAA,EACf;AACA,SAAO,UAAU,OAAO,SAAS;AACjC,QAAM,YAAY,OAAO,aAAa,QAAQ,+BAA+B;AAC7E,MAAI,OAAO,YAAY,MAAM,aAAa,QAAQ,OAAO,YAAY,MAAM,aAAa,KAAK;AAC3F,eAAuB,yBAAK,6BAAU,EAAE,UAAU;AAAA,UAChC,wBAAI,OAAO,EAAE,KAAK,SAAS,OAAO,QAAQ,UAAU,CAAC;AAAA,MACrE,YAAwB,wBAAI,UAAU,EAAE,QAAQ,MAAM,QAAQ,OAAO,GAAG,WAAW,CAAC;AAAA,IACtF,EAAE,CAAC;AAAA,EACL,OAAO;AACL,eAAuB,yBAAK,6BAAU,EAAE,UAAU;AAAA,MAChD,YAAwB,wBAAI,UAAU,EAAE,QAAQ,MAAM,QAAQ,OAAO,GAAG,WAAW,CAAC;AAAA,UACpE,wBAAI,OAAO,EAAE,KAAK,SAAS,OAAO,QAAQ,UAAU,CAAC;AAAA,IACvE,EAAE,CAAC;AAAA,EACL;AACF;AACA,IAAI,cAA8B,CAAC,gBAAgB;AACjD,cAAY,YAAY,SAAS,IAAI,CAAC,IAAI;AAC1C,cAAY,YAAY,QAAQ,IAAI,CAAC,IAAI;AACzC,cAAY,YAAY,UAAU,IAAI,CAAC,IAAI;AAC3C,SAAO;AACT,GAAG,cAAc,CAAC,CAAC;AACnB,IAAM,eAAe,CAAC,UAAU;AAC9B,QAAM,EAAE,QAAQ,MAAM,UAAU,QAAQ,OAAO,KAAK,IAAI;AACxD,QAAM,UAAgB,aAAO,IAAI;AACjC,QAAM,aAAmB,aAAO,IAAI;AACpC,QAAM,cAAc,CAAC,UAAU;AAC7B,QAAI,KAAK,aAAa,GAAG;AACvB,YAAM,gBAAgB;AACtB,aAAO,YAAY,MAAM,aAAa,IAAI;AAAA,IAC5C,OAAO;AACL,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AACA,QAAM,YAAY,CAAC,UAAU;AAC3B,UAAM,gBAAgB;AACtB,WAAO,cAAc;AAAA,EACvB;AACA,QAAM,kBAAkB,CAAC,UAAU;AACjC,QAAI,gBAAgB,KAAK,GAAG;AAC1B,aAAO,cAAc,MAAM,KAAK;AAAA,IAClC;AAAA,EACF;AACA,QAAM,gBAAgB,CAAC,UAAU;AAC/B,WAAO,gBAAgB,MAAM,KAAK;AAAA,EACpC;AACA,QAAM,UAAU,MAAM;AACpB,WAAO,SAAS,QAAQ,UAAU,KAAK,MAAM,CAAC,CAAC;AAAA,EACjD;AACA,QAAM,aAAa,MAAM;AACvB,UAAM,YAAY,KAAK,aAAa;AACpC,QAAI,YAAY,cAAc,WAAW,QAAQ;AAC/C,aAAO;AAAA,IACT;AACA,QAAI,cAAc,WAAW,SAAS;AACpC,UAAI,OAAO,cAAc,OAAO,WAAW,oCAAoC,EAAE,SAAS;AACxF,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,UAAU,CAAC,UAAU;AACzB,QAAI,WAAW,GAAG;AAChB,aAAO,SAAS,QAAQ,UAAU,KAAK,MAAM,CAAC,CAAC;AAC/C,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AACA,QAAM,qBAAqB,CAAC,UAAU;AACpC,UAAM,gBAAgB;AAAA,EACxB;AACA,EAAM,sBAAgB,MAAM;AAC1B,SAAK,WAAW,OAAO,sBAAsB,QAAQ,OAAO,CAAC;AAC7D,QAAI,OAAO,cAAc,MAAM,MAAM;AACnC,iBAAW,QAAQ,OAAO;AAAA,IAC5B;AAAA,EACF,CAAC;AACD,QAAM,uBAAuB,CAAC,UAAU;AACtC,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,oBAAoB,CAAC,UAAU;AACnC,QAAI,MAAM,SAAS,UAAU;AAC3B,aAAO,cAAc,MAAM;AAAA,IAC7B,WAAW,MAAM,SAAS,WAAW,MAAM,SAAS,eAAe;AACjE,aAAO,cAAc,MAAM;AAC3B,aAAO,SAAS,QAAQ,UAAU,KAAK,MAAM,GAAG,MAAM,OAAO,KAAK,CAAC;AAAA,IACrE;AAAA,EACF;AACA,QAAM,KAAK,OAAO;AAClB,MAAI,aAAa,GAAG,QAAQ,yBAAyB,IAAI,MAAM,GAAG,QAAQ,6BAA6B,MAAM;AAC7G,MAAI,UAAU;AACZ,kBAAc,MAAM,GAAG,QAAQ,mCAAmC;AAAA,EACpE,OAAO;AACL,kBAAc,MAAM,GAAG,QAAQ,qCAAqC;AAAA,EACtE;AACA,MAAI,KAAK,aAAa,MAAM,QAAQ;AAClC,kBAAc,MAAM,KAAK,aAAa;AAAA,EACxC;AACA,MAAI,YAAY;AAChB,MAAI,KAAK,SAAS,EAAE,0BAA0B,MAAM,OAAO;AACzD,QAAI,WAAW,QAAQ;AACrB,kBAAY;AAAA,IACd,WAAW,WAAW,SAAS;AAC7B,kBAAY;AAAA,IACd;AAAA,EACF;AACA,QAAM,cAAc,iBAAiB,QAAQ,MAAM,SAAS;AAC5D,MAAI,UAAU,YAAY,cAA0B,wBAAI,OAAO,EAAE,WAAW,GAAG,QAAQ,iCAAiC,GAAG,UAAU,YAAY,QAAQ,CAAC,IAAI;AAC9J,QAAM,UAAU,YAAY,cAA0B,wBAAI,OAAO,EAAE,WAAW,GAAG,QAAQ,iCAAiC,GAAG,UAAU,YAAY,QAAQ,CAAC,IAAI;AAChK,MAAI,OAAO,cAAc,MAAM,MAAM;AACnC,kBAA0B;AAAA,MACxB;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,WAAW,GAAG,QAAQ,8BAA8B;AAAA,QACpD,oBAAoB,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,WAAW;AAAA,QACX,cAAc,KAAK,QAAQ;AAAA,QAC3B,WAAW;AAAA,QACX,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK,cAAc,GAAG;AACxB,UAAM,aAAa,OAAO,SAAS,UAAU,SAAS;AACtD,gBAAY,QAAQ;AAAA,UACF;AAAA,QACd;AAAA,QACA;AAAA,UACE,oBAAoB,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,WAAW,GAAG,QAAQ,kCAAkC;AAAA,UACxD,eAAe;AAAA,UACf,SAAS;AAAA,UACT,UAAU,OAAO,MAAM,UAAU,aAAa,MAAM,MAAM,IAAI,IAAI,MAAM;AAAA,QAC1E;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,MACA,OAAO,KAAK,YAAY;AAAA,MACxB,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,iBAAiB,CAAC,UAAU;AAChC,QAAM,EAAE,QAAQ,KAAK,IAAI;AACzB,QAAM,KAAK,OAAO;AAClB,QAAM,aAAa,GAAG,QAAQ,4BAA4B;AAC1D,QAAM,cAAc,iBAAiB,QAAQ,IAAI;AACjD,QAAM,UAAU,YAAY,cAA0B,wBAAI,OAAO,EAAE,WAAW,GAAG,QAAQ,8BAA8B,GAAG,UAAU,YAAY,QAAQ,CAAC,IAAI,KAAK,uBAAuB;AACzL,QAAM,UAAU,YAAY,cAA0B,wBAAI,OAAO,EAAE,WAAW,GAAG,QAAQ,8BAA8B,GAAG,UAAU,YAAY,QAAQ,CAAC,IAAI;AAC7J,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,OAAO,KAAK,YAAY;AAAA,MACxB,UAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,UAAU,gBAAgB,YAAY,OAAO,UAAU,QAAQ;AACtE,QAAM,YAAY,OAAO,WAAW;AACpC,QAAM,kBAAkB,OAAO;AAC/B,QAAM,kBAAkB,eAAe;AACvC,QAAM,cAAc,eAAe,sBAAsB;AACzD,QAAM,cAAc,aAAa,OAAO,SAAS,UAAU,sBAAsB,MAAM,IAAI,QAAQ,GAAG,GAAG,KAAK,GAAG;AACjH,QAAM,MAAM,gBAAgB,cAAc,KAAK;AAC/C,MAAI,YAAY,gBAAgB,QAAQ,gCAAgC;AACxE,MAAI,YAAY,OAAO,WAAW,OAAO,WAAW,QAAQ,GAAG;AAC7D,QAAI,MAAM,OAAO,YAAY,OAAO,WAAW,OAAO;AAAA,EACxD,OAAO;AACL,QAAI,MAAM,QAAQ,WAAW,QAAQ,YAAY,QAAQ;AAAA,EAC3D;AACA,MAAI,YAAY,MAAM,WAAW,MAAM,WAAW,SAAS,GAAG;AAC5D,QAAI,MAAM,MAAM,YAAY,MAAM,WAAW,MAAM;AAAA,EACrD,OAAO;AACL,QAAI,MAAM,SAAS,WAAW,SAAS,YAAY,SAAS;AAAA,EAC9D;AACA,SAAO,YAAY,IAAI;AACvB,MAAI,WAAW;AACb,cAAU,YAAY,GAAG;AAAA,EAC3B;AACA,QAAM,SAAS,MAAM;AACnB,WAAO,oBAAoB;AAC3B,WAAO,YAAY,KAAK;AACxB,QAAI,WAAW;AACb,gBAAU,YAAY,GAAG;AAAA,IAC3B;AACA,QAAI,oBAAoB,eAAe,oBAAoB;AAC3D,oBAAgB,oBAAoB,eAAe,gBAAgB;AAAA,EACrE;AACA,QAAM,uBAAuB,CAAC,UAAU;AACtC,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,mBAAmB,CAAC,WAAW;AACnC,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,eAAe,oBAAoB;AACxD,kBAAgB,iBAAiB,eAAe,gBAAgB;AAChE,SAAO,wBAAoC;AAAA,IACzC;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,GAAG;AACR;AACA,IAAM,YAAY,CAAC,UAAU;AAC3B,QAAM,EAAE,YAAY,OAAO,QAAQ,UAAU,iBAAiB,OAAO,IAAI;AACzE,QAAM,aAAS,qBAAO,IAAI;AAC1B,8BAAU,MAAM;AACd,QAAI,OAAO,SAAS;AAClB,aAAO,QAAQ,MAAM;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,cAAc,CAAC,MAAM,UAAU;AACnC,aAAS,IAAI;AACb,WAAO;AACP,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,cAAc,CAAC,OAAO,SAAS;AACnC,UAAM,gBAAgB;AACtB,WAAO,YAAY,MAAM,aAAa,IAAI;AAC1C,eAAW,MAAM;AACf,aAAO;AAAA,IACT,GAAG,CAAC;AAAA,EACN;AACA,QAAM,YAAY,CAAC,UAAU;AAC3B,WAAO,cAAc;AAAA,EACvB;AACA,QAAM,gBAAgB,CAAC,UAAU;AAC/B,QAAI,MAAM,QAAQ,UAAU;AAC1B,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,eAAe,MAAM;AAAA,IACzB,CAAC,MAAM,MAAM;AACX,UAAI,UAAU,gBAAgB,QAAQ,2BAA2B;AACjE,UAAI,WAAW,YAAY,MAAM,KAAK,OAAO;AAC3C,mBAAW,MAAM,gBAAgB,QAAQ,qCAAqC;AAAA,MAChF;AACA,iBAAuB;AAAA,QACrB;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,oBAAoB,mBAAmB;AAAA,UACvC,SAAS,CAAC,UAAU,YAAY,MAAM,KAAK;AAAA,UAC3C,WAAW;AAAA,UACX,aAAa,CAAC,MAAM,YAAY,GAAG,KAAK,IAAI;AAAA,UAC5C;AAAA,UACA,OAAO,KAAK,KAAK,YAAY;AAAA,UAC7B,cAA0B;AAAA,YACxB;AAAA,YACA;AAAA,cACE,MAAM,KAAK;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,KAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW,gBAAgB,QAAQ,sBAAsB;AAAA,MACzD,KAAK;AAAA,MACL,UAAU;AAAA,MACV,WAAW;AAAA,MACX,oBAAoB;AAAA,MACpB,UAAU;AAAA,IACZ;AAAA,EACF;AACF;AACA,IAAM,iBAAiB,CAAC,QAAQ,MAAM,aAAa,aAAa,eAAe,iBAAiB;AAC9F,QAAM,CAAC,YAAY,aAAa,IAAU,eAAS,CAAC,CAAC;AACrD,QAAM,CAAC,kBAAkB,iBAAiB,IAAU,eAAS,KAAK;AAClE,QAAM,CAAC,qBAAqB,oBAAoB,IAAU,eAAS,KAAK;AACxE,QAAM,UAAgB,aAAO,IAAI;AACjC,QAAM,4BAAkC,aAAO,KAAK;AACpD,QAAM,2BAAiC,aAAO,MAAM;AACpD,QAAM,gBAAsB,aAAO,CAAC,CAAC;AACrC,QAAM,mBAAyB,aAAO,CAAC;AACvC,QAAM,mBAAyB,aAAO,KAAK;AAC3C,gBAAc,UAAU;AACxB,EAAM,sBAAgB,MAAM;AAC1B,QAAI,YAAY,SAAS;AACvB,wBAAkB,CAAC;AAAA,IACrB;AAAA,EACF,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC;AACjB,EAAM,sBAAgB,MAAM;AAC1B,8BAA0B,UAAU;AAAA,EACtC,GAAG,CAAC,KAAK,gBAAgB,GAAG,KAAK,QAAQ,EAAE,OAAO,KAAK,QAAQ,EAAE,MAAM,CAAC;AACxE,EAAM,sBAAgB,MAAM;AAC1B,qBAAiB;AACjB,QAAI,0BAA0B,YAAY,OAAO;AAC/C,qBAAe;AAAA,IACjB;AACA,wBAAoB;AACpB,qBAAiB;AAAA,EACnB,CAAC;AACD,EAAM,gBAAU,MAAM;AACpB,QAAI;AACJ,KAAC,KAAK,QAAQ,YAAY,OAAO,SAAS,GAAG,iBAAiB,SAAS,SAAS,EAAE,SAAS,MAAM,CAAC;AAClG,WAAO,MAAM;AACX,UAAI;AACJ,OAAC,MAAM,QAAQ,YAAY,OAAO,SAAS,IAAI,oBAAoB,SAAS,OAAO;AAAA,IACrF;AAAA,EACF,GAAG,CAAC,QAAQ,OAAO,CAAC;AACpB,QAAM,UAAU,CAAC,UAAU;AACzB,UAAM,eAAe;AAAA,EACvB;AACA,WAAS,iBAAiB;AACxB,UAAM,kBAAkB,KAAK,gBAAgB;AAC7C,QAAI,mBAAmB,YAAY,SAAS;AAC1C,YAAM,YAAY,OAAO,sBAAsB,YAAY,OAAO;AAClE,YAAM,eAAe,gBAAgB,WAAW;AAChD,UAAI,QAAQ,QAAQ,SAAS,IAAI,QAAQ,YAAY;AACrD,UAAI,QAAQ,KAAK,QAAQ,YAAY,IAAI,QAAQ,SAAS,GAAG;AAC3D,0BAAkB,kBAAkB,YAAY,OAAO,IAAI,KAAK;AAChE,yBAAiB,UAAU;AAAA,MAC7B,OAAO;AACL,gBAAQ,OAAO,YAAY,IAAI,OAAO,SAAS;AAC/C,YAAI,QAAQ,GAAG;AACb,4BAAkB,kBAAkB,YAAY,OAAO,IAAI,KAAK;AAChE,2BAAiB,UAAU;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,sBAAsB,MAAM;AAChC,QAAI,YAAY,WAAW,cAAc,SAAS;AAChD,YAAM,IAAI,YAAY;AACtB,YAAM,IAAI,cAAc;AACxB,YAAM,OAAO,eAAe,CAAC;AAC7B,YAAM,aAAa,cAAc,CAAC;AAClC,YAAM,WAAW,kBAAkB,CAAC;AACpC,UAAI,aAAa,QAAQ,aAAa,GAAG;AACvC,YAAI,YAAY,OAAO,OAAO;AAC9B,YAAI,SAAS;AACb,YAAI,YAAY,IAAI;AAClB,mBAAS,KAAK;AACd,sBAAY;AAAA,QACd;AACA,cAAM,WAAW,YAAY,OAAO,UAAU;AAC9C,YAAI,gBAAgB,YAAY,MAAM;AACpC,YAAE,MAAM,QAAQ,YAAY;AAC5B,YAAE,MAAM,OAAO,WAAW;AAAA,QAC5B,OAAO;AACL,YAAE,MAAM,SAAS,YAAY;AAC7B,YAAE,MAAM,MAAM,WAAW;AAAA,QAC3B;AACA,UAAE,MAAM,UAAU;AAAA,MACpB,OAAO;AACL,UAAE,MAAM,UAAU;AAAA,MACpB;AACA,UAAI,gBAAgB,YAAY,MAAM;AACpC,UAAE,MAAM,SAAS;AAAA,MACnB,OAAO;AACL,UAAE,MAAM,QAAQ;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACA,QAAM,mBAAmB,MAAM;AAC7B,UAAM,gBAAgB,eAAe;AACrC,UAAM,aAAa,cAAc,SAAS;AAC1C,QAAI,eAAe,kBAAkB;AACnC,wBAAkB,UAAU;AAAA,IAC9B;AACA,QAAI,yBAAyB,YAAY,QAAQ;AAC/C,+BAAyB,UAAU,WAAW,MAAM;AAClD,cAAM,iBAAiB,eAAe;AACtC,YAAI,CAAC,YAAY,gBAAgB,cAAc,OAAO,GAAG;AACvD,wBAAc,cAAc;AAAA,QAC9B;AACA,iCAAyB,UAAU;AAAA,MACrC,GAAG,GAAG;AAAA,IACR;AAAA,EACF;AACA,QAAM,WAAW,MAAM;AACrB,QAAI,CAAC,iBAAiB,SAAS;AAC7B,gCAA0B,UAAU;AAAA,IACtC;AACA,qBAAiB,UAAU;AAC3B,wBAAoB;AACpB,qBAAiB;AAAA,EACnB;AACA,QAAM,sBAAsB,CAAC,UAAU;AACrC,QAAI;AACJ,UAAM,gBAAgB;AACtB,kBAAc,QAAQ,kBAAkB,MAAM,SAAS;AACvD,UAAM,KAAK,KAAK,cAAc,YAAY,OAAO,SAAS,GAAG,sBAAsB;AACnF,QAAI,gBAAgB,YAAY,MAAM;AACpC,uBAAiB,UAAU,MAAM,UAAU,EAAE;AAAA,IAC/C,OAAO;AACL,uBAAiB,UAAU,MAAM,UAAU,EAAE;AAAA,IAC/C;AACA,cAAU,MAAM,cAAc,eAAe,OAAO,YAAY,WAAW,YAAY;AAAA,EACzF;AACA,QAAM,aAAa,CAAC,GAAG,MAAM;AAC3B,QAAI,YAAY,WAAW,cAAc,SAAS;AAChD,YAAM,IAAI,YAAY;AACtB,YAAM,IAAI,cAAc;AACxB,YAAM,OAAO,eAAe,CAAC;AAC7B,YAAM,aAAa,cAAc,CAAC;AAClC,YAAM,YAAY,eAAe,CAAC;AAClC,YAAM,IAAI,EAAE,sBAAsB;AAClC,UAAI,QAAQ;AACZ,UAAI,gBAAgB,YAAY,MAAM;AACpC,gBAAQ,IAAI,EAAE,IAAI,iBAAiB;AAAA,MACrC,OAAO;AACL,gBAAQ,IAAI,EAAE,IAAI,iBAAiB;AAAA,MACrC;AACA,cAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,aAAa,WAAW,KAAK,CAAC;AAC3D,UAAI,OAAO,GAAG;AACZ,cAAM,YAAY,QAAQ,aAAa;AACvC,0BAAkB,SAAS;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AACA,QAAM,YAAY,MAAM;AAAA,EACxB;AACA,QAAM,eAAe,MAAM;AAAA,EAC3B;AACA,QAAM,mBAAmB,MAAM;AAC7B,QAAI,YAAY,SAAS;AACvB,YAAM,QAAQ,YAAY;AAC1B,YAAM,eAAe,MAAM;AAC3B,YAAM,SAAS,sBAAsB,KAAK;AAC1C,YAAM,OAAO,eAAe,YAAY,IAAI,SAAS,eAAe,YAAY,OAAO;AACvF,UAAI,SAAS,qBAAqB;AAChC,6BAAqB,IAAI;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAiB,MAAM;AAC3B,UAAM,SAAS,CAAC;AAChB,QAAI,YAAY,SAAS;AACvB,YAAM,QAAQ,YAAY;AAC1B,YAAM,YAAY,MAAM,sBAAsB;AAC9C,YAAM,cAAc,QAAQ,SAAS,IAAI;AACzC,YAAM,aAAa,OAAO,SAAS,IAAI;AACvC,YAAM,eAAe,MAAM;AAC3B,UAAI,IAAI;AACR,YAAM,KAAK,aAAa,QAAQ,EAAE,QAAQ,CAAC,UAAU;AACnD,cAAM,UAAU,MAAM,sBAAsB;AAC5C,YAAI,MAAM,UAAU,SAAS,YAAY,GAAG;AAC1C,cAAI,QAAQ,OAAO,IAAI,eAAe,OAAO,OAAO,IAAI,YAAY;AAClE,mBAAO,KAAK,CAAC;AAAA,UACf;AACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACA,QAAM,eAAe,CAAC,UAAU;AAC9B,QAAI,YAAY,SAAS;AACvB,UAAI,KAAK,YAAY,EAAE,WAAW,EAAG;AACrC,UAAI,QAAQ;AACZ,UAAI,KAAK,IAAI,MAAM,MAAM,IAAI,GAAG;AAC9B,gBAAQ,CAAC,MAAM;AACf,YAAI,MAAM,cAAc,GAAG;AACzB,mBAAS;AAAA,QACX;AACA,cAAM,SAAS,kBAAkB,YAAY,OAAO,IAAI;AACxD,cAAM,YAAY,cAAc,YAAY,OAAO,IAAI,eAAe,YAAY,OAAO;AACzF,cAAM,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,WAAW,MAAM,CAAC;AACjD,0BAAkB,CAAC;AACnB,cAAM,gBAAgB;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACA,QAAM,UAAU,CAAC,SAAS;AACxB,QAAI,gBAAgB,YAAY,MAAM;AACpC,aAAO,KAAK;AAAA,IACd,OAAO;AACL,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AACA,QAAM,SAAS,CAAC,SAAS;AACvB,QAAI,gBAAgB,YAAY,MAAM;AACpC,aAAO,KAAK;AAAA,IACd,OAAO;AACL,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AACA,QAAM,iBAAiB,CAAC,QAAQ;AAC9B,QAAI,gBAAgB,YAAY,MAAM;AACpC,aAAO,IAAI;AAAA,IACb,OAAO;AACL,aAAO,IAAI;AAAA,IACb;AAAA,EACF;AACA,QAAM,UAAU,CAAC,SAAS;AACxB,QAAI,gBAAgB,YAAY,MAAM;AACpC,aAAO,KAAK;AAAA,IACd,OAAO;AACL,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AACA,QAAM,gBAAgB,CAAC,QAAQ;AAC7B,QAAI,gBAAgB,YAAY,MAAM;AACpC,aAAO,IAAI;AAAA,IACb,OAAO;AACL,aAAO,IAAI;AAAA,IACb;AAAA,EACF;AACA,QAAM,oBAAoB,CAAC,MAAM;AAC/B,QAAI,gBAAgB,YAAY,MAAM;AACpC,kBAAY,QAAQ,aAAa;AAAA,IACnC,OAAO;AACL,kBAAY,QAAQ,YAAY;AAAA,IAClC;AAAA,EACF;AACA,QAAM,oBAAoB,CAAC,QAAQ;AACjC,QAAI,gBAAgB,YAAY,MAAM;AACpC,aAAO,IAAI;AAAA,IACb,OAAO;AACL,aAAO,IAAI;AAAA,IACb;AAAA,EACF;AACA,SAAO,EAAE,SAAS,2BAA2B,UAAU,qBAAqB,YAAY,cAAc,qBAAqB,iBAAiB;AAC9I;AACA,SAAS,YAAY,MAAM,MAAM;AAC/B,SAAO,KAAK,WAAW,KAAK,UAAU,KAAK,MAAM,CAAC,KAAK,UAAU,QAAQ,KAAK,KAAK,CAAC;AACtF;AACA,IAAM,eAAe,CAAC,UAAU;AAC9B,QAAM,EAAE,QAAQ,QAAQ,KAAK,IAAI;AACjC,QAAM,aAAmB,aAAO,IAAI;AACpC,QAAM,gBAAsB,aAAO,IAAI;AACvC,QAAM,oBAA0B,aAAO,IAAI;AAC3C,QAAM,mBAAyB,aAAO,IAAI;AAC1C,QAAM,mBAAyB,aAAO,IAAI;AAC1C,QAAM,QAAQ,OAAO,SAAS;AAC9B,EAAM,sBAAgB,MAAM;AAC1B,WAAO,iBAAiB,OAAO,sBAAsB,QAAQ,OAAO,CAAC;AAAA,EACvE,CAAC;AACD,QAAM,EAAE,SAAS,2BAA2B,UAAU,qBAAqB,YAAY,cAAc,qBAAqB,iBAAiB,IAAI;AAAA,IAC7I;AAAA,IACA;AAAA,IACA,YAAY,KAAK,OAAO,eAAe,CAAC;AAAA,IACxC;AAAA,IACA;AAAA,IACA,OAAO,aAAa,QAAQ,yBAAyB;AAAA,EACvD;AACA,QAAM,kBAAkB,CAAC,UAAU;AACjC,QAAI,gBAAgB,KAAK,GAAG;AAC1B,aAAO,cAAc,QAAQ,KAAK;AAAA,IACpC;AAAA,EACF;AACA,QAAM,gBAAgB,CAAC,UAAU;AAC/B,WAAO,gBAAgB,QAAQ,KAAK;AAAA,EACtC;AACA,QAAM,yBAAyB,CAAC,UAAU;AACxC,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,kBAAkB,CAAC,UAAU;AACjC,UAAM,WAAW,OAAO,oBAAoB;AAC5C,UAAM,QAAQ,WAAW,IAAI,CAAC,MAAM;AAClC,aAAO,EAAE,OAAO,GAAG,MAAM,OAAO,YAAY,EAAE,CAAC,EAAE;AAAA,IACnD,CAAC;AACD,QAAI,aAAa,QAAQ;AACvB,eAAS,QAAQ,OAAO,OAAO,oBAAoB;AAAA,IACrD,OAAO;AACL,YAAM,UAAU,kBAAkB;AAClC;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,uBAAuB,CAAC,SAAS;AACrC,WAAO,SAAS,QAAQ,UAAU,KAAK,KAAK,MAAM,CAAC,CAAC;AACpD,8BAA0B,UAAU;AAAA,EACtC;AACA,QAAM,cAAc,CAAC,UAAU;AAC7B,UAAM,kBAAkB,OAAO,YAAY,EAAE,OAAO,YAAY,CAAC;AACjE,QAAI,oBAAoB,QAAQ;AAC9B,aAAO,SAAS,QAAQ,UAAU,gBAAgB,MAAM,CAAC,CAAC;AAAA,IAC5D;AACA,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,KAAK,OAAO;AAClB,QAAM,aAAa,CAAC;AACpB,QAAM,YAAY,CAAC,MAAM;AACvB,UAAM,aAAa,OAAO,YAAY,MAAM;AAC5C,UAAM,QAAQ,OAAO,YAAY,EAAE,CAAC;AACpC,eAAW;AAAA,UACO;AAAA,QACd;AAAA,QACA;AAAA,UACE;AAAA,UACA,QAAQ,OAAO,YAAY,EAAE,QAAQ;AAAA,UACrC,MAAM;AAAA,UACN,MAAM,OAAO,QAAQ,IAAI,QAAQ;AAAA,UACjC,UAAU;AAAA,UACV;AAAA,QACF;AAAA,QACA,MAAM,MAAM;AAAA,MACd;AAAA,IACF;AACA,QAAI,IAAI,OAAO,YAAY,EAAE,SAAS,GAAG;AACvC,iBAAW;AAAA,YACO,wBAAI,OAAO,EAAE,WAAW,GAAG,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC;AAAA,MACrG;AAAA,IACF;AAAA,EACF;AACA,WAAS,IAAI,GAAG,IAAI,OAAO,YAAY,EAAE,QAAQ,KAAK;AACpD,cAAU,CAAC;AAAA,EACb;AACA,MAAI,gBAAgB,GAAG,QAAQ,kBAAkB,IAAI,MAAM,GAAG,QAAQ,sBAAsB,OAAO,YAAY,EAAE,QAAQ,CAAC;AAC1H,MAAI,OAAO,aAAa,MAAM,QAAQ;AACpC,qBAAiB,MAAM,OAAO,aAAa;AAAA,EAC7C;AACA,MAAI,UAAU;AACd,MAAI,UAAU,CAAC;AACf,MAAI,gBAAgB,CAAC;AACrB,QAAM,cAAc,EAAE,SAAS,SAAS,eAAe,kBAAkB,OAAO;AAChF,SAAO,gBAAgB,QAAQ,WAAW;AAC1C,YAAU,YAAY;AACtB,kBAAgB,YAAY;AAC5B,YAAU,YAAY;AACtB,MAAI,YAAY,qBAAqB,QAAQ;AAC3C,gBAAY,mBAAmB,cAAc;AAAA,EAC/C;AACA,MAAI,cAAc,SAAS,GAAG;AAC5B,QAAI,qBAAqB;AACvB,gBAAU,CAAC,GAAG,eAAe,GAAG,OAAO;AAAA,IACzC,OAAO;AACL,iBAAW,SAAqB;AAAA,QAC9B;AAAA,QACA;AAAA,UACE,KAAK;AAAA,UACL,eAAe;AAAA,UACf,aAAa,CAAC,MAAM;AAClB,cAAE,eAAe;AAAA,UACnB;AAAA,UACA,WAAW,GAAG,QAAQ,gDAAgD;AAAA,UACtE,UAAU;AAAA,QACZ;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,MAAI,kBAAkB;AACpB,UAAM,gBAAgB,OAAO,SAAS,UAAU,qBAAqB;AACrE,QAAI;AACJ,QAAI,OAAO,MAAM,SAAS,YAAY;AACpC,YAAM,QAAQ,WAAW,IAAI,CAAC,MAAM;AAClC,eAAO,EAAE,OAAO,GAAG,MAAM,OAAO,YAAY,EAAE,CAAC,EAAE;AAAA,MACnD,CAAC;AACD,wBAAkB,MAAM,KAAK,QAAQ,KAAK;AAAA,IAC5C,OAAO;AACL,4BAAkC,yBAAK,6BAAU,EAAE,UAAU;AAAA,QAC3D,MAAM;AAAA,YACU,wBAAI,OAAO,EAAE,WAAW,GAAG,QAAQ,qCAAqC,GAAG,UAAU,WAAW,SAAS,IAAI,WAAW,SAAS,GAAG,CAAC;AAAA,MACvJ,EAAE,CAAC;AAAA,IACL;AACA,YAAQ;AAAA,MACN,KAAK,IAAI,YAAY,kBAAkB,QAAQ,MAAM;AAAA,MACrD;AAAA,UACgB;AAAA,QACd;AAAA,QACA;AAAA,UACE,KAAK;AAAA,UACL,WAAW,GAAG,QAAQ,iCAAiC,IAAI,MAAM,GAAG,QAAQ,0CAA0C,IAAI,MAAM,GAAG,QAAQ,8CAA8C,OAAO,YAAY,EAAE,QAAQ,CAAC;AAAA,UACvN,OAAO;AAAA,UACP,SAAS;AAAA,UACT,eAAe;AAAA,UACf,UAAU;AAAA,QACZ;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,gBAAgB,OAAO,YAAY;AACzC,MAAI,kBAAkB,IAAI;AACxB,UAAM,kBAAkB,OAAO,YAAY,EAAE,aAAa;AAC1D,QAAI,oBAAoB,UAAU,OAAO,iBAAiB,KAAK,gBAAgB,eAAe,GAAG;AAC/F,YAAM,cAAc,OAAO,SAAS,UAAU,UAAU;AACxD,cAAQ;AAAA,YACU;AAAA,UACd;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,WAAW,GAAG,QAAQ,iCAAiC,IAAI,MAAM,GAAG,QAAQ,uCAAuC;AAAA,YACnH,SAAS;AAAA,YACT,eAAe;AAAA,YACf,UAAU,OAAO,MAAM,WAAW,aAAa,MAAM,OAAO,eAAe,IAAI,MAAM;AAAA,UACvF;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,cAA0B,wBAAI,OAAO,EAAE,KAAK,YAAY,WAAW,GAAG,QAAQ,0BAA0B,IAAI,MAAM,GAAG,QAAQ,8BAA8B,OAAO,YAAY,EAAE,QAAQ,CAAC,GAAG,UAAU,QAAQ,GAAG,SAAS;AAChO,MAAI,aAAa,CAAC;AAClB,MAAI,aAAa,CAAC;AAClB,QAAM,eAAe,OAAO;AAC5B,MAAI,OAAO,YAAY,MAAM,aAAa,MAAM;AAC9C,iBAAa,EAAE,OAAO,QAAQ,KAAK,EAAE;AACrC,iBAAa,EAAE,OAAO,cAAc,WAAW,OAAO;AAAA,EACxD,WAAW,OAAO,YAAY,MAAM,aAAa,OAAO;AACtD,iBAAa,EAAE,MAAM,QAAQ,KAAK,EAAE;AACpC,iBAAa,EAAE,OAAO,cAAc,WAAW,OAAO;AAAA,EACxD,OAAO;AACL,iBAAa,EAAE,MAAM,EAAE;AACvB,iBAAa,EAAE,QAAQ,cAAc,WAAW,OAAO;AAAA,EACzD;AACA,MAAI,gBAAgB;AACpB,MAAI,OAAO,qBAAqB,GAAG;AACjC,wBAAgC;AAAA,MAC9B;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,WAAW,GAAG,QAAQ,0BAA0B;AAAA,QAChD,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,MAAI,mBAAmB;AACvB,MAAI,SAAS;AACX,2BAAmC,wBAAI,OAAO,EAAE,WAAW,GAAG,QAAQ,0BAA0B,GAAG,UAAU,QAAQ,CAAC;AAAA,EACxH;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,KAAK;AAAA,MACL,OAAO;AAAA,QACL,SAAS;AAAA,QACT,eAAe,OAAO,eAAe,MAAM,YAAY,OAAO,QAAQ;AAAA,MACxE;AAAA,MACA,WAAW;AAAA,MACX,oBAAoB,OAAO,QAAQ;AAAA,MACnC,SAAS;AAAA,MACT,YAAY;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,MACT,UAAU;AAAA,QACR;AAAA,YACgB,yBAAK,OAAO,EAAE,WAAW,GAAG,QAAQ,oCAAoC,GAAG,UAAU;AAAA,cACnF;AAAA,YACd;AAAA,YACA;AAAA,cACE,KAAK;AAAA,cACL,WAAW,GAAG,QAAQ,wBAAwB,IAAI,MAAM,GAAG,QAAQ,4BAA4B,OAAO,YAAY,EAAE,QAAQ,CAAC;AAAA,cAC7H,OAAO;AAAA,cACP;AAAA,cACA,cAA0B;AAAA,gBACxB;AAAA,gBACA;AAAA,kBACE,OAAO;AAAA,kBACP,WAAW,GAAG,QAAQ,sCAAsC,IAAI,MAAM,GAAG,QAAQ,0CAA0C,OAAO,YAAY,EAAE,QAAQ,CAAC;AAAA,kBACzJ,UAAU;AAAA,gBACZ;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,QACF,EAAE,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,gBAAgB,CAAC,UAAU;AAC/B,QAAM,EAAE,QAAQ,KAAK,IAAI;AACzB,QAAM,UAAgB,aAAO,IAAI;AACjC,EAAM,gBAAU,MAAM;AACpB,SAAK,YAAY,QAAQ,OAAO;AAAA,EAClC,GAAG,CAAC,MAAM,QAAQ,OAAO,CAAC;AAC1B,QAAM,KAAK,OAAO;AAClB,QAAM,aAAa,GAAG,QAAQ,qBAAqB;AACnD,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,KAAK;AAAA,MACL,WAAW;AAAA,MACX,cAA0B,wBAAI,gBAAgB,EAAE,QAAQ,KAAK,GAAG,KAAK,MAAM,CAAC;AAAA,IAC9E;AAAA,EACF;AACF;AACA,IAAM,eAAe,CAAC,UAAU;AAC9B,QAAM,EAAE,OAAO,QAAQ,cAAc,KAAK,eAAe,aAAa,SAAS,IAAI;AACnF,QAAM,eAAqB,aAAO,IAAI;AACtC,QAAM,CAAC,SAAS,UAAU,IAAU,eAAS,MAAM;AACnD,QAAM,WAA2B,oBAAI,IAAI;AACzC,EAAM,sBAAgB,MAAM;AAC1B,QAAI,CAAC,aAAa,SAAS;AACzB,YAAM,WAAW,aAAa;AAC9B,YAAM,OAAO,aAAa;AAC1B,mBAAa,UAAU,OAAO,KAAK,KAAK,UAAU,QAAQ,KAAK,CAAC,QAAQ,KAAK,CAAC,UAAU,KAAK,KAAK,WAAW,KAAK,MAAM,EAAE;AAC1H,UAAI,aAAa,SAAS;AACxB,qBAAa,SAAS,aAAa;AACnC,oBAAY,cAAc,aAAa,OAAO;AAC9C,eAAO,iBAAiB,gBAAgB,MAAM;AAC5C,cAAI,aAAa,SAAS;AACxB,kBAAM,eAAe,aAAa;AAClC,yBAAa,UAAU;AACvB,yBAAa,MAAM;AAAA,UACrB;AAAA,QACF,CAAC;AACD,qBAAa,QAAQ,iBAAiB,QAAQ,MAAM;AAClD,cAAI,aAAa,SAAS;AACxB,yBAAa,QAAQ,MAAM;AAC3B,yBAAa,QAAQ,SAAS,KAAK,OAAO,KAAK,MAAM;AACrD,yBAAa,QAAQ,OAAO,KAAK,GAAG,KAAK,CAAC;AAC1C,kBAAM,iBAAiB,aAAa,QAAQ;AAC5C,2BAAe,QAAQ;AACvB,kBAAM,gBAAgB,eAAe,cAAc,KAAK;AACxD,0BAAc,YAAY,QAAQ;AAClC,2BAAe,KAAK,YAAY,aAAa;AAC7C,uBAAW,gBAAgB,QAAQ,EAAE,KAAK,MAAM;AAC9C,yBAAW,aAAa;AAAA,YAC1B,CAAC;AACD,kBAAM,WAAW,IAAI,iBAAiB,CAAC,kBAAkB,qBAAqB,eAAe,gBAAgB,QAAQ,CAAC;AACtH,qBAAS,QAAQ,SAAS,MAAM,EAAE,WAAW,KAAK,CAAC;AACnD,yBAAa,QAAQ,iBAAiB,gBAAgB,MAAM;AAC1D,kBAAI,aAAa,SAAS;AACxB,8BAAc,YAAY;AAC1B,6BAAa,UAAU;AACvB,yBAAS,WAAW;AAAA,cACtB;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,gBAAQ,KAAK,yBAAyB,GAAG,EAAE;AAC3C,sBAAc,YAAY;AAAA,MAC5B;AAAA,IACF;AACA,WAAO,MAAM;AACX,UAAI;AACJ,UAAI,CAAC,OAAO,SAAS,EAAE,cAAc,EAAE,IAAI,aAAa,QAAQ,GAAG;AACjE,SAAC,KAAK,aAAa,YAAY,OAAO,SAAS,GAAG,MAAM;AACxD,qBAAa,UAAU;AAAA,MACzB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AACL,MAAI,YAAY,QAAQ;AACtB,eAAO,+BAAa,UAAU,OAAO;AAAA,EACvC,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,SAAS,qBAAqB,eAAe,gBAAgB,UAAU;AACrE,aAAW,YAAY,eAAe;AACpC,QAAI,SAAS,SAAS,aAAa;AACjC,iBAAW,YAAY,SAAS,YAAY;AAC1C,YAAI,oBAAoB,mBAAmB,oBAAoB,kBAAkB;AAC/E,oBAAU,gBAAgB,UAAU,QAAQ;AAAA,QAC9C;AAAA,MACF;AACA,iBAAW,WAAW,SAAS,cAAc;AAC3C,YAAI,mBAAmB,mBAAmB,mBAAmB,kBAAkB;AAC7E,gBAAM,cAAc,SAAS,IAAI,OAAO;AACxC,cAAI,aAAa;AACf,2BAAe,KAAK,YAAY,WAAW;AAAA,UAC7C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,WAAW,WAAW,UAAU;AACvC,QAAM,WAAW,CAAC;AAClB,QAAM,gBAAgB,SAAS,iBAAiB,+BAA+B;AAC/E,aAAW,WAAW,eAAe;AACnC,cAAU,WAAW,SAAS,UAAU,QAAQ;AAAA,EAClD;AACA,SAAO,QAAQ,IAAI,QAAQ;AAC7B;AACA,SAAS,UAAU,WAAW,SAAS,UAAU,UAAU;AACzD,MAAI,mBAAmB,iBAAiB;AACtC,UAAM,cAAc,QAAQ,UAAU,IAAI;AAC1C,cAAU,KAAK,YAAY,WAAW;AACtC,aAAS,IAAI,SAAS,WAAW;AACjC,QAAI,UAAU;AACZ,eAAS,KAAK,IAAI,QAAQ,CAAC,YAAY;AACrC,oBAAY,SAAS,MAAM,QAAQ,IAAI;AAAA,MACzC,CAAC,CAAC;AAAA,IACJ;AAAA,EACF,WAAW,mBAAmB,kBAAkB;AAC9C,QAAI;AACF,YAAM,eAAe,QAAQ,UAAU,IAAI;AAC3C,gBAAU,KAAK,YAAY,YAAY;AACvC,eAAS,IAAI,SAAS,YAAY;AAAA,IACpC,SAAS,GAAG;AAAA,IACZ;AAAA,EACF;AACF;AACA,IAAM,QAAQ,EAAE,OAAO,OAAO,QAAQ,OAAO,SAAS,QAAQ,YAAY,SAAS;AACnF,IAAM,YAAY,MAAM;AACtB,aAAuB,yBAAK,OAAO,EAAE,OAAO,8BAA8B,OAAO,SAAS,aAAa,UAAU;AAAA,QAC/F,wBAAI,QAAQ,EAAE,MAAM,QAAQ,GAAG,gBAAgB,CAAC;AAAA,QAChD,wBAAI,QAAQ,EAAE,QAAQ,qBAAqB,MAAM,qBAAqB,GAAG,wGAAwG,CAAC;AAAA,EACpM,EAAE,CAAC;AACL;AACA,IAAM,eAAe,MAAM;AACzB,aAAuB,yBAAK,OAAO,EAAE,OAAO,8BAA8B,OAAO,SAAS,aAAa,MAAM,qBAAqB,UAAU;AAAA,QAC1H,wBAAI,QAAQ,EAAE,GAAG,iBAAiB,MAAM,OAAO,CAAC;AAAA,QAChD,wBAAI,QAAQ,EAAE,QAAQ,qBAAqB,GAAG,iFAAiF,CAAC;AAAA,EAClJ,EAAE,CAAC;AACL;AACA,IAAM,eAAe,MAAM;AACzB,aAAuB,yBAAK,OAAO,EAAE,OAAO,8BAA8B,OAAO,SAAS,aAAa,MAAM,qBAAqB,UAAU;AAAA,QAC1H,wBAAI,QAAQ,EAAE,GAAG,iBAAiB,MAAM,OAAO,CAAC;AAAA,QAChD,wBAAI,QAAQ,EAAE,QAAQ,qBAAqB,GAAG,iBAAiB,CAAC;AAAA,EAClF,EAAE,CAAC;AACL;AACA,IAAM,WAAW,MAAM;AACrB,aAAuB,wBAAI,OAAO,EAAE,OAAO,8BAA8B,OAAO,EAAE,SAAS,SAAS,OAAO,IAAI,QAAQ,GAAG,GAAG,qBAAqB,QAAQ,SAAS,eAAe,cAA0B;AAAA,IAC1M;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACL;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAM,aAAa,MAAM;AACvB;AAAA;AAAA;AAAA;AAAA;AAAA,QAKkB,yBAAK,OAAO,EAAE,OAAO,8BAA8B,OAAO,SAAS,aAAa,MAAM,qBAAqB,UAAU;AAAA,UACnH,wBAAI,QAAQ,EAAE,GAAG,qGAAqG,CAAC;AAAA,UACvH,wBAAI,QAAQ,EAAE,GAAG,uFAAuF,CAAC;AAAA,IAC3H,EAAE,CAAC;AAAA;AAEP;AACA,IAAM,cAAc,MAAM;AACxB,aAAuB,yBAAK,OAAO,EAAE,OAAO,8BAA8B,OAAO,SAAS,aAAa,MAAM,qBAAqB,UAAU;AAAA,QAC1H,wBAAI,QAAQ,EAAE,GAAG,iBAAiB,MAAM,OAAO,CAAC;AAAA,QAChD,wBAAI,QAAQ,EAAE,QAAQ,qBAAqB,GAAG,gFAAgF,CAAC;AAAA,EACjJ,EAAE,CAAC;AACL;AACA,IAAM,eAAe,MAAM;AACzB,aAAuB,wBAAI,OAAO,EAAE,OAAO,8BAA8B,OAAO,QAAQ,QAAQ,SAAS,kBAAkB,OAAO,QAAQ,cAA0B,wBAAI,QAAQ,EAAE,MAAM,qBAAqB,QAAQ,qBAAqB,GAAG,qJAAqJ,CAAC,EAAE,CAAC;AACxY;AACA,IAAM,UAAU,MAAM;AACpB,aAAuB,yBAAK,OAAO,EAAE,OAAO,8BAA8B,OAAO,QAAQ,QAAQ,SAAS,aAAa,MAAM,qBAAqB,UAAU;AAAA,QAC1I,wBAAI,QAAQ,EAAE,GAAG,iBAAiB,MAAM,OAAO,CAAC;AAAA,QAChD,wBAAI,QAAQ,EAAE,QAAQ,qBAAqB,GAAG,sCAAsC,CAAC;AAAA,EACvG,EAAE,CAAC;AACL;AACA,IAAM,WAAW,MAAM;AACrB,aAAuB,wBAAI,OAAO,EAAE,OAAO,8BAA8B,OAAO,QAAQ,QAAQ,OAAO,QAAQ,SAAS,kBAAkB,MAAM,qBAAqB,cAA0B,wBAAI,QAAQ,EAAE,GAAG,uEAAuE,CAAC,EAAE,CAAC;AAC7R;AACA,IAAM,eAAe,CAAC,UAAU;AAC9B,aAAuB,wBAAI,OAAO,EAAE,OAAO,8BAA8B,GAAG,OAAO,OAAO,SAAS,aAAa,MAAM,qBAAqB,cAA0B,yBAAK,KAAK,EAAE,UAAU;AAAA,QACzK,wBAAI,QAAQ,EAAE,GAAG,mBAAmB,MAAM,OAAO,CAAC;AAAA,QAClD,wBAAI,QAAQ,EAAE,GAAG,61BAA61B,CAAC;AAAA,EACj4B,EAAE,CAAC,EAAE,CAAC;AACR;AACA,IAAM,UAAU,CAAC,UAAU;AACzB,QAAM,EAAE,QAAQ,KAAK,IAAI;AACzB,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW,OAAO,aAAa,QAAQ,0BAA0B;AAAA,MACjE,OAAO;AAAA,QACL,SAAS,OAAO,SAAS;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,YAAY,CAAC,UAAU;AAC3B,QAAM,EAAE,QAAQ,MAAM,UAAU,KAAK,IAAI;AACzC,QAAM,UAAgB,aAAO,IAAI;AACjC,QAAM,aAAmB,aAAO,IAAI;AACpC,QAAM,QAAQ,OAAO,SAAS;AAC9B,EAAM,sBAAgB,MAAM;AAC1B,SAAK,WAAW,OAAO,sBAAsB,QAAQ,OAAO,CAAC;AAC7D,QAAI,OAAO,cAAc,MAAM,MAAM;AACnC,iBAAW,QAAQ,OAAO;AAAA,IAC5B;AAAA,EACF,CAAC;AACD,QAAM,cAAc,CAAC,UAAU;AAC7B,QAAI,KAAK,aAAa,GAAG;AACvB,YAAM,gBAAgB;AACtB,aAAO,YAAY,MAAM,aAAa,IAAI;AAAA,IAC5C,OAAO;AACL,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AACA,QAAM,YAAY,CAAC,UAAU;AAC3B,WAAO,cAAc;AAAA,EACvB;AACA,QAAM,kBAAkB,CAAC,UAAU;AACjC,QAAI,gBAAgB,KAAK,GAAG;AAC1B,aAAO,cAAc,MAAM,KAAK;AAAA,IAClC;AAAA,EACF;AACA,QAAM,gBAAgB,CAAC,UAAU;AAC/B,WAAO,gBAAgB,MAAM,KAAK;AAAA,EACpC;AACA,QAAM,UAAU,MAAM;AACpB,WAAO,SAAS,QAAQ,UAAU,KAAK,MAAM,CAAC,CAAC;AAAA,EACjD;AACA,QAAM,gBAAgB,CAAC,UAAU;AAC/B,QAAI,KAAK,eAAe,GAAG;AACzB,eAAS;AACT,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AACA,QAAM,WAAW,MAAM;AACrB,WAAO,cAAc,IAAI;AACzB,WAAO,mBAAmB,EAAE,KAAK,iBAAiB,eAAe,SAAS;AAAA,EAC5E;AACA,QAAM,YAAY,CAAC,UAAU;AAC3B,QAAI,MAAM,WAAW,WAAW,SAAS;AACvC,aAAO,mBAAmB,EAAE,KAAK,oBAAoB,eAAe,SAAS;AAC7E,aAAO,cAAc,MAAM;AAAA,IAC7B;AAAA,EACF;AACA,QAAM,aAAa,MAAM;AACvB,UAAM,YAAY,KAAK,aAAa;AACpC,QAAI,YAAY,cAAc,WAAW,QAAQ;AAC/C,aAAO;AAAA,IACT;AACA,QAAI,cAAc,WAAW,SAAS;AACpC,UAAI,OAAO,cAAc,OAAO,WAAW,oCAAoC,EAAE,SAAS;AACxF,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,UAAU,CAAC,UAAU;AACzB,QAAI,WAAW,GAAG;AAChB,aAAO,SAAS,QAAQ,UAAU,KAAK,MAAM,CAAC,CAAC;AAC/C,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AACA,QAAM,qBAAqB,CAAC,UAAU;AACpC,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,uBAAuB,CAAC,UAAU;AACtC,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,oBAAoB,CAAC,UAAU;AACnC,QAAI,MAAM,SAAS,UAAU;AAC3B,aAAO,cAAc,MAAM;AAAA,IAC7B,WAAW,MAAM,SAAS,WAAW,MAAM,SAAS,eAAe;AACjE,aAAO,cAAc,MAAM;AAC3B,aAAO,SAAS,QAAQ,UAAU,KAAK,MAAM,GAAG,MAAM,OAAO,KAAK,CAAC;AAAA,IACrE;AAAA,EACF;AACA,QAAM,KAAK,OAAO;AAClB,QAAM,aAAa,KAAK,UAAU;AAClC,QAAM,YAAY,WAAW,yBAAyB,KAAK,WAAW,YAAY,EAAE,WAAW;AAC/F,QAAM,gBAAgB,YAAY,QAAQ,iCAAiC,QAAQ;AACnF,MAAI,aAAa,GAAG,aAAa;AACjC,gBAAc,MAAM,GAAG,gBAAgB,MAAM,WAAW,eAAe,CAAC;AACxE,MAAI,CAAC,WAAW;AACd,QAAI,UAAU;AACZ,oBAAc,MAAM,GAAG,gBAAgB,YAAY;AAAA,IACrD,OAAO;AACL,oBAAc,MAAM,GAAG,gBAAgB,cAAc;AAAA,IACvD;AAAA,EACF;AACA,MAAI,KAAK,aAAa,MAAM,QAAQ;AAClC,kBAAc,MAAM,KAAK,aAAa;AAAA,EACxC;AACA,QAAM,cAAc,iBAAiB,QAAQ,IAAI;AACjD,MAAI,UAAU,YAAY,cAA0B,wBAAI,OAAO,EAAE,WAAW,GAAG,QAAQ,8BAA8B,GAAG,UAAU,YAAY,QAAQ,CAAC,IAAI;AAC3J,QAAM,UAAU,YAAY,cAA0B,wBAAI,OAAO,EAAE,WAAW,GAAG,QAAQ,8BAA8B,GAAG,UAAU,YAAY,QAAQ,CAAC,IAAI;AAC7J,MAAI,OAAO,cAAc,MAAM,MAAM;AACnC,kBAA0B;AAAA,MACxB;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,WAAW,GAAG,QAAQ,8BAA8B;AAAA,QACpD,oBAAoB,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,WAAW;AAAA,QACX,cAAc,KAAK,QAAQ;AAAA,QAC3B,WAAW;AAAA,QACX,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK,cAAc,KAAK,CAAC,WAAW;AACtC,UAAM,aAAa,OAAO,SAAS,UAAU,SAAS;AACtD,gBAAY,QAAQ;AAAA,UACF;AAAA,QACd;AAAA,QACA;AAAA,UACE,oBAAoB,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,WAAW,GAAG,QAAQ,+BAA+B;AAAA,UACrD,eAAe;AAAA,UACf,SAAS;AAAA,UACT,UAAU,OAAO,MAAM,UAAU,aAAa,MAAM,MAAM,IAAI,IAAI,MAAM;AAAA,QAC1E;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,MACA,OAAO,KAAK,YAAY;AAAA,MACxB,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,SAAS,CAAC,UAAU;AACxB,QAAM,EAAE,MAAM,OAAO,IAAI;AACzB,QAAM,cAAoB,aAAO,IAAI;AACrC,QAAM,gBAAsB,aAAO,IAAI;AACvC,QAAM,mBAAyB,aAAO,IAAI;AAC1C,QAAM,aAAmB,aAAO,IAAI;AACpC,QAAM,eAAqB,aAAO,IAAI;AACtC,QAAM,oBAA0B,aAAO,IAAI;AAC3C,QAAM,mBAAyB,aAAO,IAAI;AAC1C,QAAM,QAAc,aAAO,MAAM;AACjC,QAAM,QAAQ,OAAO,SAAS;AAC9B,EAAM,sBAAgB,MAAM;AAC1B,SAAK,QAAQ,OAAO,sBAAsB,QAAQ,OAAO,CAAC;AAC1D,QAAI,YAAY,SAAS;AACvB,WAAK,gBAAgB,OAAO,sBAAsB,YAAY,OAAO,CAAC;AAAA,IACxE;AACA,UAAM,iBAAiB,OAAO,sBAAsB,WAAW,OAAO;AACtE,QAAI,CAAC,KAAK,eAAe,EAAE,OAAO,cAAc,KAAK,CAAC,MAAM,eAAe,CAAC,GAAG;AAC7E,WAAK,eAAe,cAAc;AAClC,UAAI,kBAAkB;AACpB,YAAI,MAAM,SAAS;AACjB,uBAAa,MAAM,OAAO;AAAA,QAC5B;AACA,cAAM,UAAU,WAAW,MAAM;AAC/B,iBAAO,eAAe,yBAAyB,cAAc;AAC7D,gBAAM,UAAU;AAAA,QAClB,GAAG,EAAE;AAAA,MACP,OAAO;AACL,eAAO,eAAe,yBAAyB,cAAc;AAAA,MAC/D;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,EAAE,SAAS,2BAA2B,UAAU,qBAAqB,YAAY,cAAc,qBAAqB,iBAAiB,IAAI;AAAA,IAC7I;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA,OAAO,aAAa,QAAQ,sBAAsB;AAAA,EACpD;AACA,QAAM,kBAAkB,CAAC,UAAU;AACjC,UAAM,WAAW,OAAO,oBAAoB;AAC5C,UAAM,QAAQ,WAAW,IAAI,CAAC,MAAM;AAClC,aAAO,EAAE,OAAO,GAAG,MAAM,KAAK,YAAY,EAAE,CAAC,EAAE;AAAA,IACjD,CAAC;AACD,QAAI,aAAa,QAAQ;AACvB,eAAS,MAAM,OAAO,OAAO,oBAAoB;AAAA,IACnD,OAAO;AACL,YAAM,UAAU,kBAAkB;AAClC;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,uBAAuB,CAAC,SAAS;AACrC,WAAO,SAAS,QAAQ,UAAU,KAAK,KAAK,MAAM,CAAC,CAAC;AACpD,8BAA0B,UAAU;AAAA,EACtC;AACA,QAAM,cAAc,CAAC,UAAU;AAC7B,QAAI,CAAC,OAAO,cAAc,GAAG;AAC3B,UAAI,KAAK,aAAa,GAAG;AACvB,cAAM,gBAAgB;AACtB,eAAO,YAAY,MAAM,aAAa,IAAI;AAAA,MAC5C,OAAO;AACL,cAAM,eAAe;AAAA,MACvB;AAAA,IACF,OAAO;AACL,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AACA,QAAM,gBAAgB,CAAC,UAAU;AAC/B,QAAI,CAAC,gBAAgB,KAAK,GAAG;AAC3B,aAAO,SAAS,QAAQ,gBAAgB,KAAK,MAAM,GAAG,OAAO,YAAY,CAAC,CAAC;AAAA,IAC7E;AAAA,EACF;AACA,QAAM,kBAAkB,CAAC,UAAU;AACjC,QAAI,gBAAgB,KAAK,GAAG;AAC1B,aAAO,cAAc,MAAM,KAAK;AAAA,IAClC;AAAA,EACF;AACA,QAAM,gBAAgB,CAAC,UAAU;AAC/B,WAAO,gBAAgB,MAAM,KAAK;AAAA,EACpC;AACA,QAAM,yBAAyB,CAAC,UAAU;AACxC,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,mBAAmB,CAAC,UAAU;AAClC,QAAI,KAAK,YAAY,GAAG;AACtB,aAAO,SAAS,IAAI;AAAA,IACtB;AACA,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,UAAU,CAAC,UAAU;AACzB,WAAO,SAAS,QAAQ,aAAa,KAAK,MAAM,CAAC,CAAC;AAClD,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,aAAa,CAAC,UAAU;AAC5B,WAAO,SAAS,QAAQ,UAAU,KAAK,YAAY,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAChE,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,cAAc,CAAC,UAAU;AAC7B,QAAI,oBAAoB,QAAQ;AAC9B,aAAO,SAAS,QAAQ,UAAU,gBAAgB,MAAM,CAAC,CAAC;AAAA,IAC5D;AACA,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,gBAAgB,CAAC,UAAU;AAC/B,QAAI,KAAK,YAAY,GAAG;AACtB,aAAO,SAAS,IAAI;AAAA,IACtB;AAAA,EACF;AACA,QAAM,KAAK,OAAO;AAClB,QAAM,kBAAkB,KAAK,gBAAgB;AAC7C,QAAM,OAAO,KAAK,QAAQ;AAC1B,QAAM,OAAO,CAAC;AACd,MAAI,KAAK,iBAAiB,GAAG;AAC3B,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,EAAE,QAAQ,KAAK;AAClD,YAAM,QAAQ,KAAK,YAAY,EAAE,CAAC;AAClC,YAAM,aAAa,KAAK,YAAY,MAAM;AAC1C,WAAK;AAAA,YACa;AAAA,UACd;AAAA,UACA;AAAA,YACE;AAAA,YACA,MAAM;AAAA,YACN,MAAM,OAAO,QAAQ;AAAA,YACrB,UAAU;AAAA,UACZ;AAAA,UACA,MAAM,MAAM;AAAA,QACd;AAAA,MACF;AACA,UAAI,IAAI,KAAK,YAAY,EAAE,SAAS,GAAG;AACrC,aAAK;AAAA,cACa,wBAAI,OAAO,EAAE,WAAW,GAAG,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC;AAAA,QACrG;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,UAAU;AACd,MAAI,gBAAgB,CAAC;AACrB,MAAI,UAAU,CAAC;AACf,QAAM,cAAc,EAAE,SAAS,eAAe,SAAS,kBAAkB,OAAO;AAChF,SAAO,gBAAgB,MAAM,WAAW;AACxC,YAAU,YAAY;AACtB,kBAAgB,YAAY;AAC5B,YAAU,YAAY;AACtB,QAAM,eAAe,KAAK,yBAAyB,KAAK,KAAK,YAAY,EAAE,WAAW;AACtF,QAAM,YAAY,gBAAgB,KAAK,YAAY,EAAE,CAAC,EAAE,cAAc,KAAK,KAAK,cAAc;AAC9F,MAAI,YAAY,qBAAqB,QAAQ;AAC3C,gBAAY,mBAAmB,cAAc;AAAA,EAC/C;AACA,MAAI,cAAc,SAAS,GAAG;AAC5B,QAAI,CAAC,KAAK,gBAAgB,MAAM,uBAAuB,eAAe;AACpE,gBAAU,CAAC,GAAG,eAAe,GAAG,OAAO;AAAA,IACzC,OAAO;AACL,WAAK,SAAqB;AAAA,QACxB;AAAA,QACA;AAAA,UACE,KAAK;AAAA,UACL,eAAe;AAAA,UACf,aAAa,CAAC,MAAM;AAClB,cAAE,eAAe;AAAA,UACnB;AAAA,UACA,WAAW,GAAG,QAAQ,gDAAgD;AAAA,UACtE,UAAU;AAAA,QACZ;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,MAAI,CAAC,KAAK,gBAAgB,GAAG;AAC3B,QAAI,kBAAkB;AACpB,YAAM,gBAAgB,OAAO,SAAS,UAAU,qBAAqB;AACrE,UAAI;AACJ,UAAI,OAAO,MAAM,SAAS,YAAY;AACpC,cAAM,QAAQ,WAAW,IAAI,CAAC,MAAM;AAClC,iBAAO,EAAE,OAAO,GAAG,MAAM,KAAK,YAAY,EAAE,CAAC,EAAE;AAAA,QACjD,CAAC;AACD,0BAAkB,MAAM,KAAK,MAAM,KAAK;AAAA,MAC1C,OAAO;AACL,8BAAkC,yBAAK,6BAAU,EAAE,UAAU;AAAA,UAC3D,MAAM;AAAA,cACU,wBAAI,OAAO,EAAE,WAAW,GAAG,QAAQ,qCAAqC,GAAG,UAAU,WAAW,SAAS,IAAI,WAAW,SAAS,GAAG,CAAC;AAAA,QACvJ,EAAE,CAAC;AAAA,MACL;AACA,cAAQ;AAAA,QACN,KAAK,IAAI,YAAY,kBAAkB,QAAQ,MAAM;AAAA,QACrD;AAAA,YACgB;AAAA,UACd;AAAA,UACA;AAAA,YACE,oBAAoB,OAAO;AAAA,YAC3B,KAAK;AAAA,YACL,WAAW,GAAG,QAAQ,8BAA8B,IAAI,MAAM,GAAG,QAAQ,+BAA+B;AAAA,YACxG,OAAO;AAAA,YACP,SAAS;AAAA,YACT,eAAe;AAAA,YACf,UAAU;AAAA,UACZ;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,oBAAoB,UAAU,OAAO,iBAAiB,KAAK,gBAAgB,eAAe,KAAK,gBAAgB,mBAAmB,GAAG;AACvI,UAAM,cAAc,OAAO,SAAS,UAAU,UAAU;AACxD,YAAQ;AAAA,UACU;AAAA,QACd;AAAA,QACA;AAAA,UACE,oBAAoB,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,WAAW,GAAG,QAAQ,8BAA8B,IAAI,MAAM,GAAG,QAAQ,oCAAoC;AAAA,UAC7G,SAAS;AAAA,UACT,eAAe;AAAA,UACf,UAAU,OAAO,MAAM,WAAW,aAAa,MAAM,OAAO,eAAe,IAAI,MAAM;AAAA,QACvF;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK,YAAY,GAAG;AACtB,UAAM,WAAW,OAAO,SAAS,UAAU,OAAO;AAClD,UAAM,WAAW,OAAO,SAAS,UAAU,QAAQ;AACnD,YAAQ;AAAA,UACU;AAAA,QACd;AAAA,QACA;AAAA,UACE,oBAAoB,OAAO;AAAA,UAC3B,OAAO,KAAK,YAAY,IAAI,WAAW;AAAA,UACvC,WAAW,GAAG,QAAQ,8BAA8B,IAAI,MAAM,GAAG,QAAQ,mCAAmC,KAAK,YAAY,IAAI,QAAQ,MAAM;AAAA,UAC/I,SAAS;AAAA,UACT,eAAe;AAAA,UACf,UAAU,KAAK,YAAY,IAAI,OAAO,MAAM,YAAY,aAAa,MAAM,QAAQ,IAAI,IAAI,MAAM,UAAU,OAAO,MAAM,aAAa,aAAa,MAAM,SAAS,IAAI,IAAI,MAAM;AAAA,QACjL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,KAAK,YAAY,KAAK,WAAW;AACpC,UAAM,QAAQ,eAAe,OAAO,SAAS,UAAU,SAAS,IAAI,OAAO,SAAS,UAAU,YAAY;AAC1G,YAAQ;AAAA,UACU;AAAA,QACd;AAAA,QACA;AAAA,UACE,oBAAoB,OAAO;AAAA,UAC3B;AAAA,UACA,WAAW,GAAG,QAAQ,8BAA8B,IAAI,MAAM,GAAG,QAAQ,oCAAoC;AAAA,UAC7G,SAAS,eAAe,aAAa;AAAA,UACrC,eAAe;AAAA,UACf,UAAU,OAAO,MAAM,gBAAgB,aAAa,MAAM,YAAY,IAAI,IAAI,MAAM;AAAA,QACtF;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK,SAAS,KAAK,KAAK,mBAAmB,GAAG;AAChD,UAAM,QAAQ,OAAO,SAAS,UAAU,aAAa;AACrD,YAAQ;AAAA,UACU;AAAA,QACd;AAAA,QACA;AAAA,UACE,oBAAoB,OAAO;AAAA,UAC3B;AAAA,UACA,WAAW,GAAG,QAAQ,4BAA4B;AAAA,UAClD,UAAU,OAAO,MAAM,iBAAiB,aAAa,MAAM,aAAa,IAAI,IAAI,MAAM;AAAA,QACxF;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,gBAA4B;AAAA,IAChC;AAAA,IACA;AAAA,MACE,KAAK;AAAA,MACL,WAAW,GAAG,QAAQ,uBAAuB;AAAA,MAC7C,eAAe;AAAA,MACf,aAAa,CAAC,MAAM;AAClB,UAAE,eAAe;AAAA,MACnB;AAAA,MACA,UAAU;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AACA,MAAI;AACJ,MAAI,kBAAkB,GAAG,QAAQ,+BAA+B;AAChE,MAAI,KAAK,qBAAqB,MAAM,QAAQ;AAC1C,uBAAmB,MAAM,KAAK,qBAAqB;AAAA,EACrD;AACA,qBAAmB,MAAM,QAAQ,mCAAmC,KAAK,eAAe;AACxF,MAAI,KAAK,SAAS,GAAG;AACnB,uBAAmB,MAAM,GAAG,QAAQ,2BAA2B;AAAA,EACjE;AACA,MAAI,KAAK,YAAY,GAAG;AACtB,uBAAmB,MAAM,GAAG,QAAQ,4BAA4B;AAAA,EAClE;AACA,MAAI,cAAc;AAChB,UAAM,UAAU,KAAK,YAAY,EAAE,CAAC;AACpC,QAAI,QAAQ,mBAAmB,MAAM,QAAQ;AAC3C,yBAAmB,MAAM,QAAQ,mBAAmB;AAAA,IACtD;AAAA,EACF;AACA,MAAI,mBAAmB;AACvB,MAAI,SAAS;AACX,2BAAmC,wBAAI,OAAO,EAAE,WAAW,GAAG,QAAQ,0BAA0B,GAAG,UAAU,QAAQ,CAAC;AAAA,EACxH;AACA,MAAI,KAAK,gBAAgB,GAAG;AAC1B,QAAI,KAAK,iBAAiB,GAAG;AAC3B,qBAA2B;AAAA,QACzB;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,EAAE,UAAU,QAAQ,KAAK,OAAO,WAAW,MAAM;AAAA,UACxD,KAAK;AAAA,UACL,oBAAoB,OAAO;AAAA,UAC3B;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,WAAW;AAAA,UACX;AAAA,UACA,UAAU;AAAA,YACR;AAAA,YACA;AAAA,gBACgB,wBAAI,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,EAAE,CAAC;AAAA,YACrD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,KAAK,iBAAiB,GAAG;AAC3B,UAAI,gBAAgB;AACpB,UAAI,KAAK,qBAAqB,GAAG;AAC/B,4BAAgC;AAAA,UAC9B;AAAA,UACA;AAAA,YACE,KAAK;AAAA,YACL,WAAW,GAAG,QAAQ,0BAA0B;AAAA,YAChD,eAAe;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AACA,qBAA2B;AAAA,QACzB;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,KAAK;AAAA,UACL,oBAAoB,OAAO;AAAA,UAC3B;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,SAAS;AAAA,UACT;AAAA,UACA,UAAU;AAAA,YACR;AAAA,gBACgB,yBAAK,OAAO,EAAE,WAAW,GAAG,QAAQ,oCAAoC,GAAG,UAAU;AAAA,kBACnF;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE,KAAK;AAAA,kBACL,WAAW,GAAG,QAAQ,+BAA+B,IAAI,MAAM,GAAG,QAAQ,mCAAmC,KAAK,eAAe,CAAC;AAAA,kBAClI,OAAO,EAAE,WAAW,QAAQ,WAAW,SAAS;AAAA,kBAChD;AAAA,kBACA,cAA0B;AAAA,oBACxB;AAAA,oBACA;AAAA,sBACE,OAAO,EAAE,OAAO,eAAe,SAAS,OAAO;AAAA,sBAC/C,WAAW,GAAG,QAAQ,6CAA6C,IAAI,MAAM,GAAG,QAAQ,iDAAiD,KAAK,eAAe,CAAC;AAAA,sBAC9J,UAAU;AAAA,oBACZ;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,YACF,EAAE,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI;AACJ,MAAI,KAAK,YAAY,EAAE,WAAW,GAAG;AACnC,UAAM,sBAAsB,OAAO,6BAA6B;AAChE,QAAI,qBAAqB;AACvB,oBAAc,oBAAoB,IAAI;AAAA,IACxC;AAAA,EACF;AACA,MAAI,cAA0B,wBAAI,OAAO,EAAE,KAAK,YAAY,WAAW,GAAG,QAAQ,0BAA0B,GAAG,UAAU,YAAY,CAAC;AACtI,MAAI,KAAK,eAAe,MAAM,OAAO;AACnC,kBAA0B,yBAAK,6BAAU,EAAE,UAAU;AAAA,MACnD;AAAA,MACA;AAAA,IACF,EAAE,CAAC;AAAA,EACL,OAAO;AACL,kBAA0B,yBAAK,6BAAU,EAAE,UAAU;AAAA,MACnD;AAAA,MACA;AAAA,IACF,EAAE,CAAC;AAAA,EACL;AACA,QAAM,SAAS;AAAA,IACb,UAAU,KAAK,IAAI,GAAG,KAAK,UAAU,IAAI,GAAG;AAAA,IAC5C,UAAU,KAAK,YAAY;AAAA,IAC3B,WAAW,KAAK,aAAa;AAAA,IAC7B,UAAU,KAAK,YAAY;AAAA,IAC3B,WAAW,KAAK,aAAa;AAAA,EAC/B;AACA,MAAI,KAAK,SAAS,EAAE,mBAAmB,OAAO,YAAY,CAAC,MAAM,UAAU,CAAC,KAAK,YAAY,GAAG;AAC9F,WAAO,UAAU;AAAA,EACnB;AACA,QAAM,aAAyB;AAAA,IAC7B;AAAA,IACA;AAAA,MACE,KAAK;AAAA,MACL,WAAW,GAAG,QAAQ,4BAA4B;AAAA,MAClD,OAAO;AAAA,MACP,cAA0B;AAAA,QACxB;AAAA,QACA;AAAA,UACE,WAAW,GAAG,QAAQ,kBAAkB;AAAA,UACxC,oBAAoB;AAAA,UACpB,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK,YAAY,GAAG;AACtB,QAAI,OAAO,eAAe,GAAG;AAC3B,iBAAO;AAAA,YACW,wBAAI,OAAO,EAAE,OAAO;AAAA,UAClC,UAAU;AAAA,UACV,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,QACT,GAAG,UAAU,OAAO,CAAC;AAAA,QACrB,OAAO,eAAe;AAAA,MACxB;AAAA,IACF,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAM,MAAM,CAAC,UAAU;AACrB,QAAM,EAAE,QAAQ,KAAK,IAAI;AACzB,QAAM,UAAgB,aAAO,IAAI;AACjC,QAAM,aAAa,KAAK,eAAe,MAAM,YAAY;AACzD,EAAM,sBAAgB,MAAM;AAC1B,SAAK,QAAQ,OAAO,sBAAsB,QAAQ,OAAO,CAAC;AAAA,EAC5D,CAAC;AACD,QAAM,QAAQ,CAAC;AACf,MAAI,IAAI;AACR,aAAW,SAAS,KAAK,YAAY,GAAG;AACtC,QAAI,IAAI,GAAG;AACT,YAAM,SAAqB,wBAAI,UAAU,EAAE,QAAQ,MAAM,OAAO,GAAG,WAAW,GAAG,aAAa,CAAC,CAAC;AAAA,IAClG;AACA,QAAI,iBAAiB,SAAS;AAC5B,YAAM,SAAqB,wBAAI,KAAK,EAAE,QAAQ,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,CAAC;AAAA,IAC7E,WAAW,iBAAiB,YAAY;AACtC,YAAM,SAAqB,wBAAI,QAAQ,EAAE,QAAQ,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,CAAC;AAAA,IAChF;AACA;AAAA,EACF;AACA,QAAM,SAAS;AAAA,IACb,UAAU,KAAK,IAAI,GAAG,KAAK,UAAU,IAAI,GAAG;AAAA;AAAA,IAE5C,UAAU,KAAK,YAAY;AAAA,IAC3B,WAAW,KAAK,aAAa;AAAA,IAC7B,UAAU,KAAK,YAAY;AAAA,IAC3B,WAAW,KAAK,aAAa;AAAA,EAC/B;AACA,MAAI,YAAY;AACd,WAAO,gBAAgB;AAAA,EACzB,OAAO;AACL,WAAO,gBAAgB;AAAA,EACzB;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,KAAK;AAAA,MACL,WAAW,OAAO,aAAa,QAAQ,eAAe;AAAA,MACtD,OAAO;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,EACF;AACF;AACA,IAAM,MAAM,CAAC,UAAU;AACrB,QAAM,EAAE,QAAQ,UAAU,MAAM,KAAK,IAAI;AACzC,QAAM,UAAgB,aAAO,IAAI;AACjC,QAAM,cAAoB,aAAO,IAAI;AACrC,QAAM,aAAa,KAAK,UAAU;AAClC,QAAM,OAAO,WAAW,eAAe;AACvC,EAAM,sBAAgB,MAAM;AAC1B,UAAM,UAAU,KAAK,mBAAmB;AACxC,YAAQ,QAAQ,YAAY,OAAO;AACnC,SAAK,mBAAmB,OAAO;AAC/B,UAAM,eAAe,MAAM;AACzB,WAAK,mBAAmB;AAAA,IAC1B;AACA,YAAQ,iBAAiB,UAAU,YAAY;AAC/C,YAAQ,QAAQ,iBAAiB,eAAe,aAAa;AAC7D,WAAO,MAAM;AACX,cAAQ,oBAAoB,UAAU,YAAY;AAClD,UAAI,QAAQ,SAAS;AACnB,gBAAQ,QAAQ,oBAAoB,eAAe,aAAa;AAAA,MAClE;AACA,WAAK,WAAW,KAAK;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,EAAM,gBAAU,MAAM;AACpB,QAAI,KAAK,WAAW,GAAG;AACrB,UAAI,YAAY,SAAS;AACvB,aAAK,sBAAsB;AAC3B,oBAAY,UAAU;AAAA,MACxB;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,gBAAgB,MAAM;AAC1B,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,kBAAkB,YAAY;AAChC,UAAI,CAAC,OAAO,SAAS,GAAG;AACtB,eAAO,SAAS,QAAQ,gBAAgB,OAAO,MAAM,GAAG,OAAO,YAAY,CAAC,CAAC;AAAA,MAC/E;AAAA,IACF;AAAA,EACF;AACA,OAAK,QAAQ,IAAI;AACjB,QAAM,KAAK,OAAO;AAClB,QAAM,SAAS,CAAC;AAChB,OAAK,kBAAkB,MAAM;AAC7B,MAAI,UAAU;AACd,MAAI,UAAU;AACZ,SAAK,WAAW,IAAI;AACpB,QAAI,SAAS,UAAU,KAAK,sBAAsB,GAAG;AACnD,YAAM,eAAe,CAAC;AACtB,WAAK,kBAAkB,YAAY;AACnC,oBAA0B,wBAAI,OAAO,EAAE,OAAO,cAAc,WAAW,GAAG,QAAQ,uBAAuB,EAAE,CAAC;AAAA,IAC9G;AAAA,EACF,OAAO;AACL,WAAO,UAAU;AACjB,SAAK,WAAW,KAAK;AAAA,EACvB;AACA,MAAI,sBAAsB,YAAY;AACpC,QAAI,KAAK,SAAS,EAAE,mBAAmB,OAAO,YAAY,CAAC,MAAM,QAAQ;AACvE,UAAI,WAAW,YAAY,GAAG;AAC5B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,eAAO,UAAU;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACA,MAAI,sBAAsB,YAAY;AACpC,QAAI,CAAC,WAAW,UAAU,GAAG;AAC3B,aAAO,UAAU;AAAA,IACnB;AAAA,EACF;AACA,MAAI,YAAY,GAAG,QAAQ,eAAe;AAC1C,MAAI,sBAAsB,YAAY;AACpC,iBAAa,MAAM,GAAG,QAAQ,sBAAsB;AACpD,iBAAa,MAAM,GAAG,QAAQ,0BAA0B,WAAW,YAAY,EAAE,QAAQ,CAAC;AAAA,EAC5F;AACA,MAAI,KAAK,oBAAoB,MAAM,QAAQ;AACzC,iBAAa,MAAM,KAAK,oBAAoB;AAAA,EAC9C;AACA,aAAuB,yBAAK,6BAAU,EAAE,UAAU;AAAA,IAChD;AAAA,QACgB;AAAA,MACd;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,OAAO;AAAA,QACP;AAAA,QACA,oBAAoB;AAAA,MACtB;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAM,gBAAN,cAAkC,gBAAU;AAAA,EAC1C,YAAY,OAAO;AACjB,UAAM,KAAK;AACX,kBAAc,MAAM,SAAS,MAAM;AACjC,WAAK,SAAS,EAAE,UAAU,MAAM,CAAC;AAAA,IACnC,CAAC;AACD,SAAK,QAAQ,EAAE,UAAU,MAAM;AAAA,EACjC;AAAA,EACA,OAAO,yBAAyB,OAAO;AACrC,WAAO,EAAE,UAAU,KAAK;AAAA,EAC1B;AAAA,EACA,kBAAkB,OAAO,WAAW;AAClC,YAAQ,MAAM,KAAK;AACnB,YAAQ,MAAM,SAAS;AAAA,EACzB;AAAA,EACA,SAAS;AACP,QAAI,KAAK,MAAM,UAAU;AACvB,iBAAuB,wBAAI,OAAO,EAAE,WAAW,QAAQ,sCAAsC,cAA0B,wBAAI,OAAO,EAAE,WAAW,QAAQ,oCAAoC,cAA0B,yBAAK,OAAO,EAAE,OAAO,EAAE,SAAS,QAAQ,eAAe,UAAU,YAAY,SAAS,GAAG,UAAU;AAAA,QACtT,KAAK,MAAM;AAAA,YACK,wBAAI,KAAK,EAAE,cAA0B,wBAAI,UAAU,EAAE,SAAS,KAAK,OAAO,UAAU,KAAK,MAAM,UAAU,CAAC,EAAE,CAAC;AAAA,MAC/H,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,IACX;AACA,WAAO,KAAK,MAAM;AAAA,EACpB;AACF;AACA,IAAM,cAAoB,WAAK,CAAC,EAAE,QAAQ,KAAK,MAAM;AACnD,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS,OAAO,SAAS,UAAU,yBAAyB;AAAA,MAC5D,WAAW,OAAO,SAAS,UAAU,+BAA+B;AAAA,MACpE,UAAU,OAAO,MAAM,QAAQ,IAAI;AAAA,IACrC;AAAA,EACF;AACF,GAAG,aAAa;AAChB,SAAS,cAAc,WAAW,WAAW;AAC3C,QAAM,WAAW,UAAU,YAAY,CAAC,UAAU,KAAK,UAAU,UAAU,IAAI,KAAK,UAAU,kBAAkB,UAAU,iBAAiB,UAAU,iBAAiB,UAAU;AAChL,SAAO,CAAC;AACV;AACA,IAAM,SAAN,cAA2B,gBAAU;AAAA;AAAA;AAAA,EAGnC,YAAY,OAAO;AACjB,UAAM,KAAK;AAEX,kBAAc,MAAM,SAAS;AAE7B,kBAAc,MAAM,UAAU;AAC9B,SAAK,UAAgB,gBAAU;AAC/B,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,QAAQ,QAAQ,OAAO,YAAY,KAAK,QAAQ;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,UAAU,MAAM;AAC7B,WAAO,KAAK,QAAQ,QAAQ,eAAe,UAAU,IAAI;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,sBAAsB,OAAO,MAAM,QAAQ;AACzC,SAAK,QAAQ,QAAQ,sBAAsB,OAAO,MAAM,MAAM;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,uBAAuB,OAAO,MAAM;AAClC,SAAK,QAAQ,QAAQ,uBAAuB,OAAO,IAAI;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,MAAM;AACzB,WAAO,KAAK,QAAQ,QAAQ,qBAAqB,IAAI;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,OAAO,WAAW,GAAG,GAAG;AACvC,SAAK,QAAQ,QAAQ,iBAAiB,OAAO,WAAW,GAAG,CAAC;AAAA,EAC9D;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK,QAAQ,QAAQ,WAAW;AAAA,EACzC;AAAA;AAAA,EAEA,SAAS;AACP,eAAuB,wBAAI,gBAAgB,EAAE,KAAK,KAAK,SAAS,GAAG,KAAK,OAAO,gBAAgB,KAAK,WAAW,CAAC;AAAA,EAClH;AACF;AACA,IAAM,kBAAkB,MAAMC,yBAA8B,gBAAU;AAAA;AAAA,EAEpE,YAAY,OAAO;AACjB,UAAM,KAAK;AACX,kBAAc,MAAM,SAAS;AAC7B,kBAAc,MAAM,cAAc;AAClC,kBAAc,MAAM,sBAAsB;AAC1C,kBAAc,MAAM,SAAS;AAC7B,kBAAc,MAAM,eAAe;AACnC,kBAAc,MAAM,eAAe;AACnC,kBAAc,MAAM,uBAAuB;AAC3C,kBAAc,MAAM,sBAAsC,oBAAI,IAAI,CAAC;AACnE,kBAAc,MAAM,UAAU;AAC9B,kBAAc,MAAM,YAAY;AAChC,kBAAc,MAAM,iBAAiB;AACrC,kBAAc,MAAM,eAAe;AACnC,kBAAc,MAAM,gBAAgB;AACpC,kBAAc,MAAM,WAAW;AAC/B,kBAAc,MAAM,OAAO;AAC3B,kBAAc,MAAM,gBAAgB;AACpC,kBAAc,MAAM,kBAAkB,CAAC;AACvC,kBAAc,MAAM,YAAY,KAAK;AACrC,kBAAc,MAAM,UAAU;AAC9B,kBAAc,MAAM,cAAc;AAClC,kBAAc,MAAM,YAAY;AAChC,kBAAc,MAAM,cAAc;AAClC,kBAAc,MAAM,sBAAsB;AAC1C,kBAAc,MAAM,eAAe;AACnC,kBAAc,MAAM,kBAAkB;AACtC,kBAAc,MAAM,uBAAuB,MAAM;AAC/C,UAAI,KAAK,qBAAqB,SAAS;AACrC,cAAM,gBAAgB,KAAK,qBAAqB,QAAQ,sBAAsB,EAAE;AAChF,YAAI,kBAAkB,KAAK,MAAM,yBAAyB;AACxD,eAAK,SAAS,EAAE,yBAAyB,cAAc,CAAC;AAAA,QAC1D;AAAA,MACF;AAAA,IACF,CAAC;AACD,kBAAc,MAAM,iBAAiB,CAAC,WAAW;AAC/C,WAAK,eAAe,cAAc;AAClC,UAAI,KAAK,MAAM,eAAe;AAC5B,aAAK,MAAM,cAAc,KAAK,MAAM,OAAO,MAAM;AAAA,MACnD;AAAA,IACF,CAAC;AACD,kBAAc,MAAM,cAAc,MAAM;AACtC,UAAI,KAAK,QAAQ,SAAS;AACxB,cAAM,OAAO,KAAK,YAAY,KAAK,QAAQ,QAAQ,sBAAsB,CAAC;AAC1E,YAAI,CAAC,KAAK,OAAO,KAAK,MAAM,IAAI,KAAK,KAAK,UAAU,KAAK,KAAK,WAAW,GAAG;AAC1E,eAAK,SAAS,EAAE,KAAK,CAAC;AACtB,cAAI,KAAK,aAAa,MAAM,gBAAgB;AAC1C,iBAAK,eAAe,cAAc;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,kBAAc,MAAM,gBAAgB,CAAC,qBAAqB;AACxD,UAAI,KAAK,MAAM,oBAAoB,QAAQ;AACzC,eAAO;AAAA,MACT,OAAO;AACL,eAAO,KAAK,MAAM,gBAAgB,gBAAgB;AAAA,MACpD;AAAA,IACF,CAAC;AACD,kBAAc,MAAM,iBAAiB,CAAC,iBAAiB;AACrD,WAAK,SAAS,QAAQ,YAAY,aAAa,QAAQ,CAAC;AAAA,IAC1D,CAAC;AACD,kBAAc,MAAM,eAAe,CAAC,cAAc,YAAY;AAAA,IAC9D,CAAC;AACD,kBAAc,MAAM,uBAAuB,CAAC,SAAS,YAAY;AAC/D,YAAM,aAAS,+BAAa,SAAS,OAAO;AAC5C,WAAK,SAAS,EAAE,OAAO,CAAC;AAAA,IAC1B,CAAC;AACD,kBAAc,MAAM,uBAAuB,MAAM;AAC/C,WAAK,SAAS,EAAE,QAAQ,OAAO,CAAC;AAAA,IAClC,CAAC;AACD,kBAAc,MAAM,YAAY,MAAM;AACpC,aAAO,KAAK;AAAA,IACd,CAAC;AACD,kBAAc,MAAM,eAAe,CAAC,OAAO,SAAS;AAClD,MAAAA,iBAAgB,YAAY,IAAI,UAAU,KAAK,YAAY,YAAY,MAAM,QAAQ,MAAM;AAC3F,YAAM,aAAa,QAAQ,cAAc,gBAAgB;AACzD,YAAM,aAAa,gBAAgB;AACnC,YAAM,aAAa,aAAa;AAChC,WAAK,iBAAiB;AACtB,UAAI,gBAAgB,YAAY;AAC9B,YAAI,WAAW;AACf,YAAI,UAAU,KAAK,SAAS,UAAU,WAAW;AACjD,YAAI,KAAK,YAAY,EAAE,SAAS,GAAG;AACjC,oBAAU,KAAK,SAAS,UAAU,SAAS,EAAE,QAAQ,KAAK,OAAO,KAAK,YAAY,EAAE,MAAM,CAAC;AAAA,QAC7F;AACA,YAAI,KAAK,MAAM,kBAAkB;AAC/B,gBAAM,gBAAgB,KAAK,MAAM,iBAAiB,SAAS,MAAM,MAAM;AACvE,cAAI,eAAe;AACjB,iBAAK,iBAAiB,OAAO,eAAe,IAAI,EAAE;AAClD,uBAAW;AAAA,UACb;AAAA,QACF;AACA,YAAI,CAAC,UAAU;AACb,eAAK,iBAAiB,OAAO,SAAS,IAAI,EAAE;AAAA,QAC9C;AAAA,MACF,OAAO;AACL,cAAM,UAAU,MAAM;AACtB,cAAM,OAAO,QAAQ,sBAAsB;AAC3C,cAAM,UAAU,MAAM,UAAU,KAAK;AACrC,cAAM,UAAU,MAAM,UAAU,KAAK;AACrC,cAAM,aAAa,QAAQ,OAAO,SAAS,KAAK,UAAU;AAC1D,cAAM,qBAAqB,sBAAsB,cAAc,WAAW,eAAe,MAAM,YAAY;AAC3G,cAAM,IAAI,qBAAqB,KAAK;AACpC,cAAM,IAAI,qBAAqB,KAAK;AACpC,YAAI,WAAW;AACf,YAAI,KAAK,MAAM,kBAAkB;AAC/B,gBAAM,cAA0B,wBAAI,gBAAgB,EAAE,QAAQ,MAAM,KAAK,GAAG,KAAK,MAAM,CAAC;AACxF,gBAAM,gBAAgB,KAAK,MAAM,iBAAiB,SAAS,MAAM,MAAM;AACvE,cAAI,eAAe;AACjB,iBAAK,iBAAiB,OAAO,eAAe,GAAG,CAAC;AAChD,uBAAW;AAAA,UACb;AAAA,QACF;AACA,YAAI,CAAC,UAAU;AACb,cAAI,SAAS,GAAG;AACd,iBAAK,iBAAiB,WAAuB,wBAAI,gBAAgB,EAAE,MAAM,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC;AAAA,UAChG,OAAO;AACL,kBAAM,aAAa,aAAa,KAAK,YAAY,GAAG,GAAG,CAAC;AAAA,UAC1D;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,kBAAc,MAAM,kBAAkB,CAAC,UAAU;AAC/C,WAAK;AACL,UAAI,KAAK,mBAAmB,GAAG;AAC7B,aAAK,YAAY,KAAK;AAAA,MACxB;AAAA,IACF,CAAC;AACD,kBAAc,MAAM,kBAAkB,CAAC,UAAU;AAC/C,WAAK;AACL,UAAI,KAAK,mBAAmB,GAAG;AAC7B,aAAK,YAAY,KAAK;AAAA,MACxB;AAAA,IACF,CAAC;AACD,kBAAc,MAAM,eAAe,CAAC,UAAU;AAC5C,UAAI,CAACA,iBAAgB,aAAa,KAAK,MAAM,gBAAgB;AAC3D,cAAM,eAAe,KAAK,MAAM,eAAe,KAAK;AACpD,YAAI,cAAc;AAChB,gBAAM,WAAW,QAAQ,SAAS,aAAa,MAAM,KAAK,MAAM,OAAO,KAAK;AAC5E,UAAAA,iBAAgB,YAAY,IAAI,UAAU,KAAK,YAAY,YAAY,UAAU,aAAa,MAAM,aAAa,MAAM;AAAA,QACzH;AAAA,MACF;AACA,UAAIA,iBAAgB,WAAW;AAC7B,YAAI,KAAK,aAAa,MAAM,kBAAkBA,iBAAgB,UAAU,eAAe,KAAK,YAAY;AACtG,UAAAA,iBAAgB,UAAU,WAAW,sBAAsB,IAAI;AAAA,QACjE;AACA,YAAIA,iBAAgB,UAAU,eAAe,KAAK,YAAY;AAC5D;AAAA,QACF;AACA,cAAM,eAAe;AACrB,aAAK,WAAW;AAChB,cAAM,UAAU,KAAK,QAAQ;AAC7B,aAAK,aAAa,KAAK,gBAAgB,cAAc,KAAK;AAC1D,aAAK,WAAW,YAAY,KAAK,aAAa,QAAQ,wBAAwB;AAC9E,aAAK,WAAW,MAAM,aAAa;AACnC,cAAM,QAAQ,KAAK,MAAM,MAAM,aAAa,cAAc;AAC1D,aAAK,WAAW,MAAM,aAAa,OAAO,KAAK,WAAW,KAAK,YAAY,KAAK,aAAa,KAAK;AAClG,gBAAQ,YAAY,KAAK,UAAU;AACnC,aAAK,WAAW;AAChB,aAAK,YAAY,IAAI;AACrB,YAAI,CAAC,KAAK,wBAAwB,KAAK,MAAM,MAAM,mBAAmB,KAAK,QAAQ,MAAM,QAAQ;AAC/F,eAAK,SAAS,EAAE,WAAW,KAAK,MAAM,MAAM,iBAAiB,EAAE,CAAC;AAAA,QAClE;AACA,cAAM,aAAa,KAAK,QAAQ,QAAQ,sBAAsB;AAC9D,cAAM,IAAI,IAAI;AAAA,UACZ,MAAM,UAAU,WAAW;AAAA,UAC3B,MAAM,UAAU,WAAW;AAAA,UAC3B;AAAA,UACA;AAAA,QACF;AACA,UAAE,gBAAgB,KAAK,UAAU;AAAA,MACnC;AAAA,IACF,CAAC;AACD,kBAAc,MAAM,cAAc,CAAC,UAAU;AAC3C,UAAI;AACJ,UAAI,KAAK,YAAY,CAAC,KAAK,sBAAsB;AAC/C,cAAM,eAAe;AACrB,cAAM,cAAc,KAAK,KAAK,QAAQ,YAAY,OAAO,SAAS,GAAG,sBAAsB;AAC3F,cAAM,MAAM;AAAA,UACV,GAAG,MAAM,YAAY,cAAc,OAAO,SAAS,WAAW,SAAS;AAAA,UACvE,GAAG,MAAM,YAAY,cAAc,OAAO,SAAS,WAAW,QAAQ;AAAA,QACxE;AACA,aAAK,qBAAqB,IAAI,GAAG,IAAI,CAAC;AACtC,cAAM,WAAW,KAAK,MAAM,MAAM,mBAAmB,KAAK,UAAUA,iBAAgB,UAAU,UAAU,IAAI,GAAG,IAAI,CAAC;AACpH,YAAI,UAAU;AACZ,eAAK,WAAW;AAChB,cAAI,KAAK,YAAY;AACnB,iBAAK,WAAW,YAAY,KAAK,aAAa,SAAS,SAAS;AAChE,qBAAS,KAAK,gBAAgB,KAAK,UAAU;AAC7C,iBAAK,WAAW,MAAM,aAAa;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,kBAAc,MAAM,eAAe,CAAC,UAAU;AAC5C,UAAI,KAAK,UAAU;AACjB,YAAI,KAAK,aAAa,MAAM,gBAAgB;AAC1C,UAAAA,iBAAgB,UAAU,WAAW,sBAAsB,KAAK;AAAA,QAClE;AACA,aAAK,eAAe;AAAA,MACtB;AAAA,IACF,CAAC;AACD,kBAAc,MAAM,UAAU,CAAC,UAAU;AACvC,UAAI,KAAK,UAAU;AACjB,cAAM,eAAe;AACrB,cAAM,YAAYA,iBAAgB;AAClC,YAAI,KAAK,UAAU;AACjB,cAAI,UAAU,aAAa,QAAQ;AACjC,kBAAM,UAAU,KAAK,SAAS,QAAQ,QAAQ,UAAU,UAAU,KAAK,SAAS,KAAK,MAAM,GAAG,KAAK,SAAS,UAAU,KAAK,SAAS,KAAK,CAAC;AAC1I,gBAAI,UAAU,qBAAqB,QAAQ;AACzC,wBAAU,iBAAiB,SAAS,KAAK;AAAA,YAC3C;AAAA,UACF,WAAW,UAAU,aAAa,QAAQ;AACxC,iBAAK,SAAS,QAAQ,SAAS,UAAU,SAAS,MAAM,GAAG,KAAK,SAAS,KAAK,MAAM,GAAG,KAAK,SAAS,UAAU,KAAK,SAAS,KAAK,CAAC;AAAA,UACrI;AAAA,QACF;AACA,aAAK,WAAW,cAAc;AAAA,MAChC;AACA,WAAK,iBAAiB;AAAA,IACxB,CAAC;AACD,SAAK,gBAAgB,CAAC;AACtB,SAAK,wBAAwB,CAAC;AAC9B,SAAK,UAAgB,gBAAU;AAC/B,SAAK,eAAqB,gBAAU;AACpC,SAAK,UAAgB,gBAAU;AAC/B,SAAK,uBAA6B,gBAAU;AAC5C,SAAK,iBAAiB,MAAM,mBAAmB,SAAS,MAAM,iBAAiB;AAC/E,SAAK,YAAY,MAAM,YAAY,MAAM,YAAY;AACrD,SAAK,QAAQ,EAAE,GAAG,cAAc,GAAG,MAAM,MAAM;AAC/C,SAAK,WAAW,MAAM,WAAW,MAAM,WAAW,MAAM;AACxD,SAAK,aAAa,KAAK,MAAM,aAAa,KAAK,MAAM,aAAa;AAClE,SAAK,uBAAuB;AAC5B,SAAK,eAAe,KAAK,MAAM,MAAM,cAAc,EAAE,IAAI,KAAK,QAAQ;AACtE,SAAK,aAAa,SAAS;AAC3B,SAAK,mBAAmB,KAAK,MAAM,oBAAoB;AACvD,SAAK,QAAQ;AAAA,MACX,MAAM,KAAK,MAAM;AAAA,MACjB,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,aAAa;AAAA,MACb,yBAAyB;AAAA,MACzB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,kBAAkB,aAAa;AAAA,IACjC;AACA,SAAK,eAAe,KAAK,aAAa,MAAM;AAAA,EAC9C;AAAA,EACA,oBAAoB;AAClB,SAAK,WAAW;AAChB,SAAK,kBAAkB,KAAK,QAAQ,QAAQ;AAC5C,SAAK,gBAAgB,KAAK,gBAAgB;AAC1C,SAAK,aAAa,SAAS,KAAK;AAChC,SAAK,aAAa,uBAAuB,CAAC,MAAM,KAAK,cAAc,CAAC;AACpE,SAAK,iBAAiB,IAAI,eAAe,CAAC,YAAY;AACpD,4BAAsB,MAAM;AAC1B,aAAK,WAAW;AAAA,MAClB,CAAC;AAAA,IACH,CAAC;AACD,QAAI,KAAK,QAAQ,SAAS;AACxB,WAAK,eAAe,QAAQ,KAAK,QAAQ,OAAO;AAAA,IAClD;AACA,QAAI,KAAK,cAAc;AACrB,WAAK,MAAM,MAAM,kBAAkB,KAAK,aAAa;AACrD,WAAK,oBAAoB;AAAA,IAC3B,OAAO;AACL,WAAK,cAAc,iBAAiB,UAAU,MAAM;AAClD,aAAK,WAAW;AAAA,MAClB,CAAC;AACD,YAAM,gBAAgB,KAAK,MAAM,WAAW,WAAW;AACvD,YAAM,gBAAgB,KAAK,QAAQ;AACnC,uBAAiB,eAAe,aAAa;AAC7C,WAAK,gBAAgB,IAAI,iBAAiB,MAAM;AAC9C,cAAM,UAAU,iBAAiB,eAAe,aAAa;AAC7D,YAAI,SAAS;AACX,eAAK,OAAO,mBAAmB;AAAA,QACjC;AAAA,MACF,CAAC;AACD,WAAK,cAAc,QAAQ,eAAe,EAAE,iBAAiB,CAAC,OAAO,EAAE,CAAC;AAAA,IAC1E;AACA,aAAS,iBAAiB,oBAAoB,MAAM;AAClD,iBAAW,CAAC,GAAG,YAAY,KAAK,KAAK,MAAM,MAAM,cAAc,GAAG;AAChE,cAAM,SAAS,aAAa;AAC5B,YAAI,QAAQ;AACV,eAAK,OAAO,mBAAmB;AAAA,QACjC;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,kBAAkB,KAAK,QAAQ,QAAQ;AAC5C,SAAK,gBAAgB,KAAK,gBAAgB;AAC1C,QAAI,KAAK,cAAc;AACrB,UAAI,KAAK,MAAM,UAAU,KAAK,eAAe;AAC3C,YAAI,KAAK,kBAAkB,QAAQ;AACjC,eAAK,cAAc,qBAAqB,KAAK,aAAa;AAAA,QAC5D;AACA,aAAK,MAAM,MAAM,cAAc,EAAE,IAAI,KAAK,QAAQ,EAAE,SAAS;AAC7D,aAAK,MAAM,MAAM,kBAAkB,KAAK,aAAa;AACrD,aAAK,eAAe,KAAK,MAAM,MAAM,cAAc,EAAE,IAAI,KAAK,QAAQ;AACtE,aAAK,aAAa,SAAS;AAC3B,aAAK,aAAa,uBAAuB,CAAC,MAAM,KAAK,cAAc,CAAC;AACpE,aAAK,gBAAgB,KAAK,MAAM;AAChC,aAAK,iBAAiB;AAAA,MACxB;AACA,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,QAAI,IAAI;AACR,QAAI,KAAK,QAAQ,SAAS;AACxB,OAAC,KAAK,KAAK,mBAAmB,OAAO,SAAS,GAAG,UAAU,KAAK,QAAQ,OAAO;AAAA,IACjF;AACA,QAAI,KAAK,cAAc;AACrB,WAAK,MAAM,MAAM,qBAAqB,KAAK,aAAa;AAAA,IAC1D;AACA,KAAC,KAAK,KAAK,kBAAkB,OAAO,SAAS,GAAG,WAAW;AAAA,EAC7D;AAAA,EACA,SAAS;AACP,QAAI,CAAC,KAAK,QAAQ,SAAS;AACzB,iBAAuB,yBAAK,OAAO,EAAE,KAAK,KAAK,SAAS,WAAW,KAAK,aAAa,QAAQ,kBAAkB,GAAG,UAAU;AAAA,YAC1G,wBAAI,OAAO,EAAE,KAAK,KAAK,cAAc,WAAW,KAAK,aAAa,QAAQ,4BAA4B,EAAE,GAAG,eAAe;AAAA,QAC1I,KAAK,sBAAsB;AAAA,MAC7B,EAAE,CAAC;AAAA,IACL;AACA,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,QAAQ,KAAK,QAAQ,EAAE,eAAe;AAC5C,UAAM,QAAQ,KAAK,QAAQ,EAAE,SAAS,EAAE;AACxC,UAAM,aAAa,EAAE,SAAS;AAC9B,UAAM,QAAQ,KAAK,aAAa;AAChC,UAAM,QAAQ,KAAK,cAAc,KAAK;AACtC,UAAM,OAAO,KAAK,WAAW;AAC7B,UAAM,gBAAgB,KAAK,kBAAkB,MAAM,KAAK,aAAa;AACrE,QAAI,kBAAkB;AACtB,QAAI,wBAAwB;AAC5B,QAAI,YAAY;AAChB,QAAI,iBAAiB;AACrB,QAAI,KAAK,cAAc;AACrB,wBAAkB,KAAK,cAAc;AACrC,uBAAiB,KAAK,sBAAsB;AAC5C,YAAM,eAAe,KAAK,mBAAmB;AAC7C,8BAAwB,KAAK,kBAAkB,cAAc,KAAK,qBAAqB;AACvF,sBAA4B,wBAAI,OAAO,EAAE,WAAW,KAAK,aAAa,QAAQ,6BAA6B,GAAG,UAAU,KAAK,gBAAgB,EAAE,GAAG,eAAe;AAAA,IACnK;AACA,eAAuB;AAAA,MACrB;AAAA,MACA;AAAA,QACE,KAAK,KAAK;AAAA,QACV,WAAW,KAAK,aAAa,QAAQ,kBAAkB;AAAA,QACvD,aAAa,KAAK;AAAA,QAClB,aAAa,KAAK;AAAA,QAClB,YAAY,KAAK;AAAA,QACjB,QAAQ,KAAK;AAAA,QACb,UAAU;AAAA,cACQ,wBAAI,OAAO,EAAE,KAAK,KAAK,cAAc,WAAW,KAAK,aAAa,QAAQ,4BAA4B,EAAE,GAAG,eAAe;AAAA,UAC1I;AAAA,cACgB,wBAAI,SAAS,EAAE,QAAQ,MAAM,MAAM,KAAK,MAAM,YAAY,GAAG,aAAa;AAAA,UAC1F;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK,MAAM;AAAA,UACX;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,OAAO;AACnB,UAAM,YAAY,KAAK,aAAa,QAAQ,uBAAuB;AACnE,UAAM,UAAU,KAAK,MAAM,MAAM,aAAa,EAAE,aAAa;AAC7D,QAAI,KAAK,gBAAgB,QAAQ,OAAO,GAAG;AACzC,kBAAwB,wBAAI,OAAO,EAAE,WAAW,WAAW,KAAK,KAAK,SAAS,UAAU,MAAM,CAAC;AAC/F,YAAM,sBAAsC,oBAAI,IAAI;AACpD,YAAM,6BAA6C,oBAAI,IAAI;AAC3D,iBAAW,CAAC,GAAG,QAAQ,KAAK,aAAa,QAAQ;AAC/C,cAAM,SAAS,QAAQ,IAAI,QAAQ;AACnC,cAAM,aAAa,UAAU,OAAO,UAAU,MAAM,CAAC,OAAO,WAAW,KAAK,OAAO,WAAW,MAAM,OAAO,YAAY,EAAE,SAAS,KAAK,KAAK,MAAM,qBAAqB;AACvK,YAAI,YAAY;AACd,8BAAoB,IAAI,cAA0B,wBAAI,cAAc,EAAE,QAAQ,MAAM,QAAQ,MAAM,KAAK,MAAM,wBAAwB,CAAC,CAAC;AACvI,qCAA2B,IAAI,cAA0B,wBAAI,WAAW,EAAE,QAAQ,MAAM,QAAQ,MAAM,OAAO,YAAY,MAAM,GAAG,CAAC,CAAC;AAAA,QACtI;AAAA,MACF;AACA,YAAM,mBAAmB,KAAK,aAAa,QAAQ,mCAAmC;AACtF,YAAM,mBAAmB,KAAK,aAAa,QAAQ,yCAAyC;AAC5F,UAAI,KAAK,MAAM,MAAM,aAAa,EAAE,oBAAoB,GAAG;AACzD,cAAM,0BAAsC,yBAAK,OAAO,EAAE,WAAW,kBAAkB,OAAO,EAAE,eAAe,SAAS,GAAG,UAAU;AAAA,UACnI,2BAA2B,IAAI,aAAa,GAAG;AAAA,cAC/B,yBAAK,OAAO,EAAE,WAAW,kBAAkB,OAAO,EAAE,eAAe,MAAM,GAAG,UAAU;AAAA,YACpG,2BAA2B,IAAI,aAAa,IAAI;AAAA,YAChD;AAAA,YACA,2BAA2B,IAAI,aAAa,KAAK;AAAA,UACnD,EAAE,CAAC;AAAA,UACH,2BAA2B,IAAI,aAAa,MAAM;AAAA,QACpD,EAAE,CAAC;AACH,mBAAuB,yBAAK,OAAO,EAAE,WAAW,kBAAkB,OAAO,EAAE,eAAe,SAAS,GAAG,UAAU;AAAA,UAC9G,oBAAoB,IAAI,aAAa,GAAG;AAAA,cACxB,yBAAK,OAAO,EAAE,WAAW,kBAAkB,OAAO,EAAE,eAAe,MAAM,GAAG,UAAU;AAAA,YACpG,oBAAoB,IAAI,aAAa,IAAI;AAAA,YACzC;AAAA,YACA,oBAAoB,IAAI,aAAa,KAAK;AAAA,UAC5C,EAAE,CAAC;AAAA,UACH,oBAAoB,IAAI,aAAa,MAAM;AAAA,QAC7C,EAAE,CAAC;AAAA,MACL,OAAO;AACL,cAAM,0BAAsC,yBAAK,OAAO,EAAE,WAAW,kBAAkB,OAAO,EAAE,eAAe,MAAM,GAAG,UAAU;AAAA,UAChI,2BAA2B,IAAI,aAAa,IAAI;AAAA,cAChC,yBAAK,OAAO,EAAE,WAAW,kBAAkB,OAAO,EAAE,eAAe,SAAS,GAAG,UAAU;AAAA,YACvG,2BAA2B,IAAI,aAAa,GAAG;AAAA,YAC/C;AAAA,YACA,2BAA2B,IAAI,aAAa,MAAM;AAAA,UACpD,EAAE,CAAC;AAAA,UACH,2BAA2B,IAAI,aAAa,KAAK;AAAA,QACnD,EAAE,CAAC;AACH,mBAAuB,yBAAK,OAAO,EAAE,WAAW,kBAAkB,OAAO,EAAE,eAAe,MAAM,GAAG,UAAU;AAAA,UAC3G,oBAAoB,IAAI,aAAa,IAAI;AAAA,cACzB,yBAAK,OAAO,EAAE,WAAW,kBAAkB,OAAO,EAAE,eAAe,SAAS,GAAG,UAAU;AAAA,YACvG,oBAAoB,IAAI,aAAa,GAAG;AAAA,YACxC;AAAA,YACA,oBAAoB,IAAI,aAAa,MAAM;AAAA,UAC7C,EAAE,CAAC;AAAA,UACH,oBAAoB,IAAI,aAAa,KAAK;AAAA,QAC5C,EAAE,CAAC;AAAA,MACL;AAAA,IACF,OAAO;AACL,iBAAuB,wBAAI,OAAO,EAAE,WAAW,WAAW,KAAK,KAAK,SAAS,OAAO,EAAE,UAAU,YAAY,KAAK,GAAG,MAAM,GAAG,QAAQ,GAAG,OAAO,GAAG,SAAS,OAAO,GAAG,UAAU,MAAM,CAAC;AAAA,IACxL;AAAA,EACF;AAAA,EACA,eAAe;AACb,eAAuB,yBAAK,6BAAU,EAAE,UAAU;AAAA,UAChC,wBAAI,KAAK,EAAE,QAAQ,MAAM,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,QAAQ,EAAE,GAAG,SAAS;AAAA,MACnG,KAAK,qBAAqB;AAAA,IAC5B,EAAE,CAAC;AAAA,EACL;AAAA,EACA,uBAAuB;AACrB,UAAM,QAAQ,CAAC;AACf,UAAM,YAAY,KAAK,MAAM;AAC7B,QAAI,KAAK,MAAM,WAAW;AACxB,YAAM,IAAI,KAAK,MAAM,MAAM,QAAQ,KAAK,QAAQ,EAAE,QAAQ;AAC1D,YAAM,SAAS;AACf,YAAM,QAAQ;AACd,YAAM,SAAS,iBAAiB;AAChC,YAAM,YAAY,KAAK,aAAa,QAAQ,qBAAqB;AACjE,YAAM,SAAS;AACf,YAAM,SAAqB,wBAAI,OAAO,EAAE,OAAO,EAAE,KAAK,GAAG,MAAM,EAAE,QAAQ,IAAI,QAAQ,OAAO,QAAQ,QAAQ,OAAO,wBAAwB,QAAQ,yBAAyB,OAAO,GAAG,WAAW,YAAY,MAAM,KAAK,aAAa,QAAQ,yBAAyB,GAAG,cAA0B,wBAAI,OAAO,EAAE,OAAO,EAAE,WAAW,iBAAiB,GAAG,UAAU,UAAU,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1X,YAAM,SAAqB,wBAAI,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,IAAI,QAAQ,MAAM,GAAG,OAAO,QAAQ,QAAQ,sBAAsB,QAAQ,yBAAyB,OAAO,GAAG,WAAW,YAAY,MAAM,KAAK,aAAa,QAAQ,0BAA0B,GAAG,cAA0B,wBAAI,OAAO,EAAE,OAAO,EAAE,WAAW,gBAAgB,GAAG,UAAU,UAAU,CAAC,EAAE,GAAG,MAAM,CAAC;AACjX,YAAM,SAAqB,wBAAI,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,OAAO,MAAM,EAAE,QAAQ,IAAI,QAAQ,OAAO,QAAQ,QAAQ,OAAO,qBAAqB,QAAQ,sBAAsB,OAAO,GAAG,WAAW,YAAY,MAAM,KAAK,aAAa,QAAQ,4BAA4B,GAAG,cAA0B,wBAAI,OAAO,EAAE,UAAU,UAAU,CAAC,EAAE,GAAG,OAAO,CAAC;AAC9V,YAAM,SAAqB,wBAAI,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,IAAI,QAAQ,MAAM,EAAE,QAAQ,OAAO,OAAO,QAAQ,QAAQ,qBAAqB,QAAQ,wBAAwB,OAAO,GAAG,WAAW,YAAY,MAAM,KAAK,aAAa,QAAQ,2BAA2B,GAAG,cAA0B,wBAAI,OAAO,EAAE,OAAO,EAAE,WAAW,iBAAiB,GAAG,UAAU,UAAU,CAAC,EAAE,GAAG,MAAM,CAAC;AAAA,IACjY;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AACd,UAAM,kBAAkB,CAAC;AACzB,QAAI,KAAK,gBAAgB;AACvB,YAAM,UAAU,KAAK,MAAM,MAAM,cAAc;AAC/C,UAAI,IAAI;AACR,iBAAW,CAAC,UAAU,YAAY,KAAK,SAAS;AAC9C,YAAI,aAAa,MAAM,gBAAgB;AACrC,0BAAgB;AAAA,gBACE;AAAA,cACd;AAAA,cACA;AAAA,gBACE,QAAQ;AAAA,gBACR,OAAO,KAAK,mBAAmB,MAAM;AAAA,gBACrC;AAAA,gBACA,KAAK,KAAK,YAAY,SAAS;AAAA,gBAC/B,aAAa,KAAK;AAAA,gBAClB,eAAe,KAAK;AAAA,gBACpB,cAA0B,wBAAI,OAAO,EAAE,WAAW,KAAK,MAAM,iBAAiB,cAA0B,wBAAIA,kBAAiB,EAAE,GAAG,KAAK,OAAO,UAAU,YAAY,KAAK,CAAC,EAAE,CAAC;AAAA,cAC/K;AAAA,cACA;AAAA,YACF;AAAA,UACF;AACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB;AACnB,UAAM,eAA+B,oBAAI,IAAI;AAC7C,SAAK,MAAM,MAAM,WAAW,CAAC,SAAS;AACpC,UAAI,gBAAgB,SAAS;AAC3B,cAAM,QAAQ;AACd,cAAM,UAAU,KAAK,mBAAmB,MAAM,MAAM,CAAC;AACrD,cAAM,mBAAmB,OAAO;AAChC,cAAM,WAAW,MAAM,WAAW;AAClC,cAAM,OAAO,MAAM,UAAU,EAAE,eAAe;AAC9C,cAAM,UAAU,YAAY,CAAC,MAAM,uBAAuB;AAC1D,cAAM,YAAY,MAAM,WAAW,KAAK,YAAY,KAAK,QAAQ,KAAK,KAAK,SAAS;AACpF,YAAI,WAAW;AACb,gBAAM,MAAM,MAAM,MAAM,KAAK,MAAM,sBAAsB,IAAI,MAAM,YAAY,IAAI;AACnF,uBAAa,IAAI,KAAK,MAAM,OAAG;AAAA,gBACb;AAAA,cACd;AAAA,cACA;AAAA,gBACE,QAAQ;AAAA,gBACR,MAAM;AAAA,gBACN;AAAA,gBACA;AAAA,gBACA,eAAe,KAAK,MAAM;AAAA,gBAC1B,cAAc,KAAK,MAAM;AAAA,cAC3B;AAAA,cACA;AAAA,YACF;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AACD,gBAAM,YAAY,SAAS;AAAA,QAC7B;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAChB,UAAM,YAAY,CAAC;AACnB,SAAK,MAAM,MAAM,WAAW,CAAC,SAAS;AACpC,UAAI,gBAAgB,SAAS;AAC3B,cAAM,QAAQ;AACd,kBAAU,SAAqB,wBAAI,eAAe,EAAE,QAAQ,MAAM,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,CAAC;AAAA,MACjG;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AACX,UAAM,OAAuB,oBAAI,IAAI;AACrC,SAAK,MAAM,MAAM,iBAAiB,KAAK,UAAU,CAAC,SAAS;AACzD,UAAI,gBAAgB,SAAS;AAC3B,cAAM,QAAQ;AACd,cAAM,WAAW,MAAM,WAAW;AAClC,cAAM,OAAO,MAAM,QAAQ;AAC3B,cAAM,YAAY,MAAM,WAAW,KAAK,YAAY,CAAC,MAAM,uBAAuB;AAClF,YAAI,WAAW;AACb,eAAK,IAAI,MAAM,MAAM,OAAmB;AAAA,YACtC;AAAA,YACA;AAAA,cACE,QAAQ;AAAA,cACR;AAAA,cACA,MAAM;AAAA,cACN;AAAA,YACF;AAAA,YACA,MAAM,MAAM;AAAA,UACd,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB;AACtB,eAAuB,wBAAI,OAAO,EAAE,KAAK,KAAK,sBAAsB,WAAW,KAAK,aAAa,QAAQ,wBAAwB,GAAG,UAAU,oBAAoB,GAAG,mBAAmB;AAAA,EAC1L;AAAA,EACA,qBAAqB,GAAG,GAAG;AACzB,UAAM,IAAI,KAAK,sBAAsB,KAAK,QAAQ,OAAO;AACzD,UAAM,IAAI,EAAE,UAAU;AACtB,UAAM,SAAS;AACf,UAAM,SAAS,iBAAiB;AAChC,QAAI,WAAW;AACf,QAAI,KAAK,MAAM,MAAM,iBAAiB,KAAK,KAAK,MAAM,qBAAqB,aAAa,QAAQ;AAC9F,UAAI,IAAI,EAAE,IAAI,UAAU,IAAI,EAAE,IAAI,UAAU,IAAI,EAAE,IAAI,UAAU,IAAI,EAAE,IAAI,QAAQ;AAChF,mBAAW;AAAA,MACb;AAAA,IACF;AACA,QAAI,WAAW,aAAa;AAC5B,QAAI,CAAC,UAAU;AACb,UAAI,KAAK,EAAE,IAAI,QAAQ;AACrB,mBAAW,aAAa;AAAA,MAC1B,WAAW,KAAK,EAAE,SAAS,IAAI,QAAQ;AACrC,mBAAW,aAAa;AAAA,MAC1B,WAAW,KAAK,EAAE,IAAI,QAAQ;AAC5B,mBAAW,aAAa;AAAA,MAC1B,WAAW,KAAK,EAAE,UAAU,IAAI,QAAQ;AACtC,mBAAW,aAAa;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,aAAa,KAAK,MAAM,kBAAkB;AAC5C,WAAK,SAAS,EAAE,kBAAkB,SAAS,CAAC;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,UAAM,OAAuB,oBAAI,IAAI;AACrC,SAAK,MAAM,MAAM,WAAW,CAAC,MAAM,MAAM;AACvC,UAAI,gBAAgB,SAAS;AAC3B,aAAK,IAAI,KAAK,MAAM,GAAG,IAAI;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,eAAW,CAAC,QAAQ,OAAO,KAAK,KAAK,oBAAoB;AACvD,UAAI,CAAC,KAAK,IAAI,MAAM,GAAG;AACrB,gBAAQ,OAAO;AACf,aAAK,mBAAmB,OAAO,MAAM;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB,YAAY,KAAK;AACjC,UAAM,UAAU,CAAC;AACjB,UAAM,aAA6B,oBAAI,IAAI;AAC3C,QAAI,YAAY,CAAC;AACjB,eAAW,MAAM,KAAK;AACpB,UAAI,WAAW,IAAI,EAAE,GAAG;AACtB,gBAAQ,KAAK,EAAE;AACf,mBAAW,IAAI,EAAE;AAAA,MACnB;AAAA,IACF;AACA,QAAI,OAAO,GAAG,IAAI,QAAQ,GAAG,OAAO;AACpC,eAAW,CAAC,IAAI,CAAC,KAAK,YAAY;AAChC,UAAI,CAAC,WAAW,IAAI,EAAE,GAAG;AACvB,YAAI,KAAK,EAAE;AAAA,MACb;AAAA,IACF;AACA,gBAAY,IAAI,IAAI,CAAC,OAAO;AAC1B,aAAO,WAAW,IAAI,EAAE;AAAA,IAC1B,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM;AACX,SAAK,WAAW,SAAS,CAAC,OAAO,UAAU;AACzC,aAAO,EAAE,eAAe,MAAM,gBAAgB,EAAE;AAAA,IAClD,CAAC;AAAA,EACH;AAAA,EACA,eAAe,MAAM;AACnB,SAAK,WAAW,SAAS,CAAC,OAAO,UAAU;AACzC,aAAO,EAAE,gBAAgB,MAAM,iBAAiB,EAAE;AAAA,IACpD,CAAC;AAAA,EACH;AAAA,EACA,SAAS,QAAQ;AACf,QAAI,KAAK,MAAM,aAAa,QAAQ;AAClC,YAAM,UAAU,KAAK,MAAM,SAAS,MAAM;AAC1C,UAAI,YAAY,QAAQ;AACtB,eAAO,KAAK,MAAM,MAAM,SAAS,OAAO;AAAA,MAC1C;AACA,aAAO;AAAA,IACT,OAAO;AACL,aAAO,KAAK,MAAM,MAAM,SAAS,MAAM;AAAA,IACzC;AAAA,EACF;AAAA,EACA,sBAAsB,KAAK;AACzB,UAAM,aAAa,KAAK,WAAW;AACnC,QAAI,YAAY;AACd,aAAO,KAAK,sBAAsB,GAAG,EAAE,WAAW,UAAU;AAAA,IAC9D;AACA,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,uBAAuB;AACrB,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA,mBAAmB,IAAI;AACrB,QAAI,kBAAkB,KAAK,mBAAmB,IAAI,EAAE;AACpD,QAAI,oBAAoB,QAAQ;AAC9B,wBAAkB,SAAS,cAAc,KAAK;AAC9C,WAAK,aAAa,QAAQ,YAAY,eAAe;AACrD,sBAAgB,YAAY,QAAQ;AACpC,WAAK,mBAAmB,IAAI,IAAI,eAAe;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa;AACX,QAAI,KAAK,QAAQ,SAAS;AACxB,aAAO,KAAK,YAAY,KAAK,QAAQ,QAAQ,sBAAsB,CAAC;AAAA,IACtE,OAAO;AACL,aAAO,KAAK,MAAM;AAAA,IACpB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa;AACX,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,aAAa;AACX,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,MAAM,kBAAkB;AAAA,EACtC;AAAA,EACA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc,SAAS;AACrB,SAAK,SAAS,EAAE,YAAY,QAAQ,CAAC;AAAA,EACvC;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,WAAW;AACT,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,cAAc,QAAQ;AACpB,UAAM,OAAO,OAAO,MAAM;AAC1B,UAAM,aAAa,KAAK,WAAW;AACnC,UAAM,YAAY;AAClB,UAAM,WAAW;AACjB,SAAK,IAAI,KAAK,cAAc,UAAU,KAAK,cAAc,UAAU,WAAW,IAAI,WAAW,IAAI,KAAK;AACtG,SAAK,IAAI,KAAK,cAAc,UAAU,KAAK,cAAc,WAAW,YAAY,WAAW,KAAK,WAAW,IAAI,KAAK;AACpH,SAAK,UAAU;AACf,SAAK,SAAS;AACd,WAAO;AAAA,EACT;AAAA,EACA,eAAe,UAAU,MAAM;AAC7B,UAAM,aAAa,KAAK,MAAM,MAAM,YAAY,QAAQ;AACxD,QAAI,eAAe,QAAQ;AACzB,YAAM,OAAO,KAAK,SAAS,QAAQ,QAAQ,MAAM,UAAU,aAAa,QAAQ,EAAE,CAAC;AACnF,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,MAAM;AACzB,UAAM,aAAa,KAAK,MAAM,MAAM,gBAAgB,KAAK,QAAQ;AACjE,QAAI,eAAe,QAAQ;AACzB,YAAM,OAAO,KAAK,SAAS,QAAQ,QAAQ,MAAM,WAAW,MAAM,GAAG,aAAa,QAAQ,EAAE,CAAC;AAC7F,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS,YAAY;AACnB,SAAK,SAAS,QAAQ,eAAe,WAAW,MAAM,GAAG,KAAK,YAAY,CAAC,CAAC;AAAA,EAC9E;AAAA,EACA,aAAa,SAAS,cAAc;AAClC,QAAI,KAAK,MAAM,aAAa;AAC1B,WAAK,MAAM,YAAY,SAAS,YAAY;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,gBAAgB,YAAY,cAAc;AACxC,QAAI,KAAK,MAAM,gBAAgB;AAC7B,WAAK,MAAM,eAAe,YAAY,YAAY;AAAA,IACpD;AAAA,EACF;AAAA,EACA,SAAS,IAAI,OAAO;AAClB,QAAI;AACJ,QAAI,KAAK,MAAM,YAAY;AACzB,gBAAU,KAAK,MAAM,WAAW,IAAI,KAAK;AAAA,IAC3C;AACA,QAAI,YAAY,QAAQ;AACtB,gBAAU,MAAM,UAAU,SAAS,KAAK;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,+BAA+B;AAC7B,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,gBAAgB,MAAM,OAAO;AAC3B,QAAI,KAAK,MAAM,eAAe;AAC5B,WAAK,MAAM,cAAc,MAAM,KAAK;AAAA,IACtC;AAAA,EACF;AAAA,EACA,cAAc,MAAM,OAAO;AACzB,QAAI,KAAK,MAAM,iBAAiB;AAC9B,WAAK,MAAM,gBAAgB,MAAM,KAAK;AAAA,IACxC;AAAA,EACF;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,SAAS,EAAE,aAAa,KAAK,CAAC;AACnC,2BAAuB,CAAC,MAAM,KAAK,eAAe;AAAA,EACpD;AAAA;AAAA,EAEA,sBAAsB,OAAO,MAAM,QAAQ;AACzC,UAAM,WAAW,QAAQ,SAAS,MAAM,KAAK,MAAM,OAAO,KAAK;AAC/D,IAAAA,iBAAgB,YAAY,IAAI,UAAU,KAAK,YAAY,OAAO,UAAU,MAAM,MAAM;AAAA,EAC1F;AAAA,EACA,uBAAuB,OAAO,MAAM;AAClC,SAAK,YAAY,OAAO,IAAI;AAAA,EAC9B;AAAA,EACA,iBAAiB,OAAO,WAAW,GAAG,GAAG;AACvC,UAAM,kBAA8B;AAAA,MAClC;AAAA,MACA;AAAA,QACE,OAAO,EAAE,UAAU,QAAQ;AAAA,QAC3B,WAAW,KAAK,aAAa,QAAQ,kBAAkB,IAAI,MAAM,KAAK,aAAa,QAAQ,qBAAqB;AAAA,QAChH,UAAU;AAAA,MACZ;AAAA,IACF;AACA,UAAM,UAAU,KAAK,gBAAgB,cAAc,KAAK;AACxD,YAAQ,aAAa,oBAAoB,iBAAiB;AAC1D,YAAQ,MAAM,WAAW;AACzB,YAAQ,MAAM,OAAO;AACrB,YAAQ,MAAM,MAAM;AACpB,SAAK,gBAAgB,KAAK,YAAY,OAAO;AAC7C,kCAAW,OAAO,EAAE,OAAO,WAAW;AACtC,UAAM,aAAa,aAAa,SAAS,GAAG,CAAC;AAC7C,eAAW,MAAM;AACf,WAAK,gBAAgB,KAAK,YAAY,OAAO;AAAA,IAC/C,GAAG,CAAC;AAAA,EACN;AAAA,EACA,sBAAsB,YAAY;AAChC,QAAI,KAAK,yBAAyB,YAAY;AAC5C,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,MAAM,aAAa,aAAa,WAAW;AAAA,MAC7D;AACA,UAAI,YAAY;AACd,aAAK,SAAS,EAAE,WAAW,MAAM,CAAC;AAAA,MACpC,OAAO;AACL,YAAI,KAAK,MAAM,MAAM,mBAAmB,KAAK,QAAQ,MAAM,QAAQ;AACjE,eAAK,SAAS,EAAE,WAAW,KAAK,MAAM,MAAM,iBAAiB,EAAE,CAAC;AAAA,QAClE;AAAA,MACF;AACA,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,IAAAA,iBAAgB,YAAY;AAC5B,QAAI,KAAK,aAAa,MAAM,gBAAgB;AAC1C,WAAK,uBAAuB;AAAA,IAC9B;AACA,eAAW,CAAC,EAAE,YAAY,KAAK,KAAK,MAAM,MAAM,cAAc,GAAG;AAC/D,mBAAa,OAAO,eAAe;AAAA,IACrC;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,SAAK,SAAS,EAAE,WAAW,MAAM,CAAC;AAClC,SAAK,YAAY,KAAK;AACtB,SAAK,iBAAiB;AACtB,SAAK,WAAW;AAChB,QAAI,KAAK,YAAY;AACnB,WAAK,QAAQ,QAAQ,YAAY,KAAK,UAAU;AAChD,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA;AAEF;AACA,cAAc,iBAAiB,WAAW;AAC1C,IAAI,iBAAiB;AACrB,IAAM,oBAAoB;AAC1B,IAAM,eAAe;AAAA,EACnB,WAAuB,wBAAI,WAAW,CAAC,CAAC;AAAA,EACxC,iBAA6B,wBAAI,WAAW,CAAC,CAAC;AAAA,EAC9C,YAAwB,wBAAI,YAAY,CAAC,CAAC;AAAA,EAC1C,cAA0B,wBAAI,cAAc,CAAC,CAAC;AAAA,EAC9C,aAAyB,wBAAI,aAAa,CAAC,CAAC;AAAA,EAC5C,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,EAC1C,eAA2B,wBAAI,UAAU,CAAC,CAAC;AAAA,EAC3C,kBAA8B,wBAAI,cAAc,CAAC,CAAC;AACpD;AACA,IAAM,wBAAwB,UAAU;AACxC,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,YAAY,YAAY,UAAU,UAAU,kBAAkB;AACxE,kBAAc,MAAM,YAAY;AAChC,kBAAc,MAAM,YAAY;AAChC,kBAAc,MAAM,UAAU;AAC9B,kBAAc,MAAM,UAAU;AAC9B,kBAAc,MAAM,kBAAkB;AACtC,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,mBAAmB;AAAA,EAC1B;AACF;", "names": ["_Orientation", "_DockLocation", "_Actions", "_TabNode", "_TabSetNode", "_RowNode", "_Model", "_BorderNode", "_LayoutInternal"]}