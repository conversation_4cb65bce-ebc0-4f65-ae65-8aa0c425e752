{"version": 3, "sources": ["../../src/hooks/useOptionalFactory.ts"], "sourcesContent": ["import { useMemo } from 'react'\n\nimport type { FactoryOrInstance } from './types.js'\n\nexport function useOptionalFactory<T>(\n\targ: FactoryOrInstance<T>,\n\tdeps?: unknown[],\n): T {\n\tconst memoDeps = [...(deps || [])]\n\tif (deps == null && typeof arg !== 'function') {\n\t\tmemoDeps.push(arg)\n\t}\n\treturn useMemo<T>(() => {\n\t\treturn typeof arg === 'function' ? (arg as () => T)() : (arg as T)\n\t}, memoDeps)\n}\n"], "names": ["useMemo", "useOptionalFactory", "arg", "deps", "memoDeps", "push"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO,CAAA;AAI/B,OAAO,SAASC,kBAAkB,CACjCC,GAAyB,EACzBC,IAAgB,EACZ;IACJ,MAAMC,QAAQ,GAAG;WAAKD,IAAI,IAAI,EAAE;KAAE;IAClC,IAAIA,IAAI,IAAI,IAAI,IAAI,OAAOD,GAAG,KAAK,UAAU,EAAE;QAC9CE,QAAQ,CAACC,IAAI,CAACH,GAAG,CAAC;KAClB;IACD,OAAOF,OAAO,CAAI,IAAM;QACvB,OAAO,OAAOE,GAAG,KAAK,UAAU,GAAG,AAACA,GAAG,EAAc,GAAIA,GAAG,AAAM,CAAA;KAClE,EAAEE,QAAQ,CAAC,CAAA;CACZ"}