{"version": 3, "sources": ["../../../src/hooks/useDrop/connectors.ts"], "sourcesContent": ["import { useMemo } from 'react'\n\nimport type { TargetConnector } from '../../internals/index.js'\n\nexport function useConnectDropTarget(connector: TargetConnector) {\n\treturn useMemo(() => connector.hooks.dropTarget(), [connector])\n}\n"], "names": ["useMemo", "useConnectDropTarget", "connector", "hooks", "drop<PERSON>ar<PERSON>"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO,CAAA;AAI/B,OAAO,SAASC,oBAAoB,CAACC,SAA0B,EAAE;IAChE,OAAOF,OAAO,CAAC,IAAME,SAAS,CAACC,KAAK,CAACC,UAAU,EAAE;IAAA,EAAE;QAACF,SAAS;KAAC,CAAC,CAAA;CAC/D"}