{"version": 3, "sources": ["../../src/hooks/useIsomorphicLayoutEffect.ts"], "sourcesContent": ["import { useEffect, useLayoutEffect } from 'react'\n\n// suppress the useLayoutEffect warning on server side.\nexport const useIsomorphicLayoutEffect =\n\ttypeof window !== 'undefined' ? useLayoutEffect : useEffect\n"], "names": ["useEffect", "useLayoutEffect", "useIsomorphicLayoutEffect", "window"], "mappings": "AAAA,SAASA,SAAS,EAAEC,eAAe,QAAQ,OAAO,CAAA;AAElD,uDAAuD;AACvD,OAAO,MAAMC,yBAAyB,GACrC,OAAOC,MAAM,KAAK,WAAW,GAAGF,eAAe,GAAGD,SAAS,CAAA"}