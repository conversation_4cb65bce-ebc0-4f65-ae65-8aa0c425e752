{"version": 3, "sources": ["../../src/types/connectors.ts"], "sourcesContent": ["import type { ReactElement, RefObject } from 'react'\n\nimport type { DragPreviewOptions, DragSourceOptions } from './options'\n\nexport type ConnectableElement = RefObject<any> | ReactElement | Element | null\n\nexport type DragElementWrapper<Options> = (\n\telementOrNode: ConnectableElement,\n\toptions?: Options,\n) => ReactElement | null\n\nexport type ConnectDragSource = DragElementWrapper<DragSourceOptions>\nexport type ConnectDropTarget = DragElementWrapper<any>\nexport type ConnectDragPreview = DragElementWrapper<DragPreviewOptions>\n"], "names": [], "mappings": "AAAA,WAauE"}