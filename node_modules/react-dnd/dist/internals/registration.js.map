{"version": 3, "sources": ["../../src/internals/registration.ts"], "sourcesContent": ["import type {\n\tDragDropManager,\n\tDragSource,\n\tDropTarget,\n\tIdentifier,\n\tSourceType,\n\tTargetType,\n\tUnsubscribe,\n} from 'dnd-core'\n\nexport function registerTarget(\n\ttype: TargetType,\n\ttarget: DropTarget,\n\tmanager: DragDropManager,\n): [Identifier, Unsubscribe] {\n\tconst registry = manager.getRegistry()\n\tconst targetId = registry.addTarget(type, target)\n\n\treturn [targetId, () => registry.removeTarget(targetId)]\n}\n\nexport function registerSource(\n\ttype: SourceType,\n\tsource: DragSource,\n\tmanager: DragDropManager,\n): [Identifier, Unsubscribe] {\n\tconst registry = manager.getRegistry()\n\tconst sourceId = registry.addSource(type, source)\n\n\treturn [sourceId, () => registry.removeSource(sourceId)]\n}\n"], "names": ["registerTarget", "type", "target", "manager", "registry", "getRegistry", "targetId", "addTarget", "remove<PERSON>arget", "registerSource", "source", "sourceId", "addSource", "removeSource"], "mappings": "AAUA,OAAO,SAASA,cAAc,CAC7BC,IAAgB,EAChBC,MAAkB,EAClBC,OAAwB,EACI;IAC5B,MAAMC,QAAQ,GAAGD,OAAO,CAACE,WAAW,EAAE;IACtC,MAAMC,QAAQ,GAAGF,QAAQ,CAACG,SAAS,CAACN,IAAI,EAAEC,MAAM,CAAC;IAEjD,OAAO;QAACI,QAAQ;QAAE,IAAMF,QAAQ,CAACI,YAAY,CAACF,QAAQ,CAAC;KAAC,CAAA;CACxD;AAED,OAAO,SAASG,cAAc,CAC7BR,IAAgB,EAChBS,MAAkB,EAClBP,OAAwB,EACI;IAC5B,MAAMC,QAAQ,GAAGD,OAAO,CAACE,WAAW,EAAE;IACtC,MAAMM,QAAQ,GAAGP,QAAQ,CAACQ,SAAS,CAACX,IAAI,EAAES,MAAM,CAAC;IAEjD,OAAO;QAACC,QAAQ;QAAE,IAAMP,QAAQ,CAACS,YAAY,CAACF,QAAQ,CAAC;KAAC,CAAA;CACxD"}