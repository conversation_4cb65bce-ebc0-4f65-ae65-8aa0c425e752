@import 'react-mosaic-component/react-mosaic-component.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Mosaic Theme for LeetCode */
.mosaic-leetcode-theme {
  background-color: #1a1a1a;
}

.mosaic-leetcode-theme .mosaic-tile {
  background-color: #1a1a1a;
  border: 1px solid #333;
}

.mosaic-leetcode-theme .mosaic-window {
  background-color: #1a1a1a;
  border: 1px solid #333;
}

.mosaic-leetcode-theme .mosaic-window-title {
  background-color: #262626;
  color: #ffffff;
  border-bottom: 1px solid #333;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 12px;
}

.mosaic-leetcode-theme .mosaic-window-title:hover {
  background-color: #2d2d2d;
}

.mosaic-leetcode-theme .mosaic-window-controls {
  background-color: #262626;
}

.mosaic-leetcode-theme .mosaic-window-controls .mosaic-default-control {
  color: #999;
  background-color: transparent;
  border: none;
  padding: 4px;
  margin: 2px;
  border-radius: 3px;
}

.mosaic-leetcode-theme .mosaic-window-controls .mosaic-default-control:hover {
  background-color: #3d3d3d;
  color: #ffffff;
}

.mosaic-leetcode-theme .mosaic-split {
  background-color: #333;
}

.mosaic-leetcode-theme .mosaic-split:hover {
  background-color: #00b8a3;
}

.mosaic-leetcode-theme .mosaic-split.-row {
  cursor: col-resize;
}

.mosaic-leetcode-theme .mosaic-split.-column {
  cursor: row-resize;
}

/* Drop targets */
.mosaic-leetcode-theme .mosaic-drop-target {
  background-color: rgba(0, 184, 163, 0.2);
  border: 2px dashed #00b8a3;
}

.mosaic-leetcode-theme .mosaic-drop-target.drop-target-hover {
  background-color: rgba(0, 184, 163, 0.3);
}

/* Window body */
.mosaic-window-leetcode .mosaic-window-body {
  background-color: #1a1a1a;
  overflow: hidden;
}

/* Enhanced drag and drop styling */
.mosaic-leetcode-theme .mosaic-drop-target-container {
  position: relative;
}

.mosaic-leetcode-theme .mosaic-drop-target {
  background-color: rgba(0, 184, 163, 0.15);
  border: 2px dashed #00b8a3;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.mosaic-leetcode-theme .mosaic-drop-target.drop-target-hover {
  background-color: rgba(0, 184, 163, 0.25);
  border-color: #00d4aa;
  box-shadow: 0 0 20px rgba(0, 184, 163, 0.3);
}

/* Window title bar enhancements */
.mosaic-leetcode-theme .mosaic-window-title {
  background: linear-gradient(135deg, #262626 0%, #2d2d2d 100%);
  color: #ffffff;
  border-bottom: 1px solid #333;
  font-size: 13px;
  font-weight: 500;
  padding: 6px 12px;
  user-select: none;
  cursor: grab;
}

.mosaic-leetcode-theme .mosaic-window-title:active {
  cursor: grabbing;
}

/* Split pane styling */
.mosaic-leetcode-theme .mosaic-split {
  background-color: #333;
  transition: background-color 0.2s ease;
  position: relative;
}

.mosaic-leetcode-theme .mosaic-split:hover {
  background-color: #00b8a3;
}

.mosaic-leetcode-theme .mosaic-split:hover::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  pointer-events: none;
}

/* Window controls */
.mosaic-leetcode-theme .mosaic-window-controls .mosaic-default-control {
  color: #999;
  background-color: transparent;
  border: none;
  padding: 6px;
  margin: 1px;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.mosaic-leetcode-theme .mosaic-window-controls .mosaic-default-control:hover {
  background-color: #3d3d3d;
  color: #ffffff;
  transform: scale(1.1);
}

/* Toolbar styling */
.mosaic-toolbar {
  background: linear-gradient(135deg, #1a1a1a 0%, #262626 100%);
  border-bottom: 2px solid #333;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100vh;
  width: 100vw;
}
