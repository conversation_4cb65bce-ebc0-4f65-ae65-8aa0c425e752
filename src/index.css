@import 'flexlayout-react/style/light.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* FlexLayout Custom Styling for LeetCode Theme */
.flexlayout-container {
  background-color: #1a1a1a;
}

/* Override FlexLayout default styles */
.flexlayout__layout {
  background-color: #1a1a1a !important;
}

.flexlayout__splitter {
  background-color: #333 !important;
  transition: background-color 0.2s ease;
}

.flexlayout__splitter:hover {
  background-color: #00b8a3 !important;
}

.flexlayout__splitter_vertical {
  cursor: col-resize;
}

.flexlayout__splitter_horizontal {
  cursor: row-resize;
}

/* Tab styling */
.flexlayout__tab_leetcode {
  background-color: #262626 !important;
  color: #ffffff !important;
  border: none !important;
  border-bottom: 2px solid transparent !important;
  padding: 8px 16px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.flexlayout__tab_leetcode:hover {
  background-color: #2d2d2d !important;
  color: #ffffff !important;
}

.flexlayout__tab_selected_leetcode {
  background-color: #1a1a1a !important;
  border-bottom-color: #00b8a3 !important;
  color: #ffffff !important;
}

/* Tabset styling */
.flexlayout__tabset_tabstrip_leetcode {
  background-color: #262626 !important;
  border-bottom: 1px solid #333 !important;
}

.flexlayout__tabset_header_leetcode {
  background-color: #262626 !important;
  border-bottom: 1px solid #333 !important;
}

.flexlayout__tabset_content {
  background-color: #1a1a1a !important;
  overflow: hidden !important;
}

/* Tab close button */
.flexlayout__tab_button_trailing {
  color: #999 !important;
  margin-left: 8px !important;
  padding: 2px !important;
  border-radius: 2px !important;
  transition: all 0.2s ease !important;
}

.flexlayout__tab_button_trailing:hover {
  background-color: #ff4757 !important;
  color: #ffffff !important;
}

/* Drop zones */
.flexlayout__outline_rect {
  background-color: rgba(0, 184, 163, 0.3) !important;
  border: 2px dashed #00b8a3 !important;
  border-radius: 4px !important;
}

.flexlayout__edge_rect {
  background-color: rgba(0, 184, 163, 0.8) !important;
  border-radius: 4px !important;
}

/* Floating windows */
.flexlayout__floating_window {
  background-color: #1a1a1a !important;
  border: 1px solid #333 !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
}

.flexlayout__floating_window_tab {
  background-color: #262626 !important;
  color: #ffffff !important;
  border-bottom: 1px solid #333 !important;
}

/* Toolbar styling */
.flexlayout-toolbar {
  background: linear-gradient(135deg, #1a1a1a 0%, #262626 100%);
  border-bottom: 2px solid #333;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100vh;
  width: 100vw;
}
