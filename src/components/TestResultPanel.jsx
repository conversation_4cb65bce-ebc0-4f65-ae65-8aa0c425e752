import { Check<PERSON>ir<PERSON>, XCircle, Clock, AlertCircle, Play, BarChart3 } from 'lucide-react'

const TestResultPanel = ({ testResults, isRunning, onRunTests, problem }) => {
  if (!testResults && !isRunning) {
    return (
      <div className="h-full flex flex-col bg-leetcode-dark">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center p-8">
            <BarChart3 className="w-16 h-16 mx-auto mb-4 text-leetcode-text-secondary" />
            <h3 className="text-lg font-medium text-white mb-2">No Test Results Yet</h3>
            <p className="text-leetcode-text-secondary mb-6 max-w-md">
              {problem
                ? "Run your code to see detailed test results, execution time, and pass/fail status here."
                : "Generate a problem and write some code, then run tests to see results here."
              }
            </p>
            {problem && (
              <button
                onClick={onRunTests}
                className="px-4 py-2 bg-leetcode-green text-white rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-leetcode-green font-medium"
              >
                Run Tests
              </button>
            )}
            {!problem && (
              <div className="bg-leetcode-light border border-leetcode-border rounded-lg p-4 max-w-sm mx-auto">
                <p className="text-xs text-leetcode-text-secondary">
                  💡 <strong>Tip:</strong> Generate a problem from Settings to start testing your code
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  if (isRunning) {
    return (
      <div className="h-full flex flex-col bg-leetcode-dark">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-leetcode-light rounded-full flex items-center justify-center">
              <div className="w-8 h-8 border-2 border-leetcode-green border-t-transparent rounded-full animate-spin"></div>
            </div>
            <h3 className="text-lg font-medium text-white mb-2">Running tests...</h3>
            <p className="text-leetcode-text-secondary">
              Please wait while we execute your code
            </p>
          </div>
        </div>
      </div>
    )
  }

  const { passed, failed, total, results, executionTime, error } = testResults

  return (
    <div className="h-full flex flex-col bg-leetcode-dark">
      {/* Results Header */}
      <div className="bg-leetcode-darker border-b border-leetcode-border p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-2 ${passed === total ? 'text-leetcode-green' : 'text-leetcode-red'}`}>
              {passed === total ? (
                <CheckCircle className="w-5 h-5" />
              ) : (
                <XCircle className="w-5 h-5" />
              )}
              <span className="font-medium">
                {passed === total ? 'All tests passed!' : `${failed} test(s) failed`}
              </span>
            </div>
            <div className="text-leetcode-text-secondary text-sm">
              {passed}/{total} test cases passed
            </div>
          </div>
          
          {executionTime && (
            <div className="flex items-center space-x-1 text-leetcode-text-secondary text-sm">
              <Clock className="w-4 h-4" />
              <span>{executionTime}ms</span>
            </div>
          )}
        </div>

        {error && (
          <div className="mt-3 p-3 bg-leetcode-red bg-opacity-20 border border-leetcode-red rounded-md">
            <div className="flex items-center space-x-2 text-leetcode-red mb-2">
              <AlertCircle className="w-4 h-4" />
              <span className="font-medium">Compilation/Runtime Error</span>
            </div>
            <pre className="text-sm text-leetcode-red font-mono whitespace-pre-wrap">
              {error}
            </pre>
          </div>
        )}

        {/* Global Stdout/Stderr if available */}
        {testResults.globalStdout && testResults.globalStdout.length > 0 && (
          <div className="mt-3 p-3 bg-leetcode-light border border-leetcode-border rounded-md">
            <div className="text-leetcode-text-secondary font-medium mb-2">Console Output:</div>
            <pre className="text-sm text-white font-mono whitespace-pre-wrap">
              {testResults.globalStdout.join('\n')}
            </pre>
          </div>
        )}

        {testResults.globalStderr && testResults.globalStderr.length > 0 && (
          <div className="mt-3 p-3 bg-leetcode-red bg-opacity-20 border border-leetcode-red rounded-md">
            <div className="text-leetcode-red font-medium mb-2">Error Output:</div>
            <pre className="text-sm text-leetcode-red font-mono whitespace-pre-wrap">
              {testResults.globalStderr.join('\n')}
            </pre>
          </div>
        )}
      </div>

      {/* Test Results */}
      <div className="flex-1 overflow-y-auto">
        {results && results.map((result, index) => (
          <div
            key={index}
            className={`border-b border-leetcode-border p-4 ${
              result.passed ? 'bg-leetcode-dark' : 'bg-leetcode-red bg-opacity-5'
            }`}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                {result.passed ? (
                  <CheckCircle className="w-4 h-4 text-leetcode-green" />
                ) : (
                  <XCircle className="w-4 h-4 text-leetcode-red" />
                )}
                <span className="font-medium text-white">Test Case {index + 1}</span>
              </div>
              <span className={`text-sm ${result.passed ? 'text-leetcode-green' : 'text-leetcode-red'}`}>
                {result.passed ? 'Passed' : 'Failed'}
              </span>
            </div>

            {/* Input */}
            <div className="mb-3">
              <div className="text-sm text-leetcode-text-secondary mb-1">Input:</div>
              <div className="bg-leetcode-light p-2 rounded text-sm font-mono text-white">
                {Object.entries(result.input).map(([key, value]) => (
                  <div key={key}>
                    <span className="text-leetcode-text-secondary">{key} = </span>
                    <span>{value}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Expected vs Actual Output */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-leetcode-text-secondary mb-1">Expected:</div>
                <div className="bg-leetcode-light p-2 rounded text-sm font-mono text-white">
                  {result.expected}
                </div>
              </div>
              <div>
                <div className="text-sm text-leetcode-text-secondary mb-1">
                  {result.passed ? 'Output:' : 'Your Output:'}
                </div>
                <div className={`p-2 rounded text-sm font-mono ${
                  result.passed 
                    ? 'bg-leetcode-green bg-opacity-20 text-leetcode-green' 
                    : 'bg-leetcode-red bg-opacity-20 text-leetcode-red'
                }`}>
                  {result.actual}
                </div>
              </div>
            </div>

            {/* Stdout Output */}
            {result.stdout && result.stdout.length > 0 && (
              <div className="mt-3 p-2 bg-leetcode-light border border-leetcode-border rounded text-sm">
                <div className="text-leetcode-text-secondary font-medium mb-1">Stdout:</div>
                <pre className="text-white font-mono whitespace-pre-wrap text-xs">
                  {result.stdout.join('\n')}
                </pre>
              </div>
            )}

            {/* Stderr Output */}
            {result.stderr && result.stderr.length > 0 && (
              <div className="mt-3 p-2 bg-leetcode-red bg-opacity-20 border border-leetcode-red rounded text-sm">
                <div className="text-leetcode-red font-medium mb-1">Stderr:</div>
                <pre className="text-leetcode-red font-mono whitespace-pre-wrap text-xs">
                  {result.stderr.join('\n')}
                </pre>
              </div>
            )}

            {/* Runtime Error for this specific test case */}
            {result.error && (
              <div className="mt-3 p-2 bg-leetcode-red bg-opacity-20 border border-leetcode-red rounded text-sm">
                <div className="text-leetcode-red font-medium mb-1">Runtime Error:</div>
                <pre className="text-leetcode-red font-mono whitespace-pre-wrap text-xs">
                  {result.error}
                </pre>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Run Again Button */}
      <div className="border-t border-leetcode-border p-4">
        <button
          onClick={onRunTests}
          className="w-full px-4 py-2 bg-leetcode-green text-white rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-leetcode-green font-medium flex items-center justify-center space-x-2"
        >
          <Play className="w-4 h-4" />
          <span>Run Again</span>
        </button>
      </div>
    </div>
  )
}

export default TestResultPanel
