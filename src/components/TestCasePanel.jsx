import { useState } from 'react'
import { <PERSON>, <PERSON>, <PERSON>O<PERSON>, Play, Trash2 } from 'lucide-react'

const TestCasePanel = ({ problem, onRunTests }) => {
  const [activeCase, setActiveCase] = useState(0)
  const [testCases, setTestCases] = useState(() => {
    // Initialize with problem test cases if available
    if (problem.testCases && problem.testCases.length > 0) {
      return problem.testCases.map((testCase, index) => ({
        id: index + 1,
        inputs: testCase.inputs || { input: 'test' },
        expectedOutput: testCase.expectedOutput || 'output',
        description: testCase.description || `Test case ${index + 1}`,
        isVisible: true
      }))
    }

    // Fallback to default test cases
    return [
      {
        id: 1,
        inputs: { root: '[3,1,4,null,2]', k: '1' },
        expectedOutput: '1',
        description: 'Basic test case',
        isVisible: true
      },
      {
        id: 2,
        inputs: { root: '[5,3,6,2,4,null,null,1]', k: '3' },
        expectedOutput: '3',
        description: 'Another test case',
        isVisible: true
      }
    ]
  })
  const [showVisualization, setShowVisualization] = useState(true)

  const addTestCase = () => {
    const newCase = {
      id: testCases.length + 1,
      inputs: Object.keys(testCases[0].inputs).reduce((acc, key) => {
        acc[key] = ''
        return acc
      }, {}),
      expectedOutput: '',
      description: `Test case ${testCases.length + 1}`,
      isVisible: true
    }
    setTestCases([...testCases, newCase])
    setActiveCase(testCases.length)
  }

  const removeTestCase = (index) => {
    if (testCases.length > 1) {
      const newTestCases = testCases.filter((_, i) => i !== index)
      setTestCases(newTestCases)
      if (activeCase >= newTestCases.length) {
        setActiveCase(newTestCases.length - 1)
      }
    }
  }

  const updateTestCase = (index, field, value) => {
    const newTestCases = [...testCases]
    if (field.startsWith('input.')) {
      const inputKey = field.split('.')[1]
      newTestCases[index].inputs[inputKey] = value
    } else {
      newTestCases[index][field] = value
    }
    setTestCases(newTestCases)
  }

  const handleRunTests = () => {
    console.log('🧪 TestCasePanel: Run Tests clicked!')
    console.log('🧪 Test cases to run:', testCases)
    console.log('🧪 onRunTests function:', typeof onRunTests)
    if (onRunTests) {
      onRunTests(testCases)
    } else {
      console.error('❌ onRunTests function not provided to TestCasePanel!')
    }
  }

  const currentCase = testCases[activeCase]

  return (
    <div className="h-full flex flex-col bg-leetcode-dark">
      {/* Test Case Tabs */}
      <div className="bg-leetcode-darker border-b border-leetcode-border p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            {testCases.map((testCase, index) => (
              <button
                key={testCase.id}
                onClick={() => setActiveCase(index)}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  activeCase === index
                    ? 'bg-leetcode-light text-white'
                    : 'text-leetcode-text-secondary hover:text-white hover:bg-leetcode-light'
                }`}
              >
                Case {index + 1}
              </button>
            ))}
            <button
              onClick={addTestCase}
              className="p-1 text-leetcode-text-secondary hover:text-leetcode-green hover:bg-leetcode-light rounded transition-colors"
              title="Add test case"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowVisualization(!showVisualization)}
              className="p-1 text-leetcode-text-secondary hover:text-white hover:bg-leetcode-light rounded transition-colors"
              title={showVisualization ? "Hide visualization" : "Show visualization"}
            >
              {showVisualization ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
            {testCases.length > 1 && (
              <button
                onClick={() => removeTestCase(activeCase)}
                className="p-1 text-leetcode-text-secondary hover:text-leetcode-red hover:bg-leetcode-light rounded transition-colors"
                title="Remove test case"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Test Case Content */}
      <div className="flex-1 p-4 overflow-y-auto">
        {currentCase && (
          <div className="space-y-4">
            {/* Test Case Description */}
            <div>
              <label className="block text-sm font-medium text-leetcode-text-secondary mb-2">
                Description:
              </label>
              <input
                type="text"
                value={currentCase.description}
                onChange={(e) => updateTestCase(activeCase, 'description', e.target.value)}
                className="w-full px-3 py-2 bg-leetcode-light border border-leetcode-border rounded-md text-sm text-white focus:outline-none focus:ring-2 focus:ring-leetcode-green"
                placeholder="Describe this test case..."
              />
            </div>

            {/* Input Fields */}
            {Object.entries(currentCase.inputs).map(([key, value]) => (
              <div key={key}>
                <label className="block text-sm font-medium text-leetcode-text-secondary mb-2">
                  {key} =
                </label>
                <textarea
                  value={value}
                  onChange={(e) => updateTestCase(activeCase, `input.${key}`, e.target.value)}
                  className="w-full h-16 px-3 py-2 bg-leetcode-light border border-leetcode-border rounded-md text-sm text-white font-mono resize-none focus:outline-none focus:ring-2 focus:ring-leetcode-green"
                  placeholder={`Enter ${key} value...`}
                />
              </div>
            ))}

            {/* Visualization Area */}
            {showVisualization && (
              <div className="bg-leetcode-light border border-leetcode-border rounded-md p-4">
                <div className="text-sm text-leetcode-text-secondary mb-2">Visualization:</div>
                <div className="h-32 flex items-center justify-center text-leetcode-text-secondary">
                  {/* Simple tree visualization placeholder */}
                  <div className="text-center">
                    <div className="text-lg font-mono">Tree Structure</div>
                    <div className="text-xs mt-2">Visual representation would go here</div>
                  </div>
                </div>
              </div>
            )}

            {/* Expected Output */}
            <div>
              <label className="block text-sm font-medium text-leetcode-text-secondary mb-2">
                Expected Output:
              </label>
              <input
                type="text"
                value={currentCase.expectedOutput}
                onChange={(e) => updateTestCase(activeCase, 'expectedOutput', e.target.value)}
                className="w-full px-3 py-2 bg-leetcode-light border border-leetcode-border rounded-md text-sm text-white font-mono focus:outline-none focus:ring-2 focus:ring-leetcode-green"
                placeholder="Enter expected output..."
              />
            </div>
          </div>
        )}
      </div>

      {/* Run Tests Button */}
      <div className="border-t border-leetcode-border p-4">
        <button
          onClick={handleRunTests}
          className="w-full px-4 py-2 bg-leetcode-green text-white rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-leetcode-green font-medium flex items-center justify-center space-x-2"
        >
          <Play className="w-4 h-4" />
          <span>Run Tests</span>
        </button>
      </div>
    </div>
  )
}

export default TestCasePanel
