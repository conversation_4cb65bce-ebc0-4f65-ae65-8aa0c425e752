import { useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react'
import { leetcodeCategories } from '../data/problems'

const SettingsPanel = ({ onNewProblem }) => {
  const [selectedCategories, setSelectedCategories] = useState(['Array', 'String'])
  const [aiPrompt, setAiPrompt] = useState(
    "You are an experienced technical interviewer conducting a coding interview. Be encouraging but challenging. Ask follow-up questions about time/space complexity, edge cases, and optimizations. Provide hints when the candidate is stuck, but don't give away the solution immediately."
  )

  const handleCategoryToggle = (category) => {
    setSelectedCategories(prev => 
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
  }

  const handleSelectAll = () => {
    setSelectedCategories(leetcodeCategories)
  }

  const handleSelectNone = () => {
    setSelectedCategories([])
  }

  const handleGenerateNewProblem = () => {
    const categoriesToUse = selectedCategories.length > 0 ? selectedCategories : ['Array']
    onNewProblem(categoriesToUse)
  }

  return (
    <div className="h-full overflow-y-auto">
      {/* Header */}
      <div className="bg-gray-50 border-b border-gray-200 p-4">
        <div className="flex items-center space-x-2">
          <Settings className="w-5 h-5 text-gray-600" />
          <h2 className="text-lg font-semibold text-gray-800">Settings</h2>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* AI Interviewer Prompt */}
        <div>
          <div className="flex items-center space-x-2 mb-3">
            <Bot className="w-4 h-4 text-gray-600" />
            <h3 className="text-sm font-medium text-gray-800">AI Interviewer Prompt</h3>
          </div>
          
          <textarea
            value={aiPrompt}
            onChange={(e) => setAiPrompt(e.target.value)}
            placeholder="Customize how the AI interviewer behaves..."
            className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
          />
          
          <p className="text-xs text-gray-500 mt-2">
            This prompt defines how the AI interviewer will interact with you during the session.
          </p>
        </div>

        {/* Problem Categories */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-800">Problem Categories</h3>
            <div className="flex space-x-2">
              <button
                onClick={handleSelectAll}
                className="text-xs text-blue-600 hover:text-blue-800"
              >
                Select All
              </button>
              <button
                onClick={handleSelectNone}
                className="text-xs text-gray-600 hover:text-gray-800"
              >
                Clear All
              </button>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2 max-h-64 overflow-y-auto">
            {leetcodeCategories.map((category) => {
              const isSelected = selectedCategories.includes(category)
              return (
                <button
                  key={category}
                  onClick={() => handleCategoryToggle(category)}
                  className={`flex items-center space-x-2 p-2 rounded-md text-left text-sm transition-colors ${
                    isSelected
                      ? 'bg-blue-100 text-blue-800 border border-blue-300'
                      : 'bg-gray-50 text-gray-700 border border-gray-200 hover:bg-gray-100'
                  }`}
                >
                  {isSelected ? (
                    <CheckSquare className="w-4 h-4" />
                  ) : (
                    <Square className="w-4 h-4" />
                  )}
                  <span className="truncate">{category}</span>
                </button>
              )
            })}
          </div>

          <p className="text-xs text-gray-500 mt-2">
            Selected: {selectedCategories.length} categories
          </p>
        </div>

        {/* Generate New Problem */}
        <div className="border-t border-gray-200 pt-4">
          <button
            onClick={handleGenerateNewProblem}
            className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors"
          >
            <Shuffle className="w-4 h-4" />
            <span>Generate New Problem</span>
          </button>
          
          <p className="text-xs text-gray-500 mt-2 text-center">
            Get a random problem from your selected categories
          </p>
        </div>
      </div>
    </div>
  )
}

export default SettingsPanel
