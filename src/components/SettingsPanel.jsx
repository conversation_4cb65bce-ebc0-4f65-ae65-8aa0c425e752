import { useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>re, <PERSON> } from 'lucide-react'
import { leetcodeCategories } from '../data/problems'

const SettingsPanel = ({ onNewProblem, customPrompt, setCustomPrompt }) => {
  const [selectedCategories, setSelectedCategories] = useState(['Array', 'String'])

  // Use the custom prompt from props, with a default fallback
  const aiPrompt = customPrompt || "You are an experienced technical interviewer conducting a coding interview. Be encouraging but challenging. Ask follow-up questions about time/space complexity, edge cases, and optimizations. Provide hints when the candidate is stuck, but don't give away the solution immediately."

  const handleCategoryToggle = (category) => {
    setSelectedCategories(prev => 
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
  }

  const handleSelectAll = () => {
    setSelectedCategories(leetcodeCategories)
  }

  const handleSelectNone = () => {
    setSelectedCategories([])
  }

  const handleGenerateNewProblem = () => {
    const categoriesToUse = selectedCategories.length > 0 ? selectedCategories : ['Array']
    onNewProblem(categoriesToUse)
  }

  return (
    <div className="h-full overflow-y-auto bg-leetcode-dark">
      {/* Header */}
      <div className="bg-leetcode-darker border-b border-leetcode-border p-4">
        <div className="flex items-center space-x-2">
          <Settings className="w-5 h-5 text-leetcode-green" />
          <h2 className="text-lg font-semibold text-white">Settings</h2>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* AI Interviewer Prompt */}
        <div>
          <div className="flex items-center space-x-2 mb-3">
            <Bot className="w-4 h-4 text-leetcode-green" />
            <h3 className="text-sm font-medium text-white">AI Interviewer Prompt</h3>
          </div>

          <textarea
            value={aiPrompt}
            onChange={(e) => setCustomPrompt(e.target.value)}
            placeholder="Customize how the AI interviewer behaves..."
            className="w-full h-32 px-3 py-2 bg-leetcode-light border border-leetcode-border rounded-md text-sm text-white placeholder-leetcode-text-secondary focus:outline-none focus:ring-2 focus:ring-leetcode-green resize-none"
          />

          <p className="text-xs text-leetcode-text-secondary mt-2">
            This prompt defines how the AI interviewer will interact with you during the session.
          </p>
        </div>

        {/* Problem Categories */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-white">Problem Categories</h3>
            <div className="flex space-x-2">
              <button
                onClick={handleSelectAll}
                className="text-xs text-leetcode-green hover:text-leetcode-green hover:opacity-80"
              >
                Select All
              </button>
              <button
                onClick={handleSelectNone}
                className="text-xs text-leetcode-text-secondary hover:text-white"
              >
                Clear All
              </button>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2 max-h-64 overflow-y-auto">
            {leetcodeCategories.map((category) => {
              const isSelected = selectedCategories.includes(category)
              return (
                <button
                  key={category}
                  onClick={() => handleCategoryToggle(category)}
                  className={`flex items-center space-x-2 p-2 rounded-md text-left text-sm transition-colors ${
                    isSelected
                      ? 'bg-leetcode-green bg-opacity-20 text-leetcode-green border border-leetcode-green'
                      : 'bg-leetcode-light text-leetcode-text-secondary border border-leetcode-border hover:bg-leetcode-lighter'
                  }`}
                >
                  {isSelected ? (
                    <CheckSquare className="w-4 h-4" />
                  ) : (
                    <Square className="w-4 h-4" />
                  )}
                  <span className="truncate">{category}</span>
                </button>
              )
            })}
          </div>

          <p className="text-xs text-leetcode-text-secondary mt-2">
            Selected: {selectedCategories.length} categories
          </p>
        </div>

        {/* Generate New Problem */}
        <div className="border-t border-leetcode-border pt-4">
          <button
            onClick={handleGenerateNewProblem}
            className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-leetcode-green text-white rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-leetcode-green transition-colors"
          >
            <Shuffle className="w-4 h-4" />
            <span>Generate New Problem</span>
          </button>

          <p className="text-xs text-leetcode-text-secondary mt-2 text-center">
            Get a random problem from your selected categories
          </p>
        </div>
      </div>
    </div>
  )
}

export default SettingsPanel
