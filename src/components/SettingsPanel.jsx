import { useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Sliders } from 'lucide-react'
import { leetcodeCategories } from '../data/problems'
import {
  DEFAULT_PROBLEM_PROMPT,
  DEFAULT_INTERVIEWER_PROMPT,
  AVAILABLE_MODELS,
  TEMPERATURE_PRESETS,
  TOKEN_PRESETS
} from '../config/prompts'

const SettingsPanel = ({
  onNewProblem,
  problemPrompt,
  setProblemPrompt,
  interviewerPrompt,
  setInterviewerPrompt,
  problemConfig,
  setProblemConfig,
  interviewerConfig,
  setInterviewerConfig,
  currentLanguage
}) => {
  const [selectedCategories, setSelectedCategories] = useState(['Array', 'String'])
  const [activePromptTab, setActivePromptTab] = useState('problem')

  // Use imported default prompts
  const defaultProblemPrompt = DEFAULT_PROBLEM_PROMPT
  const defaultInterviewerPrompt = DEFAULT_INTERVIEWER_PROMPT

  const handleCategoryToggle = (category) => {
    setSelectedCategories(prev => 
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
  }

  const handleSelectAll = () => {
    setSelectedCategories(leetcodeCategories)
  }

  const handleSelectNone = () => {
    setSelectedCategories([])
  }

  const handleGenerateNewProblem = () => {
    console.log('🎯 SettingsPanel: Generate New Problem clicked!')
    console.log('🎯 Selected categories:', selectedCategories)
    console.log('🎯 Current language:', currentLanguage)
    console.log('🎯 onNewProblem function:', typeof onNewProblem)

    const categoriesToUse = selectedCategories.length > 0 ? selectedCategories : ['Array']
    console.log('🎯 Categories to use:', categoriesToUse)

    if (onNewProblem) {
      onNewProblem(categoriesToUse, currentLanguage)
    } else {
      console.error('❌ onNewProblem function not provided!')
    }
  }

  return (
    <div className="h-full overflow-y-auto bg-leetcode-dark">
      {/* Header */}
      <div className="bg-leetcode-darker border-b border-leetcode-border p-4">
        <div className="flex items-center space-x-2">
          <Settings className="w-5 h-5 text-leetcode-green" />
          <h2 className="text-lg font-semibold text-white">Settings</h2>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* AI Prompts Configuration */}
        <div>
          <div className="flex items-center space-x-2 mb-3">
            <Brain className="w-4 h-4 text-leetcode-green" />
            <h3 className="text-sm font-medium text-white">AI Configuration</h3>
          </div>

          {/* Prompt Tabs */}
          <div className="flex space-x-1 mb-4 bg-leetcode-light rounded-md p-1">
            <button
              onClick={() => setActivePromptTab('problem')}
              className={`flex-1 flex items-center justify-center space-x-2 px-3 py-2 rounded text-sm font-medium transition-colors ${
                activePromptTab === 'problem'
                  ? 'bg-leetcode-green text-white'
                  : 'text-leetcode-text-secondary hover:text-white'
              }`}
            >
              <Code className="w-4 h-4" />
              <span>Problem Prompt</span>
            </button>
            <button
              onClick={() => setActivePromptTab('interviewer')}
              className={`flex-1 flex items-center justify-center space-x-2 px-3 py-2 rounded text-sm font-medium transition-colors ${
                activePromptTab === 'interviewer'
                  ? 'bg-leetcode-green text-white'
                  : 'text-leetcode-text-secondary hover:text-white'
              }`}
            >
              <Bot className="w-4 h-4" />
              <span>Interviewer Prompt</span>
            </button>
          </div>

          {/* Problem Prompt Tab */}
          {activePromptTab === 'problem' && (
            <div className="space-y-4">
              <div>
                <textarea
                  value={problemPrompt || defaultProblemPrompt}
                  onChange={(e) => setProblemPrompt(e.target.value)}
                  placeholder="Customize how problems are generated..."
                  className="w-full h-32 px-3 py-2 bg-leetcode-light border border-leetcode-border rounded-md text-sm text-white placeholder-leetcode-text-secondary focus:outline-none focus:ring-2 focus:ring-leetcode-green resize-none"
                />
                <p className="text-xs text-leetcode-text-secondary mt-2">
                  This prompt is used to generate new problems. Use {'{CATEGORIES}'} to insert selected categories.
                </p>
              </div>

              {/* Problem Generation Config */}
              <div className="grid grid-cols-3 gap-3">
                <div>
                  <label className="block text-xs text-leetcode-text-secondary mb-1">Model</label>
                  <select
                    value={problemConfig?.model || 'gpt-4'}
                    onChange={(e) => setProblemConfig(prev => ({ ...prev, model: e.target.value }))}
                    className="w-full px-2 py-1 bg-leetcode-light border border-leetcode-border rounded text-xs text-white focus:outline-none focus:ring-1 focus:ring-leetcode-green"
                  >
                    {AVAILABLE_MODELS.map(model => (
                      <option key={model.value} value={model.value}>{model.label}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-xs text-leetcode-text-secondary mb-1">Temperature</label>
                  <input
                    type="range"
                    min="0"
                    max="2"
                    step="0.1"
                    value={problemConfig?.temperature || 0.7}
                    onChange={(e) => setProblemConfig(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
                    className="w-full"
                  />
                  <div className="text-xs text-leetcode-text-secondary text-center">{problemConfig?.temperature || 0.7}</div>
                </div>
                <div>
                  <label className="block text-xs text-leetcode-text-secondary mb-1">Max Tokens</label>
                  <input
                    type="number"
                    min="100"
                    max="4000"
                    step="100"
                    value={problemConfig?.maxTokens || 1000}
                    onChange={(e) => setProblemConfig(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
                    className="w-full px-2 py-1 bg-leetcode-light border border-leetcode-border rounded text-xs text-white focus:outline-none focus:ring-1 focus:ring-leetcode-green"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Interviewer Prompt Tab */}
          {activePromptTab === 'interviewer' && (
            <div className="space-y-4">
              <div>
                <textarea
                  value={interviewerPrompt || defaultInterviewerPrompt}
                  onChange={(e) => setInterviewerPrompt(e.target.value)}
                  placeholder="Customize how the AI interviewer behaves..."
                  className="w-full h-32 px-3 py-2 bg-leetcode-light border border-leetcode-border rounded-md text-sm text-white placeholder-leetcode-text-secondary focus:outline-none focus:ring-2 focus:ring-leetcode-green resize-none"
                />
                <p className="text-xs text-leetcode-text-secondary mt-2">
                  This prompt defines how the AI interviewer will interact with you during the session.
                </p>
              </div>

              {/* Interviewer Config */}
              <div className="grid grid-cols-3 gap-3">
                <div>
                  <label className="block text-xs text-leetcode-text-secondary mb-1">Model</label>
                  <select
                    value={interviewerConfig?.model || 'gpt-4'}
                    onChange={(e) => setInterviewerConfig(prev => ({ ...prev, model: e.target.value }))}
                    className="w-full px-2 py-1 bg-leetcode-light border border-leetcode-border rounded text-xs text-white focus:outline-none focus:ring-1 focus:ring-leetcode-green"
                  >
                    {AVAILABLE_MODELS.map(model => (
                      <option key={model.value} value={model.value}>{model.label}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-xs text-leetcode-text-secondary mb-1">Temperature</label>
                  <input
                    type="range"
                    min="0"
                    max="2"
                    step="0.1"
                    value={interviewerConfig?.temperature || 0.7}
                    onChange={(e) => setInterviewerConfig(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
                    className="w-full"
                  />
                  <div className="text-xs text-leetcode-text-secondary text-center">{interviewerConfig?.temperature || 0.7}</div>
                </div>
                <div>
                  <label className="block text-xs text-leetcode-text-secondary mb-1">Max Tokens</label>
                  <input
                    type="number"
                    min="100"
                    max="4000"
                    step="100"
                    value={interviewerConfig?.maxTokens || 1000}
                    onChange={(e) => setInterviewerConfig(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
                    className="w-full px-2 py-1 bg-leetcode-light border border-leetcode-border rounded text-xs text-white focus:outline-none focus:ring-1 focus:ring-leetcode-green"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Problem Categories */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-white">Problem Categories</h3>
            <div className="flex space-x-2">
              <button
                onClick={handleSelectAll}
                className="text-xs text-leetcode-green hover:text-leetcode-green hover:opacity-80"
              >
                Select All
              </button>
              <button
                onClick={handleSelectNone}
                className="text-xs text-leetcode-text-secondary hover:text-white"
              >
                Clear All
              </button>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2 max-h-64 overflow-y-auto">
            {leetcodeCategories.map((category) => {
              const isSelected = selectedCategories.includes(category)
              return (
                <button
                  key={category}
                  onClick={() => handleCategoryToggle(category)}
                  className={`flex items-center space-x-2 p-2 rounded-md text-left text-sm transition-colors ${
                    isSelected
                      ? 'bg-leetcode-green bg-opacity-20 text-leetcode-green border border-leetcode-green'
                      : 'bg-leetcode-light text-leetcode-text-secondary border border-leetcode-border hover:bg-leetcode-lighter'
                  }`}
                >
                  {isSelected ? (
                    <CheckSquare className="w-4 h-4" />
                  ) : (
                    <Square className="w-4 h-4" />
                  )}
                  <span className="truncate">{category}</span>
                </button>
              )
            })}
          </div>

          <p className="text-xs text-leetcode-text-secondary mt-2">
            Selected: {selectedCategories.length} categories
          </p>
        </div>

        {/* Generate New Problem */}
        <div className="border-t border-leetcode-border pt-4">
          <div className="space-y-2">
            <button
              onClick={handleGenerateNewProblem}
              className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-leetcode-green text-white rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-leetcode-green transition-colors"
            >
              <Shuffle className="w-4 h-4" />
              <span>Generate New Problem</span>
            </button>

            <button
              onClick={() => {
                console.log('🔍 API Configuration Test:')
                console.log('VITE_OPENAI_API_KEY present:', !!import.meta.env.VITE_OPENAI_API_KEY)
                console.log('VITE_OPENAI_API_KEY starts with sk-:', import.meta.env.VITE_OPENAI_API_KEY?.startsWith('sk-'))
                console.log('VITE_OPENAI_API_KEY length:', import.meta.env.VITE_OPENAI_API_KEY?.length)
                console.log('Problem prompt length:', problemPrompt?.length || 0)
                console.log('Selected categories:', selectedCategories)
                console.log('Current language:', currentLanguage)
              }}
              className="w-full px-2 py-1 bg-leetcode-light text-leetcode-text-secondary rounded text-xs hover:bg-opacity-80 focus:outline-none"
            >
              🔍 Debug API Config
            </button>
          </div>

          <p className="text-xs text-leetcode-text-secondary mt-2 text-center">
            Get a random problem from your selected categories
          </p>
        </div>
      </div>
    </div>
  )
}

export default SettingsPanel
