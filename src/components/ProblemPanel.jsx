import { FileText, Clock, BarChart3 } from 'lucide-react'

const ProblemPanel = ({ problem }) => {
  const getDifficultyColor = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return 'text-green-600 bg-green-100'
      case 'medium':
        return 'text-yellow-600 bg-yellow-100'
      case 'hard':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="h-full overflow-y-auto">
      {/* Header */}
      <div className="bg-gray-50 border-b border-gray-200 p-4">
        <div className="flex items-center space-x-2 mb-2">
          <FileText className="w-5 h-5 text-gray-600" />
          <h2 className="text-lg font-semibold text-gray-800">Current Problem</h2>
        </div>
        
        <div className="flex items-center space-x-3">
          <h3 className="text-xl font-bold text-gray-900">
            {problem.id}. {problem.title}
          </h3>
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(
              problem.difficulty
            )}`}
          >
            {problem.difficulty}
          </span>
        </div>
      </div>

      {/* Problem Description */}
      <div className="p-4">
        <div className="prose prose-sm max-w-none">
          <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
            {problem.description}
          </div>
        </div>
      </div>

      {/* Problem Stats (placeholder) */}
      <div className="border-t border-gray-200 p-4">
        <h4 className="text-sm font-medium text-gray-800 mb-3 flex items-center">
          <BarChart3 className="w-4 h-4 mr-2" />
          Problem Stats
        </h4>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-gray-600">Acceptance Rate</div>
            <div className="font-semibold text-gray-900">
              {problem.difficulty === 'Easy' ? '65.2%' : 
               problem.difficulty === 'Medium' ? '42.8%' : '28.1%'}
            </div>
          </div>
          
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-gray-600">Submissions</div>
            <div className="font-semibold text-gray-900">
              {problem.difficulty === 'Easy' ? '2.1M' : 
               problem.difficulty === 'Medium' ? '1.3M' : '456K'}
            </div>
          </div>
        </div>
      </div>

      {/* Tips Section */}
      <div className="border-t border-gray-200 p-4">
        <h4 className="text-sm font-medium text-gray-800 mb-3 flex items-center">
          <Clock className="w-4 h-4 mr-2" />
          Interview Tips
        </h4>
        
        <div className="space-y-2 text-sm text-gray-600">
          <div className="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-400">
            <p className="font-medium text-blue-800 mb-1">Think Out Loud</p>
            <p className="text-blue-700">
              Explain your thought process as you work through the problem.
            </p>
          </div>
          
          <div className="bg-green-50 p-3 rounded-lg border-l-4 border-green-400">
            <p className="font-medium text-green-800 mb-1">Ask Questions</p>
            <p className="text-green-700">
              Clarify requirements and edge cases before coding.
            </p>
          </div>
          
          <div className="bg-purple-50 p-3 rounded-lg border-l-4 border-purple-400">
            <p className="font-medium text-purple-800 mb-1">Optimize</p>
            <p className="text-purple-700">
              Discuss time and space complexity improvements.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProblemPanel
