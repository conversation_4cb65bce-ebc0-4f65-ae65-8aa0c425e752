import { FileText, Clock, BarChart3 } from 'lucide-react'

const ProblemPanel = ({ problem }) => {
  const getDifficultyColor = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return 'text-leetcode-green bg-leetcode-green bg-opacity-20'
      case 'medium':
        return 'text-leetcode-orange bg-leetcode-orange bg-opacity-20'
      case 'hard':
        return 'text-leetcode-red bg-leetcode-red bg-opacity-20'
      default:
        return 'text-leetcode-text-secondary bg-leetcode-light'
    }
  }

  return (
    <div className="h-full overflow-y-auto bg-leetcode-dark">
      {/* Header */}
      <div className="bg-leetcode-darker border-b border-leetcode-border p-4">
        <div className="flex items-center space-x-2 mb-2">
          <FileText className="w-5 h-5 text-leetcode-green" />
          <h2 className="text-lg font-semibold text-white">Description</h2>
        </div>

        <div className="flex items-center space-x-3">
          <h3 className="text-xl font-bold text-white">
            {problem.id}. {problem.title}
          </h3>
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(
              problem.difficulty
            )}`}
          >
            {problem.difficulty}
          </span>
        </div>
      </div>

      {/* Problem Description */}
      <div className="p-4">
        <div className="prose prose-sm max-w-none">
          <div className="whitespace-pre-wrap text-leetcode-text leading-relaxed">
            {problem.description}
          </div>
        </div>
      </div>

      {/* Problem Stats (placeholder) */}
      <div className="border-t border-leetcode-border p-4">
        <h4 className="text-sm font-medium text-white mb-3 flex items-center">
          <BarChart3 className="w-4 h-4 mr-2 text-leetcode-green" />
          Problem Stats
        </h4>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="bg-leetcode-light p-3 rounded-lg border border-leetcode-border">
            <div className="text-leetcode-text-secondary">Acceptance Rate</div>
            <div className="font-semibold text-white">
              {problem.difficulty === 'Easy' ? '65.2%' :
               problem.difficulty === 'Medium' ? '42.8%' : '28.1%'}
            </div>
          </div>

          <div className="bg-leetcode-light p-3 rounded-lg border border-leetcode-border">
            <div className="text-leetcode-text-secondary">Submissions</div>
            <div className="font-semibold text-white">
              {problem.difficulty === 'Easy' ? '2.1M' :
               problem.difficulty === 'Medium' ? '1.3M' : '456K'}
            </div>
          </div>
        </div>
      </div>

      {/* Tips Section */}
      <div className="border-t border-leetcode-border p-4">
        <h4 className="text-sm font-medium text-white mb-3 flex items-center">
          <Clock className="w-4 h-4 mr-2 text-leetcode-green" />
          Interview Tips
        </h4>

        <div className="space-y-2 text-sm">
          <div className="bg-leetcode-light p-3 rounded-lg border-l-4 border-leetcode-blue">
            <p className="font-medium text-leetcode-blue mb-1">Think Out Loud</p>
            <p className="text-leetcode-text-secondary">
              Explain your thought process as you work through the problem.
            </p>
          </div>

          <div className="bg-leetcode-light p-3 rounded-lg border-l-4 border-leetcode-green">
            <p className="font-medium text-leetcode-green mb-1">Ask Questions</p>
            <p className="text-leetcode-text-secondary">
              Clarify requirements and edge cases before coding.
            </p>
          </div>

          <div className="bg-leetcode-light p-3 rounded-lg border-l-4 border-leetcode-purple">
            <p className="font-medium text-leetcode-purple mb-1">Optimize</p>
            <p className="text-leetcode-text-secondary">
              Discuss time and space complexity improvements.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProblemPanel
