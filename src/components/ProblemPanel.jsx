import { useState } from 'react'
import { FileText, Clock, <PERSON>Chart3, ChevronDown, ChevronRight } from 'lucide-react'

const ProblemPanel = ({ problem }) => {
  const [expandedSections, setExpandedSections] = useState({ part1: true })

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const getDifficultyColor = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return 'text-leetcode-green bg-leetcode-green bg-opacity-20'
      case 'medium':
        return 'text-leetcode-orange bg-leetcode-orange bg-opacity-20'
      case 'hard':
        return 'text-leetcode-red bg-leetcode-red bg-opacity-20'
      default:
        return 'text-leetcode-text-secondary bg-leetcode-light'
    }
  }

  // Show empty state if no problem
  if (!problem) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-leetcode-dark text-center p-8">
        <FileText className="w-16 h-16 text-leetcode-text-secondary mb-4" />
        <h3 className="text-xl font-medium text-white mb-2">No Problem Selected</h3>
        <p className="text-leetcode-text-secondary text-sm max-w-md mb-6">
          Generate a new problem from the Settings tab to start practicing. Choose your preferred categories and let the AI create a custom problem for you!
        </p>
        <div className="bg-leetcode-light border border-leetcode-border rounded-lg p-4 max-w-sm">
          <p className="text-xs text-leetcode-text-secondary">
            💡 <strong>Tip:</strong> Go to Settings → Select categories → Click "Generate New Problem"
          </p>
        </div>
      </div>
    )
  }

  // Check if description is the new JSON format with parts
  const isStructuredDescription = typeof problem.description === 'object' && problem.description.part1

  return (
    <div className="h-full overflow-y-auto bg-leetcode-dark">
      {/* Navigation Tabs (like LeetCode) */}
      <div className="bg-leetcode-darker border-b border-leetcode-border">
        <div className="flex">
          <div className="px-4 py-2 bg-leetcode-tab-active border-b-2 border-leetcode-green text-sm font-medium text-white">
            Description
          </div>
          <div className="px-4 py-2 text-sm font-medium text-leetcode-text-secondary hover:text-white cursor-pointer">
            Solutions
          </div>
          <div className="px-4 py-2 text-sm font-medium text-leetcode-text-secondary hover:text-white cursor-pointer">
            Testcase
          </div>
          <div className="px-4 py-2 text-sm font-medium text-leetcode-text-secondary hover:text-white cursor-pointer">
            Test Result
          </div>
        </div>
      </div>

      {/* Problem Header */}
      <div className="px-4 py-3 border-b border-leetcode-border">
        <div className="flex items-center justify-between mb-2">
          <h1 className="text-lg font-semibold text-white">
            {problem.id}. {problem.title}
          </h1>
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded text-xs font-medium ${getDifficultyColor(problem.difficulty)}`}>
              {problem.difficulty}
            </span>
            <div className="flex items-center text-leetcode-green text-sm">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-xs">Solved</span>
            </div>
          </div>
        </div>

        {/* Tags */}
        <div className="flex items-center space-x-2">
          <span className="text-xs text-leetcode-text-secondary">Topics:</span>
          <span className="px-2 py-1 bg-leetcode-light text-xs text-leetcode-text-secondary rounded">
            Binary Search Tree
          </span>
          <span className="px-2 py-1 bg-leetcode-light text-xs text-leetcode-text-secondary rounded">
            Tree
          </span>
        </div>
      </div>

      {/* Problem Description */}
      <div className="p-4">
        {isStructuredDescription ? (
          <div className="space-y-4">
            {Object.entries(problem.description).map(([partKey, partContent]) => (
              <div key={partKey} className="border border-leetcode-border rounded-lg">
                <button
                  onClick={() => toggleSection(partKey)}
                  className="w-full flex items-center justify-between p-3 text-left hover:bg-leetcode-light transition-colors"
                >
                  <span className="font-medium text-white capitalize">
                    {partKey.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </span>
                  {expandedSections[partKey] ? (
                    <ChevronDown className="w-4 h-4 text-leetcode-text-secondary" />
                  ) : (
                    <ChevronRight className="w-4 h-4 text-leetcode-text-secondary" />
                  )}
                </button>
                {expandedSections[partKey] && (
                  <div className="px-3 pb-3 border-t border-leetcode-border">
                    <div className="whitespace-pre-wrap text-leetcode-text leading-relaxed pt-3">
                      {partContent}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="prose prose-sm max-w-none">
            <div className="whitespace-pre-wrap text-leetcode-text leading-relaxed">
              {problem.description}
            </div>
          </div>
        )}
      </div>

      {/* Problem Stats (placeholder) */}
      <div className="border-t border-leetcode-border p-4">
        <h4 className="text-sm font-medium text-white mb-3 flex items-center">
          <BarChart3 className="w-4 h-4 mr-2 text-leetcode-green" />
          Problem Stats
        </h4>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="bg-leetcode-light p-3 rounded-lg border border-leetcode-border">
            <div className="text-leetcode-text-secondary">Acceptance Rate</div>
            <div className="font-semibold text-white">
              {problem.difficulty === 'Easy' ? '65.2%' :
               problem.difficulty === 'Medium' ? '42.8%' : '28.1%'}
            </div>
          </div>

          <div className="bg-leetcode-light p-3 rounded-lg border border-leetcode-border">
            <div className="text-leetcode-text-secondary">Submissions</div>
            <div className="font-semibold text-white">
              {problem.difficulty === 'Easy' ? '2.1M' :
               problem.difficulty === 'Medium' ? '1.3M' : '456K'}
            </div>
          </div>
        </div>
      </div>

      {/* Tips Section */}
      <div className="border-t border-leetcode-border p-4">
        <h4 className="text-sm font-medium text-white mb-3 flex items-center">
          <Clock className="w-4 h-4 mr-2 text-leetcode-green" />
          Interview Tips
        </h4>

        <div className="space-y-2 text-sm">
          <div className="bg-leetcode-light p-3 rounded-lg border-l-4 border-leetcode-blue">
            <p className="font-medium text-leetcode-blue mb-1">Think Out Loud</p>
            <p className="text-leetcode-text-secondary">
              Explain your thought process as you work through the problem.
            </p>
          </div>

          <div className="bg-leetcode-light p-3 rounded-lg border-l-4 border-leetcode-green">
            <p className="font-medium text-leetcode-green mb-1">Ask Questions</p>
            <p className="text-leetcode-text-secondary">
              Clarify requirements and edge cases before coding.
            </p>
          </div>

          <div className="bg-leetcode-light p-3 rounded-lg border-l-4 border-leetcode-purple">
            <p className="font-medium text-leetcode-purple mb-1">Optimize</p>
            <p className="text-leetcode-text-secondary">
              Discuss time and space complexity improvements.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProblemPanel
