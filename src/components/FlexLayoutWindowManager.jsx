import { useState, useCallback, useRef } from 'react'
import { Layout, Model, TabNode, Actions } from 'flexlayout-react'
import { MessageCircle, FileText, Settings, TestTube, BarChart3, Code2, Plus } from 'lucide-react'

// Import all our existing components
import InterviewerChat from './InterviewerChat'
import ProblemPanel from './ProblemPanel'
import SettingsPanel from './SettingsPanel'
import TestCasePanel from './TestCasePanel'
import TestResultPanel from './TestResultPanel'
import CodeEditor from './CodeEditor'

const FlexLayoutWindowManager = ({ 
  // All the props we need to pass to components
  currentProblem,
  chatMessages,
  setChatMessages,
  onNewProblem,
  problemPrompt,
  setProblemPrompt,
  interviewerPrompt,
  setInterviewerPrompt,
  problemConfig,
  setProblemConfig,
  interviewerConfig,
  setInterviewerConfig,
  testResults,
  isRunningTests,
  onRunTests,
  currentLanguage,
  onSubmitCode,
  isGeneratingProblem,
  code,
  setCode,
  selectedLanguage,
  onLanguageChange,
  showLeftPanel,
  showChatPanel,
  onToggleLeftPanel,
  onToggleChatPanel
}) => {
  // Initial layout configuration for FlexLayout
  const json = {
    global: {
      tabEnableClose: true,
      tabEnableFloat: true,
      tabEnableDrag: true,
      tabEnableRename: false,
      tabSetEnableClose: false,
      tabSetEnableDrop: true,
      tabSetEnableDrag: true,
      tabSetEnableTabStrip: true,
      tabSetClassNameTabStrip: "flexlayout__tabset_tabstrip_leetcode",
      tabSetClassNameHeader: "flexlayout__tabset_header_leetcode",
      tabClassName: "flexlayout__tab_leetcode",
      tabClassNameSelected: "flexlayout__tab_selected_leetcode"
    },
    borders: [],
    layout: {
      type: "row",
      weight: 100,
      children: [
        {
          type: "tabset",
          weight: 25,
          children: [
            {
              type: "tab",
              name: "Problem",
              component: "problem",
              id: "problem-1"
            }
          ]
        },
        {
          type: "tabset",
          weight: 25,
          children: [
            {
              type: "tab",
              name: "AI Chat",
              component: "chat",
              id: "chat-1"
            },
            {
              type: "tab",
              name: "Settings",
              component: "settings",
              id: "settings-1"
            }
          ]
        },
        {
          type: "tabset",
          weight: 50,
          children: [
            {
              type: "tab",
              name: "Code Editor",
              component: "code-editor",
              id: "code-editor-1"
            }
          ]
        }
      ]
    }
  }

  const [model] = useState(Model.fromJson(json))
  const layoutRef = useRef(null)

  // Factory function to render components
  const factory = useCallback((node) => {
    const component = node.getComponent()
    
    switch (component) {
      case 'problem':
        return <ProblemPanel problem={currentProblem} />
      
      case 'chat':
        return (
          <InterviewerChat
            messages={chatMessages}
            setMessages={setChatMessages}
            customPrompt={interviewerPrompt}
            interviewerConfig={interviewerConfig}
            onSubmitCode={onSubmitCode}
          />
        )
      
      case 'settings':
        return (
          <SettingsPanel
            onNewProblem={onNewProblem}
            problemPrompt={problemPrompt}
            setProblemPrompt={setProblemPrompt}
            interviewerPrompt={interviewerPrompt}
            setInterviewerPrompt={setInterviewerPrompt}
            problemConfig={problemConfig}
            setProblemConfig={setProblemConfig}
            interviewerConfig={interviewerConfig}
            setInterviewerConfig={setInterviewerConfig}
            currentLanguage={currentLanguage}
            isGeneratingProblem={isGeneratingProblem}
          />
        )
      
      case 'testcase':
        return (
          <TestCasePanel
            problem={currentProblem}
            onRunTests={onRunTests}
          />
        )
      
      case 'testresult':
        return (
          <TestResultPanel
            testResults={testResults}
            isRunning={isRunningTests}
            onRunTests={onRunTests}
            problem={currentProblem}
          />
        )
      
      case 'code-editor':
        return (
          <CodeEditor
            code={code}
            setCode={setCode}
            language={selectedLanguage}
            onLanguageChange={onLanguageChange}
            showLeftPanel={showLeftPanel}
            showChatPanel={showChatPanel}
            onToggleLeftPanel={onToggleLeftPanel}
            onToggleChatPanel={onToggleChatPanel}
            onRunTests={() => onRunTests(null)}
            isRunningTests={isRunningTests}
          />
        )
      
      default:
        return <div>Unknown component: {component}</div>
    }
  }, [
    currentProblem, chatMessages, setChatMessages, interviewerPrompt, interviewerConfig, onSubmitCode,
    onNewProblem, problemPrompt, setProblemPrompt, setInterviewerPrompt, problemConfig, setProblemConfig,
    setInterviewerConfig, currentLanguage, isGeneratingProblem, testResults, isRunningTests, onRunTests,
    code, setCode, selectedLanguage, onLanguageChange, showLeftPanel, showChatPanel, onToggleLeftPanel,
    onToggleChatPanel
  ])

  // Add new tab function
  const addNewTab = useCallback((componentType, name) => {
    const newTabId = `${componentType}-${Date.now()}`

    // Find the first tabset to add the new tab to
    const rootNode = model.getRoot()
    const firstTabset = rootNode.getChildren()[0]

    if (firstTabset && firstTabset.getType() === "tabset") {
      model.doAction(Actions.addNode(
        {
          type: "tab",
          name: name,
          component: componentType,
          id: newTabId
        },
        firstTabset.getId(),
        0,
        -1
      ))
    }
  }, [model])

  // Toolbar component
  const Toolbar = () => (
    <div className="bg-leetcode-darker border-b border-leetcode-border p-3 flex items-center space-x-3 z-50">
      <div className="flex items-center space-x-2">
        <Code2 className="w-5 h-5 text-leetcode-green" />
        <span className="text-white text-sm font-semibold">AI Interviewer</span>
      </div>
      <div className="w-px h-6 bg-leetcode-border"></div>
      <span className="text-leetcode-text-secondary text-sm font-medium">Add Window:</span>
      
      <button
        onClick={() => addNewTab('problem', 'Problem')}
        className="flex items-center space-x-1 px-3 py-1.5 bg-leetcode-light text-white rounded-md hover:bg-leetcode-green transition-all duration-200 text-xs font-medium hover:scale-105"
        title="Add Problem Window"
      >
        <FileText className="w-3.5 h-3.5" />
        <span>Problem</span>
      </button>

      <button
        onClick={() => addNewTab('chat', 'AI Chat')}
        className="flex items-center space-x-1 px-3 py-1.5 bg-leetcode-light text-white rounded-md hover:bg-leetcode-green transition-all duration-200 text-xs font-medium hover:scale-105"
        title="Add Chat Window"
      >
        <MessageCircle className="w-3.5 h-3.5" />
        <span>Chat</span>
      </button>

      <button
        onClick={() => addNewTab('settings', 'Settings')}
        className="flex items-center space-x-1 px-3 py-1.5 bg-leetcode-light text-white rounded-md hover:bg-leetcode-green transition-all duration-200 text-xs font-medium hover:scale-105"
        title="Add Settings Window"
      >
        <Settings className="w-3.5 h-3.5" />
        <span>Settings</span>
      </button>

      <button
        onClick={() => addNewTab('testcase', 'Test Cases')}
        className="flex items-center space-x-1 px-3 py-1.5 bg-leetcode-light text-white rounded-md hover:bg-leetcode-green transition-all duration-200 text-xs font-medium hover:scale-105"
        title="Add Test Cases Window"
      >
        <TestTube className="w-3.5 h-3.5" />
        <span>Test Cases</span>
      </button>

      <button
        onClick={() => addNewTab('testresult', 'Test Results')}
        className="flex items-center space-x-1 px-3 py-1.5 bg-leetcode-light text-white rounded-md hover:bg-leetcode-green transition-all duration-200 text-xs font-medium hover:scale-105"
        title="Add Test Results Window"
      >
        <BarChart3 className="w-3.5 h-3.5" />
        <span>Test Results</span>
      </button>

      <button
        onClick={() => addNewTab('code-editor', 'Code Editor')}
        className="flex items-center space-x-1 px-3 py-1.5 bg-leetcode-light text-white rounded-md hover:bg-leetcode-green transition-all duration-200 text-xs font-medium hover:scale-105"
        title="Add Code Editor Window"
      >
        <Code2 className="w-3.5 h-3.5" />
        <span>Code Editor</span>
      </button>
    </div>
  )

  return (
    <div className="h-screen bg-leetcode-dark flex flex-col">
      <Toolbar />
      <div className="flex-1 flexlayout-container">
        <Layout
          ref={layoutRef}
          model={model}
          factory={factory}
          onModelChange={() => {}}
        />
      </div>
    </div>
  )
}

export default FlexLayoutWindowManager
