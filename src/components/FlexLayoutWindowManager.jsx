import { useState, useCallback, useRef } from 'react'
import { Layout, Model, Actions } from 'flexlayout-react'
import { MessageCircle, FileText, Settings, TestTube, BarChart3, Code2 } from 'lucide-react'

// Import all our existing components
import InterviewerChat from './InterviewerChat'
import ProblemPanel from './ProblemPanel'
import SettingsPanel from './SettingsPanel'
import TestCasePanel from './TestCasePanel'
import TestResultPanel from './TestResultPanel'
import CodeEditor from './CodeEditor'

const FlexLayoutWindowManager = ({ 
  // All the props we need to pass to components
  currentProblem,
  chatMessages,
  setChatMessages,
  onNewProblem,
  problemPrompt,
  setProblemPrompt,
  interviewerPrompt,
  setInterviewerPrompt,
  problemConfig,
  setProblemConfig,
  interviewerConfig,
  setInterviewerConfig,
  testResults,
  isRunningTests,
  onRunTests,
  currentLanguage,
  onSubmitCode,
  isGeneratingProblem,
  code,
  setCode,
  selectedLanguage,
  onLanguageChange,
  showLeftPanel,
  showChatPanel,
  onToggleLeftPanel,
  onToggleChatPanel
}) => {
  // Initial layout configuration for FlexLayout
  const json = {
    global: {
      tabEnableClose: false,
      tabEnableFloat: false,
      tabEnableDrag: true,
      tabEnableRename: false,
      tabSetEnableClose: false,
      tabSetEnableDrop: true,
      tabSetEnableDrag: true,
      tabSetEnableTabStrip: true,
      tabSetClassNameTabStrip: "flexlayout__tabset_tabstrip_leetcode",
      tabSetClassNameHeader: "flexlayout__tabset_header_leetcode",
      tabClassName: "flexlayout__tab_leetcode",
      tabClassNameSelected: "flexlayout__tab_selected_leetcode"
    },
    borders: [],
    layout: {
      type: "row",
      weight: 100,
      children: [
        {
          type: "tabset",
          weight: 35,
          selected: 0,
          children: [
            {
              type: "tab",
              name: "Problem",
              component: "problem",
              id: "problem-1",
              enableClose: false
            },
            {
              type: "tab",
              name: "AI Chat",
              component: "chat",
              id: "chat-1"
            },
            {
              type: "tab",
              name: "Settings",
              component: "settings",
              id: "settings-1"
            },
            {
              type: "tab",
              name: "Test Cases",
              component: "testcase",
              id: "testcase-1"
            },
            {
              type: "tab",
              name: "Test Results",
              component: "testresult",
              id: "testresult-1"
            }
          ]
        },
        {
          type: "tabset",
          weight: 65,
          children: [
            {
              type: "tab",
              name: "Code Editor",
              component: "code-editor",
              id: "code-editor-1",
              enableClose: false
            }
          ]
        }
      ]
    }
  }

  const [model] = useState(Model.fromJson(json))
  const layoutRef = useRef(null)

  // Handle model changes and actions
  const onModelChange = useCallback((newModel) => {
    // Handle any model changes if needed
    console.log('Model changed:', newModel)
  }, [])

  // Handle actions like tab closing
  const onAction = useCallback((action) => {
    console.log('Action:', action)
    return action
  }, [])

  // Factory function to render components
  const factory = useCallback((node) => {
    const component = node.getComponent()
    
    switch (component) {
      case 'problem':
        return <ProblemPanel problem={currentProblem} />
      
      case 'chat':
        return (
          <InterviewerChat
            messages={chatMessages}
            setMessages={setChatMessages}
            customPrompt={interviewerPrompt}
            interviewerConfig={interviewerConfig}
            onSubmitCode={onSubmitCode}
          />
        )
      
      case 'settings':
        return (
          <SettingsPanel
            onNewProblem={onNewProblem}
            problemPrompt={problemPrompt}
            setProblemPrompt={setProblemPrompt}
            interviewerPrompt={interviewerPrompt}
            setInterviewerPrompt={setInterviewerPrompt}
            problemConfig={problemConfig}
            setProblemConfig={setProblemConfig}
            interviewerConfig={interviewerConfig}
            setInterviewerConfig={setInterviewerConfig}
            currentLanguage={currentLanguage}
            isGeneratingProblem={isGeneratingProblem}
          />
        )
      
      case 'testcase':
        return (
          <TestCasePanel
            problem={currentProblem}
            onRunTests={onRunTests}
          />
        )
      
      case 'testresult':
        return (
          <TestResultPanel
            testResults={testResults}
            isRunning={isRunningTests}
            onRunTests={onRunTests}
            problem={currentProblem}
          />
        )
      
      case 'code-editor':
        return (
          <CodeEditor
            code={code}
            setCode={setCode}
            language={selectedLanguage}
            onLanguageChange={onLanguageChange}
            showLeftPanel={showLeftPanel}
            showChatPanel={showChatPanel}
            onToggleLeftPanel={onToggleLeftPanel}
            onToggleChatPanel={onToggleChatPanel}
            onRunTests={() => onRunTests(null)}
            isRunningTests={isRunningTests}
          />
        )
      
      default:
        return <div>Unknown component: {component}</div>
    }
  }, [
    currentProblem, chatMessages, setChatMessages, interviewerPrompt, interviewerConfig, onSubmitCode,
    onNewProblem, problemPrompt, setProblemPrompt, setInterviewerPrompt, problemConfig, setProblemConfig,
    setInterviewerConfig, currentLanguage, isGeneratingProblem, testResults, isRunningTests, onRunTests,
    code, setCode, selectedLanguage, onLanguageChange, showLeftPanel, showChatPanel, onToggleLeftPanel,
    onToggleChatPanel
  ])

  return (
    <div className="h-screen bg-leetcode-dark flex flex-col">
      <div className="flex-1 flexlayout-container">
        <Layout
          ref={layoutRef}
          model={model}
          factory={factory}
          onModelChange={onModelChange}
          onAction={onAction}
        />
      </div>
    </div>
  )
}

export default FlexLayoutWindowManager
