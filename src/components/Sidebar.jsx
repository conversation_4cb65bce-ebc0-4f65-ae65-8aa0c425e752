import { useState } from 'react'
import { MessageCircle, FileText, Settings, Bot } from 'lucide-react'
import Interviewer<PERSON><PERSON> from './InterviewerChat'
import ProblemPanel from './ProblemPanel'
import SettingsPanel from './SettingsPanel'

const Sidebar = ({ currentProblem, chatMessages, setChatMessages, onNewProblem }) => {
  const [activeTab, setActiveTab] = useState('chat')

  const tabs = [
    { id: 'chat', label: 'Chat', icon: MessageCircle },
    { id: 'problem', label: 'Problem', icon: FileText },
    { id: 'settings', label: 'Settings', icon: Settings },
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case 'chat':
        return (
          <InterviewerChat
            messages={chatMessages}
            setMessages={setChatMessages}
          />
        )
      case 'problem':
        return <ProblemPanel problem={currentProblem} />
      case 'settings':
        return <SettingsPanel onNewProblem={onNewProblem} />
      default:
        return null
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="flex items-center space-x-2">
          <Bot className="w-6 h-6" />
          <h1 className="text-lg font-semibold">AI Interviewer</h1>
        </div>
        <p className="text-blue-100 text-sm mt-1">LeetCode Practice Assistant</p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 flex items-center justify-center px-3 py-3 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 bg-blue-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Icon className="w-4 h-4 mr-1" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {renderTabContent()}
      </div>
    </div>
  )
}

export default Sidebar
