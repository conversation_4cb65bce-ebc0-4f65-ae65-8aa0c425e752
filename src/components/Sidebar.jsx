import { useState } from 'react'
import { MessageCircle, FileText, Settings, Bo<PERSON> } from 'lucide-react'
import <PERSON><PERSON><PERSON><PERSON> from './InterviewerChat'
import ProblemPanel from './ProblemPanel'
import SettingsPanel from './SettingsPanel'

const Sidebar = ({ currentProblem, chatMessages, setChatMessages, onNewProblem }) => {
  const [activeTab, setActiveTab] = useState('chat')

  const tabs = [
    { id: 'chat', label: 'Chat', icon: MessageCircle },
    { id: 'problem', label: 'Problem', icon: FileText },
    { id: 'settings', label: 'Settings', icon: Settings },
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case 'chat':
        return (
          <InterviewerChat
            messages={chatMessages}
            setMessages={setChatMessages}
          />
        )
      case 'problem':
        return <ProblemPanel problem={currentProblem} />
      case 'settings':
        return <SettingsPanel onNewProblem={onNewProblem} />
      default:
        return null
    }
  }

  return (
    <div className="h-full flex flex-col bg-leetcode-dark border-r border-leetcode-border">
      {/* Header */}
      <div className="bg-leetcode-darker text-white p-4 border-b border-leetcode-border">
        <div className="flex items-center space-x-2">
          <Bot className="w-6 h-6 text-leetcode-green" />
          <h1 className="text-lg font-semibold text-white">AI Interviewer</h1>
        </div>
        <p className="text-leetcode-text-secondary text-sm mt-1">LeetCode Practice Assistant</p>
      </div>

      {/* Tabs */}
      <div className="border-b border-leetcode-border">
        <nav className="flex">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 flex items-center justify-center px-3 py-3 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-leetcode-green text-leetcode-green bg-leetcode-light'
                    : 'border-transparent text-leetcode-text-secondary hover:text-white hover:bg-leetcode-light'
                }`}
              >
                <Icon className="w-4 h-4 mr-1" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden bg-leetcode-dark">
        {renderTabContent()}
      </div>
    </div>
  )
}

export default Sidebar
