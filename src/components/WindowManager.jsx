import { useState, useCallback } from 'react'
import DockLayout from 'rc-dock'
import { MessageCircle, FileText, Settings, TestTube, BarChart3, Code2, Plus } from 'lucide-react'

// Import all our existing components
import Interviewer<PERSON><PERSON> from './InterviewerChat'
import ProblemPanel from './ProblemPanel'
import SettingsPanel from './SettingsPanel'
import TestCasePanel from './TestCasePanel'
import TestResultPanel from './TestResultPanel'
import CodeEditor from './CodeEditor'

const WINDOW_TYPES = {
  CHAT: 'chat',
  PROBLEM: 'problem', 
  SETTINGS: 'settings',
  TESTCASE: 'testcase',
  TESTRESULT: 'testresult',
  CODE_EDITOR: 'code_editor'
}

const WINDOW_CONFIG = {
  [WINDOW_TYPES.CHAT]: {
    title: 'AI Chat',
    icon: MessageCircle,
    component: InterviewerChat
  },
  [WINDOW_TYPES.PROBLEM]: {
    title: 'Problem',
    icon: FileText,
    component: ProblemPanel
  },
  [WINDOW_TYPES.SETTINGS]: {
    title: 'Settings',
    icon: Settings,
    component: SettingsPanel
  },
  [WINDOW_TYPES.TESTCASE]: {
    title: 'Test Cases',
    icon: TestTube,
    component: TestCasePanel
  },
  [WINDOW_TYPES.TESTRESULT]: {
    title: 'Test Results',
    icon: BarChart3,
    component: TestResultPanel
  },
  [WINDOW_TYPES.CODE_EDITOR]: {
    title: 'Code Editor',
    icon: Code2,
    component: CodeEditor
  }
}

const WindowManager = ({ 
  // All the props we need to pass to components
  currentProblem,
  chatMessages,
  setChatMessages,
  onNewProblem,
  problemPrompt,
  setProblemPrompt,
  interviewerPrompt,
  setInterviewerPrompt,
  problemConfig,
  setProblemConfig,
  interviewerConfig,
  setInterviewerConfig,
  onSwitchToChat,
  shouldSwitchToChat,
  testResults,
  isRunningTests,
  onRunTests,
  currentLanguage,
  onSubmitCode,
  isGeneratingProblem,
  code,
  setCode,
  selectedLanguage,
  onLanguageChange,
  showLeftPanel,
  showChatPanel,
  onToggleLeftPanel,
  onToggleChatPanel
}) => {
  // Initial layout: Code editor on the right, sidebar panels on the left
  const [currentNode, setCurrentNode] = useState({
    direction: 'row',
    first: {
      direction: 'column',
      first: WINDOW_TYPES.PROBLEM,
      second: {
        direction: 'row',
        first: WINDOW_TYPES.CHAT,
        second: WINDOW_TYPES.SETTINGS,
        splitPercentage: 50
      },
      splitPercentage: 60
    },
    second: WINDOW_TYPES.CODE_EDITOR,
    splitPercentage: 30
  })

  const onChange = useCallback((newNode) => {
    setCurrentNode(newNode)
  }, [])

  const createNewWindow = useCallback((windowType) => {
    // Add a new window to the layout
    setCurrentNode(prevNode => {
      if (!prevNode) {
        return windowType
      }

      // If we have a single window, create a split
      if (typeof prevNode === 'string') {
        return {
          direction: 'row',
          first: prevNode,
          second: windowType,
          splitPercentage: 50
        }
      }

      // If we have a split, add to the second position
      return {
        ...prevNode,
        second: {
          direction: 'column',
          first: prevNode.second,
          second: windowType,
          splitPercentage: 50
        }
      }
    })
  }, [])

  const renderWindow = useCallback((id, path) => {
    const config = WINDOW_CONFIG[id]
    if (!config) return <div>Unknown window type: {id}</div>

    const Component = config.component
    const Icon = config.icon

    // Props mapping for each component type
    const getComponentProps = () => {
      switch (id) {
        case WINDOW_TYPES.CHAT:
          return {
            messages: chatMessages,
            setMessages: setChatMessages,
            customPrompt: interviewerPrompt,
            interviewerConfig,
            onSubmitCode
          }
        case WINDOW_TYPES.PROBLEM:
          return {
            problem: currentProblem
          }
        case WINDOW_TYPES.SETTINGS:
          return {
            onNewProblem,
            problemPrompt,
            setProblemPrompt,
            interviewerPrompt,
            setInterviewerPrompt,
            problemConfig,
            setProblemConfig,
            interviewerConfig,
            setInterviewerConfig,
            currentLanguage,
            isGeneratingProblem
          }
        case WINDOW_TYPES.TESTCASE:
          return {
            problem: currentProblem,
            onRunTests
          }
        case WINDOW_TYPES.TESTRESULT:
          return {
            testResults,
            isRunning: isRunningTests,
            onRunTests,
            problem: currentProblem
          }
        case WINDOW_TYPES.CODE_EDITOR:
          return {
            code,
            setCode,
            language: selectedLanguage,
            onLanguageChange,
            showLeftPanel,
            showChatPanel,
            onToggleLeftPanel,
            onToggleChatPanel,
            onRunTests: () => onRunTests(null),
            isRunningTests
          }
        default:
          return {}
      }
    }

    return (
      <MosaicWindow
        path={path}
        createNode={() => WINDOW_TYPES.PROBLEM}
        title={
          <div className="flex items-center space-x-2">
            <Icon className="w-4 h-4" />
            <span>{config.title}</span>
          </div>
        }
        className="mosaic-window-leetcode"
      >
        <div className="h-full bg-leetcode-dark">
          <Component {...getComponentProps()} />
        </div>
      </MosaicWindow>
    )
  }, [
    chatMessages, setChatMessages, interviewerPrompt, interviewerConfig, onSubmitCode,
    currentProblem, onNewProblem, problemPrompt, setProblemPrompt, setInterviewerPrompt,
    problemConfig, setProblemConfig, setInterviewerConfig, currentLanguage, isGeneratingProblem,
    testResults, isRunningTests, onRunTests, code, setCode, selectedLanguage, onLanguageChange,
    showLeftPanel, showChatPanel, onToggleLeftPanel, onToggleChatPanel
  ])

  const Toolbar = () => (
    <div className="mosaic-toolbar bg-leetcode-darker border-b border-leetcode-border p-3 flex items-center space-x-3">
      <div className="flex items-center space-x-2">
        <Code2 className="w-5 h-5 text-leetcode-green" />
        <span className="text-white text-sm font-semibold">AI Interviewer</span>
      </div>
      <div className="w-px h-6 bg-leetcode-border"></div>
      <span className="text-leetcode-text-secondary text-sm font-medium">Add Window:</span>
      {Object.entries(WINDOW_CONFIG).map(([type, config]) => {
        const Icon = config.icon
        return (
          <button
            key={type}
            onClick={() => createNewWindow(type)}
            className="flex items-center space-x-1 px-3 py-1.5 bg-leetcode-light text-white rounded-md hover:bg-leetcode-green transition-all duration-200 text-xs font-medium hover:scale-105"
            title={`Add ${config.title} Window`}
          >
            <Icon className="w-3.5 h-3.5" />
            <span>{config.title}</span>
          </button>
        )
      })}
    </div>
  )

  return (
    <div className="h-screen bg-leetcode-dark flex flex-col">
      <Toolbar />
      <div className="flex-1">
        <Mosaic
          renderTile={renderWindow}
          value={currentNode}
          onChange={onChange}
          className="mosaic-leetcode-theme"
        />
      </div>
    </div>
  )
}

export default WindowManager
