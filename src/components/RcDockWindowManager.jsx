import { useState, useCallback, useRef } from 'react'
import DockLayout from 'rc-dock'
import { MessageCircle, FileText, Settings, TestTube, BarChart3, Code2, Plus } from 'lucide-react'

// Import all our existing components
import Interviewer<PERSON>hat from './InterviewerChat'
import ProblemPanel from './ProblemPanel'
import SettingsPanel from './SettingsPanel'
import TestCasePanel from './TestCasePanel'
import TestResultPanel from './TestResultPanel'
import CodeEditor from './CodeEditor'

const RcDockWindowManager = ({ 
  // All the props we need to pass to components
  currentProblem,
  chatMessages,
  setChatMessages,
  onNewProblem,
  problemPrompt,
  setProblemPrompt,
  interviewerPrompt,
  setInterviewerPrompt,
  problemConfig,
  setProblemConfig,
  interviewerConfig,
  setInterviewerConfig,
  testResults,
  isRunningTests,
  onRunTests,
  currentLanguage,
  onSubmitCode,
  isGeneratingProblem,
  code,
  setCode,
  selectedLanguage,
  onLanguageChange,
  showLeftPanel,
  showChatPanel,
  onToggleLeftPanel,
  onToggleChatPanel
}) => {
  const dockLayoutRef = useRef(null)

  // Component factory function
  const getComponent = useCallback((tabData) => {
    const { id } = tabData
    
    switch (id) {
      case 'problem':
        return <ProblemPanel problem={currentProblem} />
      
      case 'chat':
        return (
          <InterviewerChat
            messages={chatMessages}
            setMessages={setChatMessages}
            customPrompt={interviewerPrompt}
            interviewerConfig={interviewerConfig}
            onSubmitCode={onSubmitCode}
          />
        )
      
      case 'settings':
        return (
          <SettingsPanel
            onNewProblem={onNewProblem}
            problemPrompt={problemPrompt}
            setProblemPrompt={setProblemPrompt}
            interviewerPrompt={interviewerPrompt}
            setInterviewerPrompt={setInterviewerPrompt}
            problemConfig={problemConfig}
            setProblemConfig={setProblemConfig}
            interviewerConfig={interviewerConfig}
            setInterviewerConfig={setInterviewerConfig}
            currentLanguage={currentLanguage}
            isGeneratingProblem={isGeneratingProblem}
          />
        )
      
      case 'testcase':
        return (
          <TestCasePanel
            problem={currentProblem}
            onRunTests={onRunTests}
          />
        )
      
      case 'testresult':
        return (
          <TestResultPanel
            testResults={testResults}
            isRunning={isRunningTests}
            onRunTests={onRunTests}
            problem={currentProblem}
          />
        )
      
      case 'code-editor':
        return (
          <CodeEditor
            code={code}
            setCode={setCode}
            language={selectedLanguage}
            onLanguageChange={onLanguageChange}
            showLeftPanel={showLeftPanel}
            showChatPanel={showChatPanel}
            onToggleLeftPanel={onToggleLeftPanel}
            onToggleChatPanel={onToggleChatPanel}
            onRunTests={() => onRunTests(null)}
            isRunningTests={isRunningTests}
          />
        )
      
      default:
        return <div>Unknown component: {id}</div>
    }
  }, [
    currentProblem, chatMessages, setChatMessages, interviewerPrompt, interviewerConfig, onSubmitCode,
    onNewProblem, problemPrompt, setProblemPrompt, setInterviewerPrompt, problemConfig, setProblemConfig,
    setInterviewerConfig, currentLanguage, isGeneratingProblem, testResults, isRunningTests, onRunTests,
    code, setCode, selectedLanguage, onLanguageChange, showLeftPanel, showChatPanel, onToggleLeftPanel,
    onToggleChatPanel
  ])

  // Initial layout configuration
  const defaultLayout = {
    dockbox: {
      mode: 'horizontal',
      children: [
        {
          mode: 'vertical',
          size: 300,
          children: [
            {
              tabs: [
                {
                  id: 'problem',
                  title: 'Problem',
                  content: getComponent({ id: 'problem' }),
                  group: 'default',
                  closable: false
                }
              ]
            },
            {
              tabs: [
                {
                  id: 'chat',
                  title: 'AI Chat',
                  content: getComponent({ id: 'chat' }),
                  group: 'default'
                },
                {
                  id: 'settings',
                  title: 'Settings',
                  content: getComponent({ id: 'settings' }),
                  group: 'default'
                }
              ]
            }
          ]
        },
        {
          tabs: [
            {
              id: 'code-editor',
              title: 'Code Editor',
              content: getComponent({ id: 'code-editor' }),
              group: 'default',
              closable: false
            }
          ]
        }
      ]
    }
  }

  // Add new tab function
  const addNewTab = useCallback((tabType) => {
    const tabConfigs = {
      problem: { id: 'problem-' + Date.now(), title: 'Problem', content: getComponent({ id: 'problem' }) },
      chat: { id: 'chat-' + Date.now(), title: 'AI Chat', content: getComponent({ id: 'chat' }) },
      settings: { id: 'settings-' + Date.now(), title: 'Settings', content: getComponent({ id: 'settings' }) },
      testcase: { id: 'testcase-' + Date.now(), title: 'Test Cases', content: getComponent({ id: 'testcase' }) },
      testresult: { id: 'testresult-' + Date.now(), title: 'Test Results', content: getComponent({ id: 'testresult' }) },
      'code-editor': { id: 'code-editor-' + Date.now(), title: 'Code Editor', content: getComponent({ id: 'code-editor' }) }
    }

    const newTab = tabConfigs[tabType]
    if (newTab && dockLayoutRef.current) {
      // Add to the first available tabset
      dockLayoutRef.current.dockMove(newTab, null, 'middle')
    }
  }, [getComponent])

  // Toolbar component
  const Toolbar = () => (
    <div className="bg-leetcode-darker border-b border-leetcode-border p-3 flex items-center space-x-3">
      <div className="flex items-center space-x-2">
        <Code2 className="w-5 h-5 text-leetcode-green" />
        <span className="text-white text-sm font-semibold">AI Interviewer</span>
      </div>
      <div className="w-px h-6 bg-leetcode-border"></div>
      <span className="text-leetcode-text-secondary text-sm font-medium">Add Window:</span>
      
      <button
        onClick={() => addNewTab('problem')}
        className="flex items-center space-x-1 px-3 py-1.5 bg-leetcode-light text-white rounded-md hover:bg-leetcode-green transition-all duration-200 text-xs font-medium hover:scale-105"
        title="Add Problem Window"
      >
        <FileText className="w-3.5 h-3.5" />
        <span>Problem</span>
      </button>

      <button
        onClick={() => addNewTab('chat')}
        className="flex items-center space-x-1 px-3 py-1.5 bg-leetcode-light text-white rounded-md hover:bg-leetcode-green transition-all duration-200 text-xs font-medium hover:scale-105"
        title="Add Chat Window"
      >
        <MessageCircle className="w-3.5 h-3.5" />
        <span>Chat</span>
      </button>

      <button
        onClick={() => addNewTab('settings')}
        className="flex items-center space-x-1 px-3 py-1.5 bg-leetcode-light text-white rounded-md hover:bg-leetcode-green transition-all duration-200 text-xs font-medium hover:scale-105"
        title="Add Settings Window"
      >
        <Settings className="w-3.5 h-3.5" />
        <span>Settings</span>
      </button>

      <button
        onClick={() => addNewTab('testcase')}
        className="flex items-center space-x-1 px-3 py-1.5 bg-leetcode-light text-white rounded-md hover:bg-leetcode-green transition-all duration-200 text-xs font-medium hover:scale-105"
        title="Add Test Cases Window"
      >
        <TestTube className="w-3.5 h-3.5" />
        <span>Test Cases</span>
      </button>

      <button
        onClick={() => addNewTab('testresult')}
        className="flex items-center space-x-1 px-3 py-1.5 bg-leetcode-light text-white rounded-md hover:bg-leetcode-green transition-all duration-200 text-xs font-medium hover:scale-105"
        title="Add Test Results Window"
      >
        <BarChart3 className="w-3.5 h-3.5" />
        <span>Test Results</span>
      </button>

      <button
        onClick={() => addNewTab('code-editor')}
        className="flex items-center space-x-1 px-3 py-1.5 bg-leetcode-light text-white rounded-md hover:bg-leetcode-green transition-all duration-200 text-xs font-medium hover:scale-105"
        title="Add Code Editor Window"
      >
        <Code2 className="w-3.5 h-3.5" />
        <span>Code Editor</span>
      </button>
    </div>
  )

  return (
    <div className="h-screen bg-leetcode-dark flex flex-col">
      <Toolbar />
      <div className="flex-1">
        <DockLayout
          ref={dockLayoutRef}
          defaultLayout={defaultLayout}
          style={{
            position: 'absolute',
            left: 0,
            top: 0,
            right: 0,
            bottom: 0,
          }}
        />
      </div>
    </div>
  )
}

export default RcDockWindowManager
