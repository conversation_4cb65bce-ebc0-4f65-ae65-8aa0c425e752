import { useState, useRef, useEffect } from 'react'
import { Send, Bot, User, Code2 } from 'lucide-react'

const InterviewerChat = ({ messages, setMessages }) => {
  const [inputMessage, setInputMessage] = useState('')
  const messagesEndRef = useRef(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = (e) => {
    e.preventDefault()
    if (!inputMessage.trim()) return

    const newMessage = {
      id: Date.now(),
      sender: 'user',
      message: inputMessage,
      timestamp: new Date()
    }

    // Simulate AI response
    const aiResponse = {
      id: Date.now() + 1,
      sender: 'ai',
      message: `I understand your question: "${inputMessage}". Let me help you with that. Can you tell me more about what specific part you're struggling with?`,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, newMessage, aiResponse])
    setInputMessage('')
  }

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  return (
    <div className="h-full flex flex-col">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg px-3 py-2 ${
                message.sender === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-800'
              }`}
            >
              {/* Message Header */}
              <div className="flex items-center space-x-2 mb-1">
                {message.sender === 'ai' ? (
                  <Bot className="w-4 h-4" />
                ) : (
                  <User className="w-4 h-4" />
                )}
                <span className="text-xs opacity-75">
                  {message.sender === 'ai' ? 'AI Interviewer' : 'You'}
                </span>
                {message.timestamp && (
                  <span className="text-xs opacity-50">
                    {formatTime(message.timestamp)}
                  </span>
                )}
              </div>

              {/* Message Content */}
              <div className="text-sm">
                {message.message}
              </div>

              {/* Code Preview (if message contains code) */}
              {message.code && (
                <div className="mt-2 p-2 bg-black bg-opacity-10 rounded text-xs">
                  <div className="flex items-center space-x-1 mb-1">
                    <Code2 className="w-3 h-3" />
                    <span className="font-medium">{message.language}</span>
                  </div>
                  <pre className="whitespace-pre-wrap font-mono">
                    {message.code.substring(0, 100)}
                    {message.code.length > 100 && '...'}
                  </pre>
                </div>
              )}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="border-t border-gray-200 p-4">
        <form onSubmit={handleSendMessage} className="flex space-x-2">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            placeholder="Ask a question or share your thoughts..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            type="submit"
            disabled={!inputMessage.trim()}
            className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="w-4 h-4" />
          </button>
        </form>
      </div>
    </div>
  )
}

export default InterviewerChat
