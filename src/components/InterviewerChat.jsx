import { useState, useRef, useEffect } from 'react'
import { Send, Bot, User, Code2 } from 'lucide-react'

const InterviewerChat = ({ messages, setMessages }) => {
  const [inputMessage, setInputMessage] = useState('')
  const messagesEndRef = useRef(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = (e) => {
    e.preventDefault()
    if (!inputMessage.trim()) return

    const newMessage = {
      id: Date.now(),
      sender: 'user',
      message: inputMessage,
      timestamp: new Date()
    }

    // Simulate AI response
    const aiResponse = {
      id: Date.now() + 1,
      sender: 'ai',
      message: `I understand your question: "${inputMessage}". Let me help you with that. Can you tell me more about what specific part you're struggling with?`,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, newMessage, aiResponse])
    setInputMessage('')
  }

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  return (
    <div className="h-full flex flex-col bg-leetcode-dark">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg px-3 py-2 ${
                message.sender === 'user'
                  ? 'bg-leetcode-green text-white'
                  : 'bg-leetcode-light text-white border border-leetcode-border'
              }`}
            >
              {/* Message Header */}
              <div className="flex items-center space-x-2 mb-1">
                {message.sender === 'ai' ? (
                  <Bot className="w-4 h-4" />
                ) : (
                  <User className="w-4 h-4" />
                )}
                <span className="text-xs opacity-75">
                  {message.sender === 'ai' ? 'AI Interviewer' : 'You'}
                </span>
                {message.timestamp && (
                  <span className="text-xs opacity-50">
                    {formatTime(message.timestamp)}
                  </span>
                )}
              </div>

              {/* Message Content */}
              <div className="text-sm">
                {message.message}
              </div>

              {/* Code Preview (if message contains code) */}
              {message.code && (
                <div className="mt-2 p-2 bg-leetcode-darker rounded text-xs border border-leetcode-border">
                  <div className="flex items-center space-x-1 mb-1">
                    <Code2 className="w-3 h-3 text-leetcode-green" />
                    <span className="font-medium text-leetcode-green">{message.language}</span>
                  </div>
                  <pre className="whitespace-pre-wrap font-mono text-leetcode-text-secondary">
                    {message.code.substring(0, 100)}
                    {message.code.length > 100 && '...'}
                  </pre>
                </div>
              )}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="border-t border-leetcode-border p-4">
        <form onSubmit={handleSendMessage} className="flex space-x-2">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            placeholder="Ask a question or share your thoughts..."
            className="flex-1 px-3 py-2 bg-leetcode-light border border-leetcode-border rounded-md text-sm text-white placeholder-leetcode-text-secondary focus:outline-none focus:ring-2 focus:ring-leetcode-green"
          />
          <button
            type="submit"
            disabled={!inputMessage.trim()}
            className="px-3 py-2 bg-leetcode-green text-white rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-leetcode-green disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="w-4 h-4" />
          </button>
        </form>
      </div>
    </div>
  )
}

export default InterviewerChat
