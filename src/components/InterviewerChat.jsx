import { useState, useRef, useEffect } from 'react'
import { Send, Bot, User, Code2, Loader2, Upload } from 'lucide-react'
import aiService from '../services/aiService'
import TypingIndicator from './ui/TypingIndicator'

const InterviewerChat = ({ messages, setMessages, customPrompt = '', interviewerConfig, onSubmitCode }) => {
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async (e) => {
    e.preventDefault()
    if (!inputMessage.trim() || isLoading) return

    const userMessage = {
      id: Date.now(),
      sender: 'user',
      message: inputMessage,
      timestamp: new Date()
    }

    // Add user message immediately
    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsLoading(true)

    try {
      // Get AI response
      const aiResponseText = await aiService.sendChatMessage(
        inputMessage,
        messages,
        customPrompt,
        interviewerConfig
      )

      const aiResponse = {
        id: Date.now() + 1,
        sender: 'ai',
        message: aiResponseText,
        timestamp: new Date()
      }

      setMessages(prev => [...prev, aiResponse])
    } catch (error) {
      console.error('Error getting AI response:', error)

      // Show error toast
      window.toast?.error(
        'Chat Error',
        'Failed to get AI response. Please check your connection and try again.'
      )

      const errorResponse = {
        id: Date.now() + 1,
        sender: 'ai',
        message: "I'm having trouble connecting right now. Could you rephrase your question?",
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorResponse])
    } finally {
      setIsLoading(false)
    }
  }

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  const handleKeyDown = (e) => {
    // Handle Cmd+Enter (Mac) or Ctrl+Enter (Windows/Linux)
    if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
      e.preventDefault()
      e.stopPropagation()
      handleSendMessage(e)
    }
  }

  return (
    <div className="h-full flex flex-col bg-leetcode-dark">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <Bot className="w-12 h-12 text-leetcode-text-secondary mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">Ready to Start!</h3>
            <p className="text-leetcode-text-secondary text-sm max-w-md">
              Generate a problem from the Settings tab, then come back here to chat with your AI interviewer about your solution approach and get feedback.
            </p>
          </div>
        ) : (
          messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg px-3 py-2 ${
                message.sender === 'user'
                  ? 'bg-leetcode-green text-white'
                  : 'bg-leetcode-light text-white border border-leetcode-border'
              }`}
            >
              {/* Message Header */}
              <div className="flex items-center space-x-2 mb-1">
                {message.sender === 'ai' ? (
                  <Bot className="w-4 h-4" />
                ) : (
                  <User className="w-4 h-4" />
                )}
                <span className="text-xs opacity-75">
                  {message.sender === 'ai' ? 'AI Interviewer' : 'You'}
                </span>
                {message.timestamp && (
                  <span className="text-xs opacity-50">
                    {formatTime(message.timestamp)}
                  </span>
                )}
              </div>

              {/* Message Content */}
              <div className="text-sm">
                {message.message}
              </div>

              {/* Code Preview (if message contains code) */}
              {message.code && (
                <div className="mt-2 p-2 bg-leetcode-darker rounded text-xs border border-leetcode-border">
                  <div className="flex items-center space-x-1 mb-1">
                    <Code2 className="w-3 h-3 text-leetcode-green" />
                    <span className="font-medium text-leetcode-green">{message.language}</span>
                    {message.codeChanged === false && (
                      <span className="text-leetcode-orange text-xs ml-2">
                        (unchanged)
                      </span>
                    )}
                    {message.codeChanged === true && (
                      <span className="text-leetcode-green text-xs ml-2">
                        (updated)
                      </span>
                    )}
                  </div>
                  <pre className="whitespace-pre-wrap font-mono text-leetcode-text-secondary">
                    {message.code.substring(0, 100)}
                    {message.code.length > 100 && '...'}
                  </pre>
                </div>
              )}
            </div>
          </div>
          ))
        )}

        {/* Typing Indicator */}
        {isLoading && <TypingIndicator />}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="border-t border-leetcode-border p-4">
        <form onSubmit={handleSendMessage} className="flex space-x-2">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={isLoading ? "AI is thinking..." : "Ask a question or share your thoughts... (Cmd+Enter to send)"}
            disabled={isLoading}
            className="flex-1 px-3 py-2 bg-leetcode-light border border-leetcode-border rounded-md text-sm text-white placeholder-leetcode-text-secondary focus:outline-none focus:ring-2 focus:ring-leetcode-green disabled:opacity-50"
          />

          {/* Submit Code Button */}
          {onSubmitCode && (
            <button
              type="button"
              onClick={() => {
                onSubmitCode(inputMessage)
                setInputMessage('') // Clear input after submitting code
              }}
              disabled={isLoading}
              className="px-3 py-2 bg-leetcode-orange text-white rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-leetcode-orange disabled:opacity-50 disabled:cursor-not-allowed"
              title="Submit code for review"
            >
              <Upload className="w-4 h-4" />
            </button>
          )}

          {/* Send Message Button */}
          <button
            type="submit"
            disabled={!inputMessage.trim() || isLoading}
            className="px-3 py-2 bg-leetcode-green text-white rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-leetcode-green disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </button>
        </form>
      </div>
    </div>
  )
}

export default InterviewerChat
