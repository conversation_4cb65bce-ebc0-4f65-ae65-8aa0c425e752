import { useState } from 'react'
import { Send, MessageSquare } from 'lucide-react'

const ChatSubmitPanel = ({ onSubmit }) => {
  const [message, setMessage] = useState('')

  const handleSubmit = (e) => {
    e.preventDefault()
    onSubmit(message)
    setMessage('')
  }

  return (
    <div className="h-full flex flex-col bg-leetcode-dark">
      {/* Header */}
      <div className="bg-leetcode-darker border-b border-leetcode-border px-4 py-2 flex items-center">
        <MessageSquare className="w-4 h-4 text-leetcode-green mr-2" />
        <h3 className="text-sm font-medium text-white">Submit Code & Thoughts</h3>
      </div>

      {/* Content */}
      <div className="flex-1 p-4">
        <form onSubmit={handleSubmit} className="h-full flex flex-col">
          {/* Message Input */}
          <div className="flex-1 mb-3">
            <label htmlFor="thoughts" className="block text-sm font-medium text-leetcode-text-secondary mb-2">
              Share your thoughts, approach, or questions:
            </label>
            <textarea
              id="thoughts"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Describe your current approach, any challenges you're facing, or questions you have..."
              className="w-full h-full resize-none bg-leetcode-light border border-leetcode-border rounded-md px-3 py-2 text-sm text-white placeholder-leetcode-text-secondary focus:outline-none focus:ring-2 focus:ring-leetcode-green focus:border-transparent"
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              className="inline-flex items-center px-4 py-2 bg-leetcode-green text-white text-sm font-medium rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-leetcode-green focus:ring-offset-2 focus:ring-offset-leetcode-dark transition-colors"
            >
              <Send className="w-4 h-4 mr-2" />
              Send Code & Thoughts
            </button>
          </div>
        </form>
      </div>

      {/* Info Text */}
      <div className="px-4 pb-2">
        <p className="text-xs text-leetcode-text-secondary">
          This will send your current code along with your thoughts to the AI interviewer
        </p>
      </div>
    </div>
  )
}

export default ChatSubmitPanel
