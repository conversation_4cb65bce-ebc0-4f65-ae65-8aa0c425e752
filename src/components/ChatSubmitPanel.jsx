import { useState } from 'react'
import { Send, MessageSquare } from 'lucide-react'

const ChatSubmitPanel = ({ onSubmit }) => {
  const [message, setMessage] = useState('')

  const handleSubmit = (e) => {
    e.preventDefault()
    onSubmit(message)
    setMessage('')
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-gray-100 border-b border-gray-200 px-4 py-2 flex items-center">
        <MessageSquare className="w-4 h-4 text-gray-600 mr-2" />
        <h3 className="text-sm font-medium text-gray-800">Submit Code & Thoughts</h3>
      </div>

      {/* Content */}
      <div className="flex-1 p-4">
        <form onSubmit={handleSubmit} className="h-full flex flex-col">
          {/* Message Input */}
          <div className="flex-1 mb-3">
            <label htmlFor="thoughts" className="block text-sm font-medium text-gray-700 mb-2">
              Share your thoughts, approach, or questions:
            </label>
            <textarea
              id="thoughts"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Describe your current approach, any challenges you're facing, or questions you have..."
              className="w-full h-full resize-none border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            >
              <Send className="w-4 h-4 mr-2" />
              Send Code & Thoughts
            </button>
          </div>
        </form>
      </div>

      {/* Info Text */}
      <div className="px-4 pb-2">
        <p className="text-xs text-gray-500">
          This will send your current code along with your thoughts to the AI interviewer
        </p>
      </div>
    </div>
  )
}

export default ChatSubmitPanel
