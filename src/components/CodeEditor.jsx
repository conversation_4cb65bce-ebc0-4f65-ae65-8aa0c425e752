import { Editor } from '@monaco-editor/react'
import { Code, Play } from 'lucide-react'

const CodeEditor = ({ code, setCode, language, onLanguageChange }) => {
  const handleEditorChange = (value) => {
    setCode(value || '')
  }

  const getMonacoLanguage = (lang) => {
    return lang === 'javascript' ? 'javascript' : 'python'
  }

  return (
    <div className="h-full flex flex-col bg-leetcode-dark">
      {/* Header */}
      <div className="bg-leetcode-darker border-b border-leetcode-border px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Code className="w-5 h-5 text-leetcode-green" />
          <h2 className="text-lg font-semibold text-white">Code</h2>
        </div>

        {/* Language Selector */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-leetcode-text-secondary">Language:</span>
          <select
            value={language}
            onChange={(e) => onLanguageChange(e.target.value)}
            className="px-3 py-1 bg-leetcode-light border border-leetcode-border rounded-md text-sm text-white focus:outline-none focus:ring-2 focus:ring-leetcode-green"
          >
            <option value="javascript">JavaScript</option>
            <option value="python">Python</option>
          </select>
        </div>
      </div>

      {/* Monaco Editor */}
      <div className="flex-1">
        <Editor
          height="100%"
          language={getMonacoLanguage(language)}
          value={code}
          onChange={handleEditorChange}
          theme="vs-dark"
          options={{
            minimap: { enabled: false },
            fontSize: 14,
            lineNumbers: 'on',
            roundedSelection: false,
            scrollBeyondLastLine: false,
            automaticLayout: true,
            tabSize: 2,
            insertSpaces: true,
            wordWrap: 'on',
            folding: true,
            lineDecorationsWidth: 10,
            lineNumbersMinChars: 3,
            glyphMargin: false,
            fontFamily: 'Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace',
            scrollbar: {
              vertical: 'auto',
              horizontal: 'auto',
              verticalScrollbarSize: 12,
              horizontalScrollbarSize: 12,
            },
          }}
        />
      </div>
    </div>
  )
}

export default CodeEditor
