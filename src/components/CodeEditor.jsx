import { Editor } from '@monaco-editor/react'
import { Code, Play, PanelLeft, PanelBottom, PanelLeftClose, PanelBottomClose } from 'lucide-react'

const CodeEditor = ({ code, setCode, language, onLanguageChange, onRunTests, isRunningTests }) => {
  const handleEditorChange = (value) => {
    setCode(value || '')
  }

  const getMonacoLanguage = (lang) => {
    return lang === 'javascript' ? 'javascript' : 'python'
  }

  return (
    <div className="h-full flex flex-col bg-leetcode-dark">
      {/* Editor Controls */}
      <div className="border-b border-leetcode-border p-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Language Selector */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-leetcode-text-secondary">Language:</span>
              <select
                value={language}
                onChange={(e) => onLanguageChange(e.target.value)}
                className="bg-leetcode-light border border-leetcode-border rounded px-2 py-1 text-sm text-white focus:outline-none focus:ring-2 focus:ring-leetcode-green"
              >
                <option value="javascript">JavaScript</option>
                <option value="python">Python</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Monaco Editor with Run Button */}
      <div className="flex-1 relative">
        <Editor
          height="100%"
          language={getMonacoLanguage(language)}
          value={code}
          onChange={handleEditorChange}
          theme="vs-dark"
          options={{
            minimap: { enabled: false },
            fontSize: 14,
            lineNumbers: 'on',
            roundedSelection: false,
            scrollBeyondLastLine: false,
            automaticLayout: true,
            tabSize: 2,
            insertSpaces: true,
            wordWrap: 'on',
            folding: true,
            lineDecorationsWidth: 10,
            lineNumbersMinChars: 3,
            glyphMargin: false,
            fontFamily: 'Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace',
            scrollbar: {
              vertical: 'auto',
              horizontal: 'auto',
              verticalScrollbarSize: 12,
              horizontalScrollbarSize: 12,
            },
          }}
        />

        {/* Floating Run Button */}
        <button
          onClick={() => {
            console.log('🎯 Run button clicked!')
            console.log('🔧 onRunTests function:', typeof onRunTests)
            console.log('🔧 isRunningTests:', isRunningTests)
            console.log('📝 Current code length:', code?.length || 0)
            if (onRunTests) {
              onRunTests()
            } else {
              console.error('❌ onRunTests function is not provided!')
            }
          }}
          disabled={isRunningTests}
          className="absolute bottom-4 right-4 px-4 py-2 bg-leetcode-green text-white rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-leetcode-green font-medium flex items-center space-x-2 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
          title="Run tests (Ctrl+Enter)"
        >
          {isRunningTests ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span>Running...</span>
            </>
          ) : (
            <>
              <Play className="w-4 h-4" />
              <span>Run</span>
            </>
          )}
        </button>
      </div>

      {/* Bottom Status Bar (like LeetCode) */}
      <div className="bg-leetcode-darker border-t border-leetcode-border px-4 py-2 flex items-center justify-between text-xs">
        <div className="flex items-center space-x-4">
          <span className="text-leetcode-green">Saved</span>
          <span className="text-leetcode-text-secondary">
            Continue to work on your code from {new Date().toLocaleDateString()}, {new Date().toLocaleTimeString()}
          </span>
        </div>
        <div className="flex items-center space-x-4 text-leetcode-text-secondary">
          <span>Ln {code.split('\n').length}, Col 1</span>
        </div>
      </div>
    </div>
  )
}

export default CodeEditor
