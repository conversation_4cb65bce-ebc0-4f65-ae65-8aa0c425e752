import { Editor } from '@monaco-editor/react'
import { Code, Play } from 'lucide-react'

const CodeEditor = ({ code, setCode, language, onLanguageChange }) => {
  const handleEditorChange = (value) => {
    setCode(value || '')
  }

  const getMonacoLanguage = (lang) => {
    return lang === 'javascript' ? 'javascript' : 'python'
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-gray-50 border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Code className="w-5 h-5 text-gray-600" />
          <h2 className="text-lg font-semibold text-gray-800">Code Editor</h2>
        </div>
        
        {/* Language Selector */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">Language:</span>
          <select
            value={language}
            onChange={(e) => onLanguageChange(e.target.value)}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="javascript">JavaScript</option>
            <option value="python">Python</option>
          </select>
        </div>
      </div>

      {/* Monaco Editor */}
      <div className="flex-1">
        <Editor
          height="100%"
          language={getMonacoLanguage(language)}
          value={code}
          onChange={handleEditorChange}
          theme="vs-light"
          options={{
            minimap: { enabled: false },
            fontSize: 14,
            lineNumbers: 'on',
            roundedSelection: false,
            scrollBeyondLastLine: false,
            automaticLayout: true,
            tabSize: 2,
            insertSpaces: true,
            wordWrap: 'on',
            folding: true,
            lineDecorationsWidth: 10,
            lineNumbersMinChars: 3,
            glyphMargin: false,
            scrollbar: {
              vertical: 'auto',
              horizontal: 'auto',
              verticalScrollbarSize: 12,
              horizontalScrollbarSize: 12,
            },
          }}
        />
      </div>
    </div>
  )
}

export default CodeEditor
