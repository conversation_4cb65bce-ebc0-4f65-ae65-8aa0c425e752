import { useState, useEffect } from 'react'
import WindowManager from './components/WindowManager'
import ToastContainer from './components/ui/ToastContainer'
import { getRandomProblem } from './data/problems'
import aiService from './services/aiService'
import codeExecutor from './services/codeExecutor'
import {
  DEFAULT_PROBLEM_CONFIG,
  DEFAULT_INTERVIEWER_CONFIG,
  DEFAULT_PROBLEM_PROMPT,
  DEFAULT_INTERVIEWER_PROMPT
} from './config/prompts'

function App() {
  const [currentProblem, setCurrentProblem] = useState(null)
  const [selectedLanguage, setSelectedLanguage] = useState('javascript')
  const [code, setCode] = useState('')
  const [lastSubmittedCode, setLastSubmittedCode] = useState('')
  const [shouldSwitchToChat, setShouldSwitchToChat] = useState(false)
  const [showLeftPanel, setShowLeftPanel] = useState(true)
  const [showChatPanel, setShowChatPanel] = useState(true)
  const [testResults, setTestResults] = useState(null)
  const [isRunningTests, setIsRunningTests] = useState(false)
  const [isGeneratingProblem, setIsGeneratingProblem] = useState(false)

  // Separate prompt configurations
  const [problemPrompt, setProblemPrompt] = useState(DEFAULT_PROBLEM_PROMPT)
  const [interviewerPrompt, setInterviewerPrompt] = useState(DEFAULT_INTERVIEWER_PROMPT)
  const [problemConfig, setProblemConfig] = useState(DEFAULT_PROBLEM_CONFIG)
  const [interviewerConfig, setInterviewerConfig] = useState(DEFAULT_INTERVIEWER_CONFIG)
  const [chatMessages, setChatMessages] = useState([])

  // Set initial placeholder comment
  useEffect(() => {
    if (!currentProblem) {
      const placeholders = {
        javascript: '// Generate a problem from the Settings tab to start coding!',
        python: '# Generate a problem from the Settings tab to start coding!',
        java: '// Generate a problem from the Settings tab to start coding!',
        cpp: '// Generate a problem from the Settings tab to start coding!',
        c: '// Generate a problem from the Settings tab to start coding!'
      }
      setCode(placeholders[selectedLanguage] || '// Generate a problem from the Settings tab to start coding!')
    }
  }, [selectedLanguage, currentProblem])

  // Keyboard shortcuts for panel toggles
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Cmd/Ctrl + B to toggle left panel
      if ((e.metaKey || e.ctrlKey) && e.key === 'b') {
        e.preventDefault()
        toggleLeftPanel()
      }
      // Cmd/Ctrl + J to toggle chat panel
      if ((e.metaKey || e.ctrlKey) && e.key === 'j') {
        e.preventDefault()
        toggleChatPanel()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  const handleLanguageChange = (language) => {
    setSelectedLanguage(language)
    if (currentProblem) {
      setCode(currentProblem.starterCode[language])
    } else {
      // Set placeholder comment for the selected language
      const placeholders = {
        javascript: '// Generate a problem from the Settings tab to start coding!',
        python: '# Generate a problem from the Settings tab to start coding!',
        java: '// Generate a problem from the Settings tab to start coding!',
        cpp: '// Generate a problem from the Settings tab to start coding!',
        c: '// Generate a problem from the Settings tab to start coding!'
      }
      setCode(placeholders[language] || '// Generate a problem from the Settings tab to start coding!')
    }
  }

  const handleNewProblem = async (categories, language) => {
    console.log('🎯 Generating new problem for categories:', categories, 'language:', language)
    console.log('🔍 Problem prompt:', problemPrompt)
    console.log('🔍 Problem prompt length:', problemPrompt?.length || 0)

    // Check if we should use AI generation or fallback to random
    const shouldUseAI = problemPrompt && problemPrompt.trim().length > 0
    console.log('🤖 Should use AI:', shouldUseAI)

    setIsGeneratingProblem(true)
    let newProblem

    try {
      if (shouldUseAI) {
        console.log('🤖 Using AI to generate problem...')
        const targetLanguage = language || selectedLanguage
        newProblem = await aiService.generateProblem(categories, problemPrompt, problemConfig, targetLanguage)

        // Show success toast
        window.toast?.success(
          'Problem Generated!',
          'AI has created a new problem for you.'
        )
      } else {
        console.log('📚 Using predefined problem from library')
        newProblem = getRandomProblem(categories)
      }

      console.log('✅ Generated problem:', newProblem)
      console.log('🔍 Problem structure:', newProblem ? Object.keys(newProblem) : 'null')
      console.log('🔍 Problem test cases:', newProblem?.testCases)

      setCurrentProblem(newProblem)
      setCode(newProblem.starterCode[selectedLanguage] || '')
      setLastSubmittedCode('') // Reset code tracking for new problem
      setTestResults(null)

      setChatMessages([
        {
          id: Date.now(),
          sender: 'ai',
          message: `Great! I've given you a new problem: "${newProblem.title}". Take a look and let me know when you're ready to start!`
        }
      ])

      // Switch to problem tab to show the new problem
      setShouldSwitchToChat('problem')

    } catch (error) {
      console.error('❌ Problem generation failed:', error)

      // Show error toast
      window.toast?.error(
        'Problem Generation Failed',
        'Failed to generate a new problem. Please try again or check your API configuration.'
      )

      // Fallback to random problem
      newProblem = getRandomProblem(categories)
      setCurrentProblem(newProblem)
      setCode(newProblem.starterCode[selectedLanguage] || '')
      setLastSubmittedCode('')
      setTestResults(null)
    } finally {
      setIsGeneratingProblem(false)
    }
  }

  const handleSubmitCode = async (userMessage) => {
    // Check if code has changed since last submission
    const codeChanged = code !== lastSubmittedCode
    const trimmedCode = code.trim()
    const trimmedLastCode = lastSubmittedCode.trim()
    const hasSignificantChange = trimmedCode !== trimmedLastCode

    console.log('🔍 Code submission check:')
    console.log('Code changed:', codeChanged)
    console.log('Significant change:', hasSignificantChange)
    console.log('Current code length:', trimmedCode.length)
    console.log('Last submitted length:', trimmedLastCode.length)

    // Add user message with code
    const newUserMessage = {
      id: Date.now(),
      sender: 'user',
      message: userMessage || 'Here\'s my current code:',
      code: code,
      language: selectedLanguage,
      timestamp: new Date(),
      codeChanged: hasSignificantChange
    }

    setChatMessages(prev => [...prev, newUserMessage])

    try {
      let aiResponseText

      if (hasSignificantChange) {
        console.log('🚀 Code changed - sending to OpenAI for analysis')
        // Get AI analysis of the code since it changed
        aiResponseText = await aiService.analyzeCodeSubmission(
          code,
          selectedLanguage,
          userMessage,
          currentProblem,
          chatMessages,
          interviewerPrompt
        )
        // Update last submitted code
        setLastSubmittedCode(code)
      } else {
        console.log('💭 Code unchanged - using lightweight response')
        // Code hasn't changed, provide a lightweight response
        if (userMessage) {
          // If user provided thoughts, respond to those without re-analyzing code
          aiResponseText = await aiService.sendChatMessage(
            `User thoughts about their current code: "${userMessage}". The code hasn't changed since last submission, so please respond to their thoughts without re-analyzing the code.`,
            chatMessages,
            interviewerPrompt,
            interviewerConfig
          )
        } else {
          // No thoughts and no code change - suggest what to do next
          aiResponseText = "I see you're submitting the same code as before. Are you looking for clarification on something specific, or would you like to try a different approach? Feel free to share your thoughts or ask questions!"
        }
      }

      const aiResponse = {
        id: Date.now() + 1,
        sender: 'ai',
        message: aiResponseText,
        timestamp: new Date()
      }

      setChatMessages(prev => [...prev, aiResponse])
    } catch (error) {
      console.error('Error analyzing code:', error)

      // Show error toast
      window.toast?.error(
        'Code Analysis Failed',
        'Failed to analyze your code. Please check your API configuration and try again.'
      )

      const errorResponse = {
        id: Date.now() + 1,
        sender: 'ai',
        message: `I can see your ${selectedLanguage} solution. ${userMessage ? 'Thanks for sharing your thoughts! ' : ''}Let me analyze your code and provide some feedback. What's your current thinking about the time and space complexity?`,
        timestamp: new Date()
      }
      setChatMessages(prev => [...prev, errorResponse])
    }
  }



  const handleSwitchToChat = () => {
    // Reset the switch trigger
    setShouldSwitchToChat(false)
  }

  const toggleLeftPanel = () => {
    setShowLeftPanel(prev => !prev)
  }

  const toggleChatPanel = () => {
    setShowChatPanel(prev => !prev)
  }

  const handleRunTests = async (testCases) => {
    console.log('🧪 Running tests with cases:', testCases)
    console.log('📝 Current code:', code)
    console.log('🔤 Selected language:', selectedLanguage)
    console.log('🎯 Current problem:', currentProblem?.title)
    setIsRunningTests(true)
    setTestResults(null)

    // If no test cases provided, use default ones from the problem
    console.log('🔍 Debug test cases:')
    console.log('  - testCases parameter:', testCases)
    console.log('  - currentProblem.testCases:', currentProblem?.testCases)
    console.log('  - currentProblem keys:', currentProblem ? Object.keys(currentProblem) : 'no problem')

    const casesToRun = testCases || currentProblem?.testCases || [
      {
        inputs: { input: 'test' },
        expectedOutput: 'output'
      }
    ]

    console.log('🧪 Final test cases to run:', casesToRun)

    try {
      const results = await codeExecutor.executeCode(code, selectedLanguage, casesToRun)
      setTestResults(results)

      // Auto-switch to test results tab if tests were run
      // You could add logic here to switch to the test results tab
      console.log('✅ Test results:', results)
    } catch (error) {
      console.error('❌ Error running tests:', error)
      setTestResults({
        passed: 0,
        failed: casesToRun?.length || 0,
        total: casesToRun?.length || 0,
        results: [],
        executionTime: null,
        error: error.message
      })
    } finally {
      setIsRunningTests(false)
    }
  }

  return (
    <div className="h-screen bg-leetcode-dark text-leetcode-text">
      <ToastContainer />
      <WindowManager
        currentProblem={currentProblem}
        chatMessages={chatMessages}
        setChatMessages={setChatMessages}
        onNewProblem={handleNewProblem}
        problemPrompt={problemPrompt}
        setProblemPrompt={setProblemPrompt}
        interviewerPrompt={interviewerPrompt}
        setInterviewerPrompt={setInterviewerPrompt}
        problemConfig={problemConfig}
        setProblemConfig={setProblemConfig}
        interviewerConfig={interviewerConfig}
        setInterviewerConfig={setInterviewerConfig}
        onSwitchToChat={handleSwitchToChat}
        shouldSwitchToChat={shouldSwitchToChat}
        testResults={testResults}
        isRunningTests={isRunningTests}
        onRunTests={handleRunTests}
        currentLanguage={selectedLanguage}
        onSubmitCode={handleSubmitCode}
        isGeneratingProblem={isGeneratingProblem}
        code={code}
        setCode={setCode}
        selectedLanguage={selectedLanguage}
        onLanguageChange={handleLanguageChange}
        showLeftPanel={showLeftPanel}
        showChatPanel={showChatPanel}
        onToggleLeftPanel={toggleLeftPanel}
        onToggleChatPanel={toggleChatPanel}
      />
    </div>
  )
}

export default App
