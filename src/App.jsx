import { useState } from 'react'
import Sidebar from './components/Sidebar'
import CodeEditor from './components/CodeEditor'
import ChatSubmitPanel from './components/ChatSubmitPanel'
import { getRandomProblem } from './data/problems'

function App() {
  const [currentProblem, setCurrentProblem] = useState(() => getRandomProblem(['Array']))
  const [selectedLanguage, setSelectedLanguage] = useState('javascript')
  const [code, setCode] = useState(currentProblem.starterCode[selectedLanguage])
  const [chatMessages, setChatMessages] = useState([
    {
      id: 1,
      sender: 'ai',
      message: `Hello! I'm your AI interviewer. I've given you the problem "${currentProblem.title}". Take your time to understand it, and feel free to ask any clarifying questions. When you're ready, start coding and share your thought process with me!`
    }
  ])

  const handleLanguageChange = (language) => {
    setSelectedLanguage(language)
    setCode(currentProblem.starterCode[language])
  }

  const handleNewProblem = (categories) => {
    const newProblem = getRandomProblem(categories)
    setCurrentProblem(newProblem)
    setCode(newProblem.starterCode[selectedLanguage])
    setChatMessages([
      {
        id: Date.now(),
        sender: 'ai',
        message: `Great! I've given you a new problem: "${newProblem.title}". Take a look and let me know when you're ready to start!`
      }
    ])
  }

  const handleSubmitCode = (userMessage) => {
    // Add user message
    const newUserMessage = {
      id: Date.now(),
      sender: 'user',
      message: userMessage,
      code: code,
      language: selectedLanguage
    }

    // Simulate AI response
    const aiResponse = {
      id: Date.now() + 1,
      sender: 'ai',
      message: `I can see your current approach. ${userMessage ? 'Thanks for sharing your thoughts! ' : ''}Let me analyze your code and provide some feedback. What's your current thinking about the time and space complexity?`
    }

    setChatMessages(prev => [...prev, newUserMessage, aiResponse])
  }

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Left Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        <Sidebar
          currentProblem={currentProblem}
          chatMessages={chatMessages}
          setChatMessages={setChatMessages}
          onNewProblem={handleNewProblem}
        />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Code Editor */}
        <div className="flex-1 bg-white">
          <CodeEditor
            code={code}
            setCode={setCode}
            language={selectedLanguage}
            onLanguageChange={handleLanguageChange}
          />
        </div>

        {/* Chat and Submit Panel */}
        <div className="h-64 bg-gray-50 border-t border-gray-200">
          <ChatSubmitPanel
            onSubmit={handleSubmitCode}
          />
        </div>
      </div>
    </div>
  )
}

export default App
