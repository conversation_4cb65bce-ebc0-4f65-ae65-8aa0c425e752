import { useState } from 'react'
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels'
import Sidebar from './components/Sidebar'
import CodeEditor from './components/CodeEditor'
import ChatSubmitPanel from './components/ChatSubmitPanel'
import { getRandomProblem } from './data/problems'

function App() {
  const [currentProblem, setCurrentProblem] = useState(() => getRandomProblem(['Array']))
  const [selectedLanguage, setSelectedLanguage] = useState('javascript')
  const [code, setCode] = useState(currentProblem.starterCode[selectedLanguage])
  const [chatMessages, setChatMessages] = useState([
    {
      id: 1,
      sender: 'ai',
      message: `Hello! I'm your AI interviewer. I've given you the problem "${currentProblem.title}". Take your time to understand it, and feel free to ask any clarifying questions. When you're ready, start coding and share your thought process with me!`
    }
  ])

  const handleLanguageChange = (language) => {
    setSelectedLanguage(language)
    setCode(currentProblem.starterCode[language])
  }

  const handleNewProblem = (categories) => {
    const newProblem = getRandomProblem(categories)
    setCurrentProblem(newProblem)
    setCode(newProblem.starterCode[selectedLanguage])
    setChatMessages([
      {
        id: Date.now(),
        sender: 'ai',
        message: `Great! I've given you a new problem: "${newProblem.title}". Take a look and let me know when you're ready to start!`
      }
    ])
  }

  const handleSubmitCode = (userMessage) => {
    // Add user message
    const newUserMessage = {
      id: Date.now(),
      sender: 'user',
      message: userMessage,
      code: code,
      language: selectedLanguage
    }

    // Simulate AI response
    const aiResponse = {
      id: Date.now() + 1,
      sender: 'ai',
      message: `I can see your current approach. ${userMessage ? 'Thanks for sharing your thoughts! ' : ''}Let me analyze your code and provide some feedback. What's your current thinking about the time and space complexity?`
    }

    setChatMessages(prev => [...prev, newUserMessage, aiResponse])
  }

  return (
    <div className="h-screen bg-leetcode-dark text-white">
      <PanelGroup direction="horizontal">
        {/* Left Sidebar */}
        <Panel defaultSize={25} minSize={20} maxSize={40}>
          <Sidebar
            currentProblem={currentProblem}
            chatMessages={chatMessages}
            setChatMessages={setChatMessages}
            onNewProblem={handleNewProblem}
          />
        </Panel>

        {/* Vertical Resize Handle */}
        <PanelResizeHandle className="w-1 bg-leetcode-border hover:bg-leetcode-accent transition-colors" />

        {/* Main Content Area */}
        <Panel defaultSize={75} minSize={50}>
          <PanelGroup direction="vertical">
            {/* Code Editor */}
            <Panel defaultSize={70} minSize={40}>
              <CodeEditor
                code={code}
                setCode={setCode}
                language={selectedLanguage}
                onLanguageChange={handleLanguageChange}
              />
            </Panel>

            {/* Horizontal Resize Handle */}
            <PanelResizeHandle className="h-1 bg-leetcode-border hover:bg-leetcode-accent transition-colors" />

            {/* Chat and Submit Panel */}
            <Panel defaultSize={30} minSize={20} maxSize={50}>
              <ChatSubmitPanel
                onSubmit={handleSubmitCode}
              />
            </Panel>
          </PanelGroup>
        </Panel>
      </PanelGroup>
    </div>
  )
}

export default App
