import OpenAI from 'openai'

class AIService {
  constructor() {
    this.openai = null
    this.isConfigured = false
    this.lastRequestTime = 0
    this.minRequestInterval = 2000 // 2 seconds between requests
    this.initializeOpenAI()
  }

  initializeOpenAI() {
    const apiKey = import.meta.env.VITE_OPENAI_API_KEY

    console.log('🔍 Checking OpenAI configuration...')
    console.log('API Key present:', !!apiKey)
    console.log('API Key starts with sk-:', apiKey?.startsWith('sk-'))

    if (!apiKey || apiKey === 'your_openai_api_key_here') {
      console.warn('⚠️ OpenAI API key not configured. Using mock responses.')
      console.log('Expected format: VITE_OPENAI_API_KEY=sk-...')
      return
    }

    try {
      this.openai = new OpenAI({
        apiKey: apiKey,
        dangerouslyAllowBrowser: true // Note: In production, use a backend proxy
      })
      this.isConfigured = true
      console.log('✅ OpenAI service initialized successfully!')
      console.log('🤖 AI responses will be powered by:', import.meta.env.VITE_AI_MODEL || 'gpt-3.5-turbo')
    } catch (error) {
      console.error('❌ Failed to initialize OpenAI:', error)
    }
  }

  getSystemPrompt(customPrompt = '') {
    const defaultPrompt = `You are an experienced technical interviewer conducting a coding interview. Your role is to:

1. Be encouraging but challenging
2. Ask follow-up questions about time/space complexity, edge cases, and optimizations
3. Provide hints when the candidate is stuck, but don't give away the solution immediately
4. Analyze code for correctness, efficiency, and style
5. Guide the candidate through their thought process
6. Ask clarifying questions about their approach

Keep responses concise but helpful. Focus on the interview experience.`

    return customPrompt || defaultPrompt
  }

  async sendChatMessage(message, conversationHistory = [], customPrompt = '', config = {}) {
    console.log('💬 Sending chat message:', message)
    console.log('🔧 AI Service configured:', this.isConfigured)

    if (!this.isConfigured) {
      console.log('🎭 Using mock response (AI not configured)')
      return this.getMockResponse(message)
    }

    // Rate limiting
    const now = Date.now()
    const timeSinceLastRequest = now - this.lastRequestTime
    if (timeSinceLastRequest < this.minRequestInterval) {
      const waitTime = this.minRequestInterval - timeSinceLastRequest
      console.log(`⏱️ Rate limiting: waiting ${waitTime}ms before next request`)
      await new Promise(resolve => setTimeout(resolve, waitTime))
    }

    console.log('🚀 Sending request to OpenAI...')
    this.lastRequestTime = Date.now()

    try {
      const messages = [
        { role: 'system', content: this.getSystemPrompt(customPrompt) },
        ...conversationHistory.map(msg => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.message
        })),
        { role: 'user', content: message }
      ]

      const response = await this.openai.chat.completions.create({
        model: config.model || import.meta.env.VITE_AI_MODEL || 'gpt-3.5-turbo',
        messages: messages,
        max_tokens: config.maxTokens || parseInt(import.meta.env.VITE_AI_MAX_TOKENS) || 1000,
        temperature: config.temperature !== undefined ? config.temperature : parseFloat(import.meta.env.VITE_AI_TEMPERATURE) || 0.7,
      })

      console.log('✅ Received OpenAI response!')
      return response.choices[0].message.content
    } catch (error) {
      console.error('OpenAI API error:', error)

      // Handle specific error types
      if (error.status === 429) {
        console.warn('⚠️ Rate limit exceeded. Using mock response.')
        return this.getMockResponse(message, '⚠️ I\'m being rate limited by OpenAI. Here\'s a helpful response anyway: ')
      } else if (error.status === 401) {
        console.error('❌ Invalid API key')
        return this.getMockResponse(message, '❌ API key issue. Using mock response: ')
      } else if (error.status === 403) {
        console.error('❌ Access denied to model')
        return this.getMockResponse(message, '❌ Model access denied. Using mock response: ')
      } else {
        console.error('❌ Unknown API error:', error.message)
        return this.getMockResponse(message, '❌ Connection issue. Using mock response: ')
      }
    }
  }

  async analyzeCodeSubmission(code, language, userThoughts, problem, conversationHistory = [], customPrompt = '') {
    if (!this.isConfigured) {
      return this.getMockCodeAnalysis(code, language, userThoughts)
    }

    // Rate limiting
    const now = Date.now()
    const timeSinceLastRequest = now - this.lastRequestTime
    if (timeSinceLastRequest < this.minRequestInterval) {
      const waitTime = this.minRequestInterval - timeSinceLastRequest
      console.log(`⏱️ Rate limiting code analysis: waiting ${waitTime}ms`)
      await new Promise(resolve => setTimeout(resolve, waitTime))
    }
    this.lastRequestTime = Date.now()

    try {
      const analysisPrompt = `Analyze this code submission for the problem "${problem.title}":

**Code (${language}):**
\`\`\`${language}
${code}
\`\`\`

**User's thoughts:** ${userThoughts || 'No additional thoughts provided.'}

**Problem:** ${problem.title} (${problem.difficulty})

Please provide:
1. Code correctness assessment
2. Time and space complexity analysis
3. Suggestions for improvement
4. Follow-up questions to test understanding
5. Hints for optimization if needed

Keep the response conversational and interview-focused.`

      const messages = [
        { role: 'system', content: this.getSystemPrompt(customPrompt) },
        ...conversationHistory.slice(-6).map(msg => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.message
        })),
        { role: 'user', content: analysisPrompt }
      ]

      const response = await this.openai.chat.completions.create({
        model: import.meta.env.VITE_AI_MODEL || 'gpt-3.5-turbo',
        messages: messages,
        max_tokens: parseInt(import.meta.env.VITE_AI_MAX_TOKENS) || 1000,
        temperature: parseFloat(import.meta.env.VITE_AI_TEMPERATURE) || 0.7,
      })

      return response.choices[0].message.content
    } catch (error) {
      console.error('OpenAI API error:', error)
      return this.getMockCodeAnalysis(code, language, userThoughts, 'I encountered an error analyzing your code, but here\'s some general feedback: ')
    }
  }

  getMockResponse(message, prefix = '') {
    const responses = [
      "That's a great question! Can you walk me through your current approach?",
      "I see what you're thinking. What do you think the time complexity of that approach would be?",
      "Interesting approach! Have you considered any edge cases?",
      "Good thinking! What if the input was much larger - would your solution still work efficiently?",
      "That's on the right track. Can you think of a way to optimize this further?",
    ]
    
    return prefix + responses[Math.floor(Math.random() * responses.length)]
  }

  getMockCodeAnalysis(code, language, userThoughts, prefix = '') {
    return prefix + `I can see your ${language} solution. ${userThoughts ? 'Thanks for sharing your thoughts! ' : ''}

Looking at your code:
- The logic appears to be on the right track
- Consider the time complexity - can you tell me what you think it is?
- Are there any edge cases you might have missed?
- What would happen if the input was empty or very large?

What's your next step for optimizing this solution?`
  }

  async generateProblem(categories = [], problemPrompt = '', config = {}, language = 'javascript') {
    console.log('🧩 Generating new problem for categories:', categories, 'language:', language)
    console.log('🔧 AI Service configured:', this.isConfigured)
    console.log('🔑 API Key present:', !!import.meta.env.VITE_OPENAI_API_KEY)
    console.log('📝 Problem prompt length:', problemPrompt.length)

    if (!this.isConfigured) {
      console.log('🎭 Using mock problem generation (AI not configured)')
      console.log('💡 To use real AI generation, set VITE_OPENAI_API_KEY in your .env file')
      return this.getMockProblem(categories)
    }

    // Rate limiting
    const now = Date.now()
    const timeSinceLastRequest = now - this.lastRequestTime
    if (timeSinceLastRequest < this.minRequestInterval) {
      const waitTime = this.minRequestInterval - timeSinceLastRequest
      console.log(`⏱️ Rate limiting problem generation: waiting ${waitTime}ms`)
      await new Promise(resolve => setTimeout(resolve, waitTime))
    }
    this.lastRequestTime = Date.now()

    try {
      // Replace placeholders with actual values
      const categoriesText = categories.length > 0 ? categories.join(', ') : 'Array, String'
      const languageText = language === 'javascript' ? 'JavaScript' : 'Python'

      let prompt = problemPrompt
        .replace(/\{CATEGORIES\}/g, categoriesText)
        .replace(/\{LANGUAGE\}/g, languageText)
        .replace(/\{\{CATEGORIES\}\}/g, categoriesText)
        .replace(/\{\{LANGUAGE\}\}/g, languageText)

      const messages = [
        { role: 'system', content: 'You are an expert at creating LeetCode-style algorithmic problems.' },
        { role: 'user', content: prompt }
      ]

      const response = await this.openai.chat.completions.create({
        model: config.model || import.meta.env.VITE_AI_MODEL || 'gpt-3.5-turbo',
        messages: messages,
        max_tokens: config.maxTokens || parseInt(import.meta.env.VITE_AI_MAX_TOKENS) || 1500,
        temperature: config.temperature !== undefined ? config.temperature : parseFloat(import.meta.env.VITE_AI_TEMPERATURE) || 0.7,
      })

      console.log('✅ Generated new problem!')
      return this.parseProblemResponse(response.choices[0].message.content, categories)
    } catch (error) {
      console.error('OpenAI API error:', error)
      return this.getMockProblem(categories, 'Generated with fallback: ')
    }
  }

  getMockProblem(categories = [], prefix = '') {
    const category = categories[0] || 'Array'
    return {
      id: Date.now(),
      title: `${prefix}Mock ${category} Problem`,
      difficulty: 'Medium',
      description: `This is a mock ${category.toLowerCase()} problem generated when AI is not available.

Example:
Input: [1, 2, 3]
Output: [3, 2, 1]

Constraints:
• 1 <= array.length <= 1000
• -1000 <= array[i] <= 1000`,
      starterCode: {
        javascript: `/**
 * @param {number[]} nums
 * @return {number[]}
 */
var solution = function(nums) {
    // Your code here

};`,
        python: `class Solution:
    def solution(self, nums: List[int]) -> List[int]:
        # Your code here
        pass`
      },
      testCases: [
        {
          inputs: { nums: '[1,2,3]' },
          expectedOutput: '[3,2,1]'
        }
      ]
    }
  }

  parseProblemResponse(response, categories) {
    try {
      // Try to parse as JSON first
      const jsonResponse = JSON.parse(response)

      if (jsonResponse.description && jsonResponse.initial_code && jsonResponse.test_cases) {
        // Handle the new JSON format
        return this.parseJsonProblemFormat(jsonResponse)
      }
    } catch (error) {
      console.log('Response is not JSON, treating as plain text')
    }

    // Fallback to simple parsing for non-JSON responses
    return {
      id: Date.now(),
      title: 'AI Generated Problem',
      difficulty: 'Medium',
      description: response,
      starterCode: {
        javascript: `/**
 * @param {any} input
 * @return {any}
 */
var solution = function(input) {
    // Your code here

};`,
        python: `class Solution:
    def solution(self, input):
        # Your code here
        pass`
      },
      testCases: [
        {
          inputs: { input: 'test' },
          expectedOutput: 'output'
        }
      ]
    }
  }

  parseJsonProblemFormat(jsonResponse) {
    const { description, initial_code, test_cases } = jsonResponse

    // Extract function name from initial code for title
    const functionMatch = initial_code.match(/def\s+(\w+)\s*\(|function\s+(\w+)\s*\(|var\s+(\w+)\s*=/)
    const functionName = functionMatch ? (functionMatch[1] || functionMatch[2] || functionMatch[3]) : 'Generated Problem'

    // Convert test cases to the expected format
    const convertedTestCases = test_cases.map(tc => {
      const testCase = tc.test_case
      return {
        inputs: { input: testCase.input },
        expectedOutput: testCase.expected_output,
        description: testCase.description
      }
    })

    // Generate starter code for both languages based on the initial code
    const starterCode = this.generateStarterCode(initial_code, functionName)

    return {
      id: Date.now(),
      title: this.formatFunctionName(functionName),
      difficulty: 'Medium',
      description: description,
      starterCode: starterCode,
      testCases: convertedTestCases
    }
  }

  generateStarterCode(initialCode, functionName) {
    // Detect if it's Python or JavaScript based on syntax
    const isPython = initialCode.includes('def ') || initialCode.includes('    #')

    if (isPython) {
      return {
        javascript: this.convertPythonToJavaScript(initialCode, functionName),
        python: initialCode
      }
    } else {
      return {
        javascript: initialCode,
        python: this.convertJavaScriptToPython(initialCode, functionName)
      }
    }
  }

  convertPythonToJavaScript(pythonCode, functionName) {
    // Basic conversion from Python to JavaScript
    const jsCode = `/**
 * @param {any} arr
 * @return {any}
 */
var ${functionName} = function(arr) {
    // Your code here

};`
    return jsCode
  }

  convertJavaScriptToPython(jsCode, functionName) {
    // Basic conversion from JavaScript to Python
    const pythonCode = `def ${functionName}(arr):
    # Your code here
    pass`
    return pythonCode
  }

  formatFunctionName(functionName) {
    // Convert snake_case to Title Case
    return functionName
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  async generateProblemHint(problem, userCode, conversationHistory = []) {
    if (!this.isConfigured) {
      return `Here's a hint for ${problem.title}: Think about the most efficient data structure for this problem. What would give you the best time complexity?`
    }

    try {
      const hintPrompt = `The user is working on "${problem.title}" and seems stuck. Based on their current code and our conversation, provide a helpful hint that guides them toward the solution without giving it away completely.

Current code:
\`\`\`
${userCode}
\`\`\`

Provide a single, focused hint that helps them make progress.`

      const messages = [
        { role: 'system', content: this.getSystemPrompt() },
        ...conversationHistory.slice(-4).map(msg => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.message
        })),
        { role: 'user', content: hintPrompt }
      ]

      const response = await this.openai.chat.completions.create({
        model: import.meta.env.VITE_AI_MODEL || 'gpt-3.5-turbo',
        messages: messages,
        max_tokens: 200,
        temperature: 0.7,
      })

      return response.choices[0].message.content
    } catch (error) {
      console.error('OpenAI API error:', error)
      return `Here's a hint for ${problem.title}: Think about the most efficient data structure for this problem. What would give you the best time complexity?`
    }
  }
}

export default new AIService()
