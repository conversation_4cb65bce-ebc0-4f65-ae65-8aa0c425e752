import OpenAI from 'openai'

class AIService {
  constructor() {
    this.openai = null
    this.isConfigured = false
    this.initializeOpenAI()
  }

  initializeOpenAI() {
    const apiKey = import.meta.env.VITE_OPENAI_API_KEY
    
    if (!apiKey || apiKey === 'your_openai_api_key_here') {
      console.warn('OpenAI API key not configured. Using mock responses.')
      return
    }

    try {
      this.openai = new OpenAI({
        apiKey: apiKey,
        dangerouslyAllowBrowser: true // Note: In production, use a backend proxy
      })
      this.isConfigured = true
      console.log('OpenAI service initialized successfully')
    } catch (error) {
      console.error('Failed to initialize OpenAI:', error)
    }
  }

  getSystemPrompt(customPrompt = '') {
    const defaultPrompt = `You are an experienced technical interviewer conducting a coding interview. Your role is to:

1. Be encouraging but challenging
2. Ask follow-up questions about time/space complexity, edge cases, and optimizations
3. Provide hints when the candidate is stuck, but don't give away the solution immediately
4. Analyze code for correctness, efficiency, and style
5. Guide the candidate through their thought process
6. Ask clarifying questions about their approach

Keep responses concise but helpful. Focus on the interview experience.`

    return customPrompt || defaultPrompt
  }

  async sendChatMessage(message, conversationHistory = [], customPrompt = '') {
    if (!this.isConfigured) {
      return this.getMockResponse(message)
    }

    try {
      const messages = [
        { role: 'system', content: this.getSystemPrompt(customPrompt) },
        ...conversationHistory.map(msg => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.message
        })),
        { role: 'user', content: message }
      ]

      const response = await this.openai.chat.completions.create({
        model: import.meta.env.VITE_AI_MODEL || 'gpt-4',
        messages: messages,
        max_tokens: parseInt(import.meta.env.VITE_AI_MAX_TOKENS) || 1000,
        temperature: parseFloat(import.meta.env.VITE_AI_TEMPERATURE) || 0.7,
      })

      return response.choices[0].message.content
    } catch (error) {
      console.error('OpenAI API error:', error)
      return this.getMockResponse(message, 'Sorry, I encountered an error. Let me help you anyway: ')
    }
  }

  async analyzeCodeSubmission(code, language, userThoughts, problem, conversationHistory = [], customPrompt = '') {
    if (!this.isConfigured) {
      return this.getMockCodeAnalysis(code, language, userThoughts)
    }

    try {
      const analysisPrompt = `Analyze this code submission for the problem "${problem.title}":

**Code (${language}):**
\`\`\`${language}
${code}
\`\`\`

**User's thoughts:** ${userThoughts || 'No additional thoughts provided.'}

**Problem:** ${problem.title} (${problem.difficulty})

Please provide:
1. Code correctness assessment
2. Time and space complexity analysis
3. Suggestions for improvement
4. Follow-up questions to test understanding
5. Hints for optimization if needed

Keep the response conversational and interview-focused.`

      const messages = [
        { role: 'system', content: this.getSystemPrompt(customPrompt) },
        ...conversationHistory.slice(-6).map(msg => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.message
        })),
        { role: 'user', content: analysisPrompt }
      ]

      const response = await this.openai.chat.completions.create({
        model: import.meta.env.VITE_AI_MODEL || 'gpt-4',
        messages: messages,
        max_tokens: parseInt(import.meta.env.VITE_AI_MAX_TOKENS) || 1000,
        temperature: parseFloat(import.meta.env.VITE_AI_TEMPERATURE) || 0.7,
      })

      return response.choices[0].message.content
    } catch (error) {
      console.error('OpenAI API error:', error)
      return this.getMockCodeAnalysis(code, language, userThoughts, 'I encountered an error analyzing your code, but here\'s some general feedback: ')
    }
  }

  getMockResponse(message, prefix = '') {
    const responses = [
      "That's a great question! Can you walk me through your current approach?",
      "I see what you're thinking. What do you think the time complexity of that approach would be?",
      "Interesting approach! Have you considered any edge cases?",
      "Good thinking! What if the input was much larger - would your solution still work efficiently?",
      "That's on the right track. Can you think of a way to optimize this further?",
    ]
    
    return prefix + responses[Math.floor(Math.random() * responses.length)]
  }

  getMockCodeAnalysis(code, language, userThoughts, prefix = '') {
    return prefix + `I can see your ${language} solution. ${userThoughts ? 'Thanks for sharing your thoughts! ' : ''}

Looking at your code:
- The logic appears to be on the right track
- Consider the time complexity - can you tell me what you think it is?
- Are there any edge cases you might have missed?
- What would happen if the input was empty or very large?

What's your next step for optimizing this solution?`
  }

  async generateProblemHint(problem, userCode, conversationHistory = []) {
    if (!this.isConfigured) {
      return `Here's a hint for ${problem.title}: Think about the most efficient data structure for this problem. What would give you the best time complexity?`
    }

    try {
      const hintPrompt = `The user is working on "${problem.title}" and seems stuck. Based on their current code and our conversation, provide a helpful hint that guides them toward the solution without giving it away completely.

Current code:
\`\`\`
${userCode}
\`\`\`

Provide a single, focused hint that helps them make progress.`

      const messages = [
        { role: 'system', content: this.getSystemPrompt() },
        ...conversationHistory.slice(-4).map(msg => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.message
        })),
        { role: 'user', content: hintPrompt }
      ]

      const response = await this.openai.chat.completions.create({
        model: import.meta.env.VITE_AI_MODEL || 'gpt-4',
        messages: messages,
        max_tokens: 200,
        temperature: 0.7,
      })

      return response.choices[0].message.content
    } catch (error) {
      console.error('OpenAI API error:', error)
      return `Here's a hint for ${problem.title}: Think about the most efficient data structure for this problem. What would give you the best time complexity?`
    }
  }
}

export default new AIService()
