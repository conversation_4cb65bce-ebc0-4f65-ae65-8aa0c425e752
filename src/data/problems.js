export const leetcodeCategories = [
  'Array',
  'String',
  'Hash Table',
  'Dynamic Programming',
  'Math',
  'Sorting',
  'Greedy',
  'Depth-First Search',
  'Binary Search',
  'Database',
  'Breadth-First Search',
  'Tree',
  'Matrix',
  'Two Pointers',
  'Binary Tree',
  'Bit Manipulation',
  'Stack',
  'Design',
  'Heap (Priority Queue)',
  'Graph',
  'Simulation',
  'Backtracking',
  'Counting',
  'Sliding Window',
  'Union Find',
  'Linked List',
  'Ordered Set',
  'Monotonic Stack',
  'Enumeration',
  'Recursion'
];

export const sampleProblems = {
  'Array': [
    {
      id: 1,
      title: "Two Sum",
      difficulty: "Easy",
      description: `Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.

You may assume that each input would have exactly one solution, and you may not use the same element twice.

You can return the answer in any order.

Example 1:
Input: nums = [2,7,11,15], target = 9
Output: [0,1]
Explanation: Because nums[0] + nums[1] == 9, we return [0, 1].

Example 2:
Input: nums = [3,2,4], target = 6
Output: [1,2]

Example 3:
Input: nums = [3,3], target = 6
Output: [0,1]

Constraints:
• 2 <= nums.length <= 10^4
• -10^9 <= nums[i] <= 10^9
• -10^9 <= target <= 10^9
• Only one valid answer exists.`,
      starterCode: {
        javascript: `/**
 * @param {number[]} nums
 * @param {number} target
 * @return {number[]}
 */
var twoSum = function(nums, target) {
    
};`,
        python: `class Solution:
    def twoSum(self, nums: List[int], target: int) -> List[int]:
        `
      }
    }
  ],
  'String': [
    {
      id: 2,
      title: "Valid Parentheses",
      difficulty: "Easy",
      description: `Given a string s containing just the characters '(', ')', '{', '}', '[' and ']', determine if the input string is valid.

An input string is valid if:
1. Open brackets must be closed by the same type of brackets.
2. Open brackets must be closed in the correct order.
3. Every close bracket has a corresponding open bracket of the same type.

Example 1:
Input: s = "()"
Output: true

Example 2:
Input: s = "()[]{}"
Output: true

Example 3:
Input: s = "(]"
Output: false

Constraints:
• 1 <= s.length <= 10^4
• s consists of parentheses only '()[]{}'.`,
      starterCode: {
        javascript: `/**
 * @param {string} s
 * @return {boolean}
 */
var isValid = function(s) {
    
};`,
        python: `class Solution:
    def isValid(self, s: str) -> bool:
        `
      }
    }
  ]
};

export const getRandomProblem = (categories) => {
  const availableCategories = categories.length > 0 ? categories : Object.keys(sampleProblems);
  const randomCategory = availableCategories[Math.floor(Math.random() * availableCategories.length)];
  const problems = sampleProblems[randomCategory];
  
  if (!problems || problems.length === 0) {
    return sampleProblems['Array'][0]; // fallback
  }
  
  return problems[Math.floor(Math.random() * problems.length)];
};
